// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Typography rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-typography ant-typography-rtl css-var-root"
/>
`;

exports[`Typography rtl render component should be rendered correctly in RTL direction 2`] = `
<article
  class="ant-typography ant-typography-rtl css-var-root"
/>
`;

exports[`Typography rtl render component should be rendered correctly in RTL direction 3`] = `
<h1
  class="ant-typography ant-typography-rtl css-var-root"
/>
`;

exports[`Typography rtl render component should be rendered correctly in RTL direction 4`] = `
<a
  class="ant-typography ant-typography-rtl css-var-root"
/>
`;
