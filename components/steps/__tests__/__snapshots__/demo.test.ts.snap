// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/steps/demo/clickable.tsx correctly 1`] = `
Array [
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            1
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 1
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 2
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 3
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-filled css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            1
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 1
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 2
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 3
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/steps/demo/component-token.tsx correctly 1`] = `
Array [
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Left 00:00:08"
            >
              Left 00:00:08
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Left 00:00:08"
            >
              Left 00:00:08
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish ant-steps-item-custom ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="user"
            class="anticon anticon-user"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="user"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Login
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-finish"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-finish ant-steps-item-custom"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="solution"
            class="anticon anticon-solution"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="solution"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M688 264c0-4.4-3.6-8-8-8H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48zm-8 136H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM480 544H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-48 308H208V148h560v344c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h264c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm356.8-74.4c29-26.3 47.2-64.3 47.2-106.6 0-79.5-64.5-144-144-144s-144 64.5-144 144c0 42.3 18.2 80.3 47.2 106.6-57 32.5-96.2 92.7-99.2 162.1-.2 4.5 3.5 8.3 8 8.3h48.1c4.2 0 7.7-3.3 8-7.6C564 871.2 621.7 816 692 816s128 55.2 131.9 124.4c.2 4.2 3.7 7.6 8 7.6H880c4.6 0 8.2-3.8 8-8.3-2.9-69.5-42.2-129.6-99.2-162.1zM692 591c44.2 0 80 35.8 80 80s-35.8 80-80 80-80-35.8-80-80 35.8-80 80-80z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Verification
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-custom"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="loading"
            class="anticon anticon-loading anticon-spin"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="loading"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Pay
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait ant-steps-item-custom"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="smile"
            class="anticon anticon-smile"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="smile"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Done
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-dot css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-navigation css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 1
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="00:00:05"
            >
              00:00:05
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 2
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="00:01:02"
            >
              00:01:02
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 3
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="waiting for longlong time"
            >
              waiting for longlong time
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/steps/demo/customized-progress-dot.tsx correctly 1`] = `
<div
  class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-dot css-var-test-id"
  style="--steps-items-offset:0"
>
  <div
    class="ant-steps-item ant-steps-item-finish"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        aria-describedby="test-id"
        class="ant-steps-item-icon ant-wave-target"
      />
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Finished
          </div>
          <div
            class="ant-steps-item-rail ant-steps-item-rail-process"
          />
        </div>
        <div
          class="ant-steps-item-content"
        >
          You can hover on the dot.
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-process ant-steps-item-active"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        aria-describedby="test-id"
        class="ant-steps-item-icon ant-wave-target"
      />
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            In Progress
          </div>
          <div
            class="ant-steps-item-rail ant-steps-item-rail-wait"
          />
        </div>
        <div
          class="ant-steps-item-content"
        >
          You can hover on the dot.
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-wait"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        aria-describedby="test-id"
        class="ant-steps-item-icon ant-wave-target"
      />
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Waiting
          </div>
          <div
            class="ant-steps-item-rail ant-steps-item-rail-wait"
          />
        </div>
        <div
          class="ant-steps-item-content"
        >
          You can hover on the dot.
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-wait"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        aria-describedby="test-id"
        class="ant-steps-item-icon ant-wave-target"
      />
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Waiting
          </div>
        </div>
        <div
          class="ant-steps-item-content"
        >
          You can hover on the dot.
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/error.tsx correctly 1`] = `
<div
  class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled css-var-test-id"
  style="--steps-items-offset:0"
>
  <div
    class="ant-steps-item ant-steps-item-finish"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        class="ant-steps-item-icon ant-wave-target"
      >
        <span
          aria-label="check"
          class="anticon anticon-check ant-steps-item-icon-finish"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="check"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Finished
          </div>
          <div
            class="ant-steps-item-rail ant-steps-item-rail-error"
          />
        </div>
        <div
          class="ant-steps-item-content"
        >
          This is a content
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-error ant-steps-item-active"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        class="ant-steps-item-icon ant-wave-target"
      >
        <span
          aria-label="close"
          class="anticon anticon-close ant-steps-item-icon-error"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            In Process
          </div>
          <div
            class="ant-steps-item-rail ant-steps-item-rail-wait"
          />
        </div>
        <div
          class="ant-steps-item-content"
        >
          This is a content
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-wait"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        class="ant-steps-item-icon ant-wave-target"
      >
        <span
          class="ant-steps-item-icon-number"
        >
          3
        </span>
      </div>
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Waiting
          </div>
        </div>
        <div
          class="ant-steps-item-content"
        >
          This is a content
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/icon.tsx correctly 1`] = `
<div
  class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled css-var-test-id"
  style="--steps-items-offset:0"
>
  <div
    class="ant-steps-item ant-steps-item-finish ant-steps-item-custom ant-steps-item-active"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        class="ant-steps-item-icon ant-wave-target"
      >
        <span
          aria-label="user"
          class="anticon anticon-user"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="user"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Login
          </div>
          <div
            class="ant-steps-item-rail ant-steps-item-rail-finish"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-finish ant-steps-item-custom"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        class="ant-steps-item-icon ant-wave-target"
      >
        <span
          aria-label="solution"
          class="anticon anticon-solution"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="solution"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M688 264c0-4.4-3.6-8-8-8H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48zm-8 136H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM480 544H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-48 308H208V148h560v344c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h264c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm356.8-74.4c29-26.3 47.2-64.3 47.2-106.6 0-79.5-64.5-144-144-144s-144 64.5-144 144c0 42.3 18.2 80.3 47.2 106.6-57 32.5-96.2 92.7-99.2 162.1-.2 4.5 3.5 8.3 8 8.3h48.1c4.2 0 7.7-3.3 8-7.6C564 871.2 621.7 816 692 816s128 55.2 131.9 124.4c.2 4.2 3.7 7.6 8 7.6H880c4.6 0 8.2-3.8 8-8.3-2.9-69.5-42.2-129.6-99.2-162.1zM692 591c44.2 0 80 35.8 80 80s-35.8 80-80 80-80-35.8-80-80 35.8-80 80-80z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Verification
          </div>
          <div
            class="ant-steps-item-rail ant-steps-item-rail-process"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-process ant-steps-item-custom"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        class="ant-steps-item-icon ant-wave-target"
      >
        <span
          aria-label="loading"
          class="anticon anticon-loading anticon-spin"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="loading"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="0 0 1024 1024"
            width="1em"
          >
            <path
              d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Pay
          </div>
          <div
            class="ant-steps-item-rail ant-steps-item-rail-wait"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-wait ant-steps-item-custom"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        class="ant-steps-item-icon ant-wave-target"
      >
        <span
          aria-label="smile"
          class="anticon anticon-smile"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="smile"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Done
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/inline.tsx correctly 1`] = `
<div
  class="ant-list ant-list-split css-var-test-id"
>
  <div
    class="ant-spin-nested-loading css-var-test-id"
  >
    <div
      class="ant-spin-container"
    >
      <ul
        class="ant-list-items"
      >
        <li
          class="ant-list-item"
        >
          <div
            class="ant-list-item-meta"
          >
            <div
              class="ant-list-item-meta-avatar"
            >
              <span
                class="ant-avatar ant-avatar-circle ant-avatar-image css-var-test-id ant-avatar-css-var"
              >
                <img
                  src="https://api.dicebear.com/7.x/miniavs/svg?seed=0"
                />
              </span>
            </div>
            <div
              class="ant-list-item-meta-content"
            >
              <h4
                class="ant-list-item-meta-title"
              >
                <a
                  href="https://ant.design"
                >
                  Ant Design Title 1
                </a>
              </h4>
              <div
                class="ant-list-item-meta-description"
              >
                Ant Design, a design language for background applications, is refined by Ant UED Team
              </div>
            </div>
          </div>
          <div
            class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-inline ant-steps-dot css-var-test-id"
            style="--steps-items-offset:0;margin-top:8px"
          >
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-process ant-steps-item-active"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 1
                    </div>
                    <div
                      class="ant-steps-item-rail ant-steps-item-rail-wait"
                    />
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 1
                  </div>
                </div>
              </div>
            </div>
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-wait"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 2
                    </div>
                    <div
                      class="ant-steps-item-rail ant-steps-item-rail-wait"
                    />
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 2
                  </div>
                </div>
              </div>
            </div>
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-wait"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 3
                    </div>
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 3
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>
        <li
          class="ant-list-item"
        >
          <div
            class="ant-list-item-meta"
          >
            <div
              class="ant-list-item-meta-avatar"
            >
              <span
                class="ant-avatar ant-avatar-circle ant-avatar-image css-var-test-id ant-avatar-css-var"
              >
                <img
                  src="https://api.dicebear.com/7.x/miniavs/svg?seed=1"
                />
              </span>
            </div>
            <div
              class="ant-list-item-meta-content"
            >
              <h4
                class="ant-list-item-meta-title"
              >
                <a
                  href="https://ant.design"
                >
                  Ant Design Title 2
                </a>
              </h4>
              <div
                class="ant-list-item-meta-description"
              >
                Ant Design, a design language for background applications, is refined by Ant UED Team
              </div>
            </div>
          </div>
          <div
            class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-inline ant-steps-dot css-var-test-id"
            style="--steps-items-offset:0;margin-top:8px"
          >
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-finish"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 1
                    </div>
                    <div
                      class="ant-steps-item-rail ant-steps-item-rail-error"
                    />
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 1
                  </div>
                </div>
              </div>
            </div>
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-error ant-steps-item-active"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 2
                    </div>
                    <div
                      class="ant-steps-item-rail ant-steps-item-rail-wait"
                    />
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 2
                  </div>
                </div>
              </div>
            </div>
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-wait"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 3
                    </div>
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 3
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>
        <li
          class="ant-list-item"
        >
          <div
            class="ant-list-item-meta"
          >
            <div
              class="ant-list-item-meta-avatar"
            >
              <span
                class="ant-avatar ant-avatar-circle ant-avatar-image css-var-test-id ant-avatar-css-var"
              >
                <img
                  src="https://api.dicebear.com/7.x/miniavs/svg?seed=2"
                />
              </span>
            </div>
            <div
              class="ant-list-item-meta-content"
            >
              <h4
                class="ant-list-item-meta-title"
              >
                <a
                  href="https://ant.design"
                >
                  Ant Design Title 3
                </a>
              </h4>
              <div
                class="ant-list-item-meta-description"
              >
                Ant Design, a design language for background applications, is refined by Ant UED Team
              </div>
            </div>
          </div>
          <div
            class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-inline ant-steps-dot css-var-test-id"
            style="--steps-items-offset:0;margin-top:8px"
          >
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-finish"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 1
                    </div>
                    <div
                      class="ant-steps-item-rail ant-steps-item-rail-finish"
                    />
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 1
                  </div>
                </div>
              </div>
            </div>
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-finish"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 2
                    </div>
                    <div
                      class="ant-steps-item-rail ant-steps-item-rail-process"
                    />
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 2
                  </div>
                </div>
              </div>
            </div>
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-process ant-steps-item-active"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 3
                    </div>
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 3
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>
        <li
          class="ant-list-item"
        >
          <div
            class="ant-list-item-meta"
          >
            <div
              class="ant-list-item-meta-avatar"
            >
              <span
                class="ant-avatar ant-avatar-circle ant-avatar-image css-var-test-id ant-avatar-css-var"
              >
                <img
                  src="https://api.dicebear.com/7.x/miniavs/svg?seed=3"
                />
              </span>
            </div>
            <div
              class="ant-list-item-meta-content"
            >
              <h4
                class="ant-list-item-meta-title"
              >
                <a
                  href="https://ant.design"
                >
                  Ant Design Title 4
                </a>
              </h4>
              <div
                class="ant-list-item-meta-description"
              >
                Ant Design, a design language for background applications, is refined by Ant UED Team
              </div>
            </div>
          </div>
          <div
            class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-inline ant-steps-dot css-var-test-id"
            style="--steps-items-offset:0;margin-top:8px"
          >
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-finish"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 1
                    </div>
                    <div
                      class="ant-steps-item-rail ant-steps-item-rail-process"
                    />
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 1
                  </div>
                </div>
              </div>
            </div>
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-process ant-steps-item-active"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 2
                    </div>
                    <div
                      class="ant-steps-item-rail ant-steps-item-rail-wait"
                    />
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 2
                  </div>
                </div>
              </div>
            </div>
            <div
              aria-describedby="test-id"
              class="ant-steps-item ant-steps-item-wait"
            >
              <div
                class="ant-steps-item-wrapper"
              >
                <div
                  class="ant-steps-item-icon ant-wave-target"
                />
                <div
                  class="ant-steps-item-section"
                >
                  <div
                    class="ant-steps-item-header"
                  >
                    <div
                      class="ant-steps-item-title"
                    >
                      Step 3
                    </div>
                  </div>
                  <div
                    class="ant-steps-item-content"
                  >
                    This is Step 3
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/inline-variant.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-inline ant-steps-dot css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 1
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Sub Title"
            >
              Sub Title
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 1
          </div>
        </div>
      </div>
    </div>
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 2
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Sub Title"
            >
              Sub Title
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 2
          </div>
        </div>
      </div>
    </div>
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 3
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Sub Title"
            >
              Sub Title
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 3
          </div>
        </div>
      </div>
    </div>
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 4
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Sub Title"
            >
              Sub Title
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 4
          </div>
        </div>
      </div>
    </div>
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 5
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Sub Title"
            >
              Sub Title
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 5
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-inline ant-steps-dot css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
              style="color:#1677ff"
            >
              Step 1
            </div>
            <div
              class="ant-steps-item-subtitle"
              style="color:#0958d9"
              title="Sub Title"
            >
              Sub Title
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-finish"
              style="background:rgba(0,0,0,0.25)"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 1
          </div>
        </div>
      </div>
    </div>
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
              style="color:#1677ff"
            >
              Step 2
            </div>
            <div
              class="ant-steps-item-subtitle"
              style="color:#0958d9"
              title="Sub Title"
            >
              Sub Title
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-finish"
              style="background:rgba(0,0,0,0.25)"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 2
          </div>
        </div>
      </div>
    </div>
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
              style="color:#1677ff"
            >
              Step 3
            </div>
            <div
              class="ant-steps-item-subtitle"
              style="color:#0958d9"
              title="Sub Title"
            >
              Sub Title
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-finish"
              style="background:rgba(0,0,0,0.25)"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 3
          </div>
        </div>
      </div>
    </div>
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
              style="color:#1677ff"
            >
              Step 4
            </div>
            <div
              class="ant-steps-item-subtitle"
              style="color:#0958d9"
              title="Sub Title"
            >
              Sub Title
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-finish"
              style="background:rgba(0,0,0,0.25)"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 4
          </div>
        </div>
      </div>
    </div>
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-finish ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
              style="color:#1677ff"
            >
              Step 5
            </div>
            <div
              class="ant-steps-item-subtitle"
              style="color:#0958d9"
              title="Sub Title"
            >
              Sub Title
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 5
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-inline ant-steps-dot css-var-test-id"
    style="--steps-items-offset:2"
  >
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 3
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Sub Title"
            >
              Sub Title
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 3
          </div>
        </div>
      </div>
    </div>
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 4
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Sub Title"
            >
              Sub Title
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 4
          </div>
        </div>
      </div>
    </div>
    <div
      aria-describedby="test-id"
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 5
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Sub Title"
            >
              Sub Title
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is Step 5
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/nav.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-large ant-flex-vertical"
>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-navigation ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 1
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="00:00:05"
            >
              00:00:05
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 2
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="00:01:02"
            >
              00:01:02
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 3
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="waiting for longlong time"
            >
              waiting for longlong time
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-navigation css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 1
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 2
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 3
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            4
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 4
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-navigation ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              finish 1
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-finish"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-finish"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              finish 2
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              current process
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait ant-steps-item-disabled"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            4
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              wait
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/panel.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-panel css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            1
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 1
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="00:00"
            >
              00:00
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-error"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
      <svg
        class="ant-steps-panel-arrow"
        preserveAspectRatio="none"
        viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>
          Arrow
        </title>
        <path
          d="M 0 0 L 100 50 L 0 100"
        />
      </svg>
    </div>
    <div
      class="ant-steps-item ant-steps-item-error"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="close"
            class="anticon anticon-close ant-steps-item-icon-error"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 2
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
      <svg
        class="ant-steps-panel-arrow"
        preserveAspectRatio="none"
        viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>
          Arrow
        </title>
        <path
          d="M 0 0 L 100 50 L 0 100"
        />
      </svg>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 3
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
      <svg
        class="ant-steps-panel-arrow"
        preserveAspectRatio="none"
        viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>
          Arrow
        </title>
        <path
          d="M 0 0 L 100 50 L 0 100"
        />
      </svg>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-outlined ant-steps-panel ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            1
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 1
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="00:00"
            >
              00:00
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-error"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
      <svg
        class="ant-steps-panel-arrow"
        preserveAspectRatio="none"
        viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>
          Arrow
        </title>
        <path
          d="M 0 0 L 100 50 L 0 100"
        />
      </svg>
    </div>
    <div
      class="ant-steps-item ant-steps-item-error"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="close"
            class="anticon anticon-close ant-steps-item-icon-error"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 2
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
      <svg
        class="ant-steps-panel-arrow"
        preserveAspectRatio="none"
        viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>
          Arrow
        </title>
        <path
          d="M 0 0 L 100 50 L 0 100"
        />
      </svg>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Step 3
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
      <svg
        class="ant-steps-panel-arrow"
        preserveAspectRatio="none"
        viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>
          Arrow
        </title>
        <path
          d="M 0 0 L 100 50 L 0 100"
        />
      </svg>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/progress.tsx correctly 1`] = `
<div
  class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-with-progress css-var-test-id"
  style="--steps-items-offset:0"
>
  <div
    class="ant-steps-item ant-steps-item-finish"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        class="ant-steps-item-icon ant-wave-target"
      >
        <span
          aria-label="check"
          class="anticon anticon-check ant-steps-item-icon-finish"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="check"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Finished
          </div>
          <div
            class="ant-steps-item-rail ant-steps-item-rail-process"
          />
        </div>
        <div
          class="ant-steps-item-content"
        >
          This is a content.
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-process ant-steps-item-active"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        class="ant-steps-item-icon ant-wave-target"
      >
        <svg
          aria-valuemax="100"
          aria-valuemin="0"
          aria-valuenow="60"
          class="ant-steps-item-progress-icon-svg"
          height="100%"
          viewBox="0 0 100 100"
          width="100%"
          xmlns="http://www.w3.org/2000/svg"
        >
          <title>
            Progress
          </title>
          <circle
            class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-rail"
          />
          <circle
            class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-ptg"
            stroke-dasharray="calc(var(--progress-r) * 2 * 1.8849555921538756) 9999"
            transform="rotate(-90 50 50)"
          />
        </svg>
        <span
          class="ant-steps-item-icon-number"
        >
          2
        </span>
      </div>
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            In Progress
          </div>
          <div
            class="ant-steps-item-subtitle"
            title="Left 00:00:08"
          >
            Left 00:00:08
          </div>
          <div
            class="ant-steps-item-rail ant-steps-item-rail-wait"
          />
        </div>
        <div
          class="ant-steps-item-content"
        >
          This is a content.
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-wait"
  >
    <div
      class="ant-steps-item-wrapper"
    >
      <div
        class="ant-steps-item-icon ant-wave-target"
      >
        <span
          class="ant-steps-item-icon-number"
        >
          3
        </span>
      </div>
      <div
        class="ant-steps-item-section"
      >
        <div
          class="ant-steps-item-header"
        >
          <div
            class="ant-steps-item-title"
          >
            Waiting
          </div>
        </div>
        <div
          class="ant-steps-item-content"
        >
          This is a content.
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/progress-debug.tsx correctly 1`] = `
Array [
  <div
    class="ant-space-compact ant-space-compact-block"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
      type="button"
    >
      <span>
        Percentage to undefined
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item"
      type="button"
    >
      <span>
        Percentage +
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item"
      type="button"
    >
      <span>
        Current +
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item"
      type="button"
    >
      <span>
        Status Wait
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item"
      type="button"
    >
      <span>
        Status Process
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item"
      type="button"
    >
      <span>
        Status Finish
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-last-item"
      type="button"
    >
      <span>
        Status Error
      </span>
    </button>
  </div>,
  <br />,
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-with-progress css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <svg
            aria-valuemax="100"
            aria-valuemin="0"
            aria-valuenow="0"
            class="ant-steps-item-progress-icon-svg"
            height="100%"
            viewBox="0 0 100 100"
            width="100%"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Progress
            </title>
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-rail"
            />
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-ptg"
              stroke-dasharray="calc(var(--progress-r) * 2 * 0) 9999"
              transform="rotate(-90 50 50)"
            />
          </svg>
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Left 00:00:08"
            >
              Left 00:00:08
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-with-progress ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <svg
            aria-valuemax="100"
            aria-valuemin="0"
            aria-valuenow="0"
            class="ant-steps-item-progress-icon-svg"
            height="100%"
            viewBox="0 0 100 100"
            width="100%"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Progress
            </title>
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-rail"
            />
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-ptg"
              stroke-dasharray="calc(var(--progress-r) * 2 * 0) 9999"
              transform="rotate(-90 50 50)"
            />
          </svg>
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Left 00:00:08"
            >
              Left 00:00:08
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-filled ant-steps-with-progress css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <svg
            aria-valuemax="100"
            aria-valuemin="0"
            aria-valuenow="0"
            class="ant-steps-item-progress-icon-svg"
            height="100%"
            viewBox="0 0 100 100"
            width="100%"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Progress
            </title>
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-rail"
            />
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-ptg"
              stroke-dasharray="calc(var(--progress-r) * 2 * 0) 9999"
              transform="rotate(-90 50 50)"
            />
          </svg>
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Left 00:00:08"
            >
              Left 00:00:08
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-filled ant-steps-with-progress ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <svg
            aria-valuemax="100"
            aria-valuemin="0"
            aria-valuenow="0"
            class="ant-steps-item-progress-icon-svg"
            height="100%"
            viewBox="0 0 100 100"
            width="100%"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Progress
            </title>
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-rail"
            />
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-ptg"
              stroke-dasharray="calc(var(--progress-r) * 2 * 0) 9999"
              transform="rotate(-90 50 50)"
            />
          </svg>
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Left 00:00:08"
            >
              Left 00:00:08
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
  0,
]
`;

exports[`renders components/steps/demo/progress-dot.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-dot css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-outlined ant-steps-dot css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />
  <div
    class="ant-flex css-var-test-id ant-flex-gap-middle"
  >
    <div
      class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-filled ant-steps-dot css-var-test-id"
      style="--steps-items-offset:0;flex:auto"
    >
      <div
        class="ant-steps-item ant-steps-item-finish"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          />
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Finished
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-process"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-process ant-steps-item-active"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          />
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                In Progress
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-wait"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-wait"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          />
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Waiting
              </div>
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-outlined ant-steps-dot css-var-test-id"
      style="--steps-items-offset:0;flex:auto"
    >
      <div
        class="ant-steps-item ant-steps-item-finish"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          />
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Finished
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-process"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-process ant-steps-item-active"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          />
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                In Progress
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-wait"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-wait"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          />
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Waiting
              </div>
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/simple.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-large ant-flex-vertical"
>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Left 00:00:08"
            >
              Left 00:00:08
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-outlined css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Left 00:00:08"
            >
              Left 00:00:08
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Left 00:00:08"
            >
              Left 00:00:08
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-outlined ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-subtitle"
              title="Left 00:00:08"
            >
              Left 00:00:08
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/step-next.tsx correctly 1`] = `
Array [
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            1
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              First
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Second
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Last
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    style="line-height:260px;text-align:center;color:rgba(0,0,0,0.45);background-color:rgba(0,0,0,0.02);border-radius:8px;border:1px dashed #d9d9d9;margin-top:16px"
  >
    First-content
  </div>,
  <div
    style="margin-top:24px"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Next
      </span>
    </button>
  </div>,
]
`;

exports[`renders components/steps/demo/steps-in-steps.tsx correctly 1`] = `
Array [
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
    style="margin-bottom:16px"
  >
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="small"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Small
      </span>
    </label>
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="default"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Default
      </span>
    </label>
  </div>,
  <div
    class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-filled ant-steps-default css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            1
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            <div
              class="ant-card ant-card-bordered css-var-test-id"
            >
              <div
                class="ant-card-body"
              >
                <div
                  class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled ant-steps-default css-var-test-id"
                  style="--steps-items-offset:0"
                >
                  <div
                    class="ant-steps-item ant-steps-item-process ant-steps-item-active"
                  >
                    <div
                      class="ant-steps-item-wrapper"
                    >
                      <div
                        class="ant-steps-item-icon ant-wave-target"
                      >
                        <span
                          class="ant-steps-item-icon-number"
                        >
                          1
                        </span>
                      </div>
                      <div
                        class="ant-steps-item-section"
                      >
                        <div
                          class="ant-steps-item-header"
                        >
                          <div
                            class="ant-steps-item-title"
                          >
                            Finished
                          </div>
                          <div
                            class="ant-steps-item-rail ant-steps-item-rail-wait"
                          />
                        </div>
                        <div
                          class="ant-steps-item-content"
                        >
                          This is a content.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="ant-steps-item ant-steps-item-wait"
                  >
                    <div
                      class="ant-steps-item-wrapper"
                    >
                      <div
                        class="ant-steps-item-icon ant-wave-target"
                      >
                        <span
                          class="ant-steps-item-icon-number"
                        >
                          2
                        </span>
                      </div>
                      <div
                        class="ant-steps-item-section"
                      >
                        <div
                          class="ant-steps-item-header"
                        >
                          <div
                            class="ant-steps-item-title"
                          >
                            In Progress
                          </div>
                          <div
                            class="ant-steps-item-rail ant-steps-item-rail-wait"
                          />
                        </div>
                        <div
                          class="ant-steps-item-content"
                        >
                          This is a content.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="ant-steps-item ant-steps-item-wait"
                  >
                    <div
                      class="ant-steps-item-wrapper"
                    >
                      <div
                        class="ant-steps-item-icon ant-wave-target"
                      >
                        <span
                          class="ant-steps-item-icon-number"
                        >
                          3
                        </span>
                      </div>
                      <div
                        class="ant-steps-item-section"
                      >
                        <div
                          class="ant-steps-item-header"
                        >
                          <div
                            class="ant-steps-item-title"
                          >
                            Waiting
                          </div>
                        </div>
                        <div
                          class="ant-steps-item-content"
                        >
                          This is a content.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/steps/demo/title-placement.tsx correctly 1`] = `
Array [
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-ellipsis css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-with-progress css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <svg
            aria-valuemax="100"
            aria-valuemin="0"
            aria-valuenow="60"
            class="ant-steps-item-progress-icon-svg"
            height="100%"
            viewBox="0 0 100 100"
            width="100%"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Progress
            </title>
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-rail"
            />
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-ptg"
              stroke-dasharray="calc(var(--progress-r) * 2 * 1.8849555921538756) 9999"
              transform="rotate(-90 50 50)"
            />
          </svg>
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-filled ant-steps-with-progress ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <svg
            aria-valuemax="100"
            aria-valuemin="0"
            aria-valuenow="80"
            class="ant-steps-item-progress-icon-svg"
            height="100%"
            viewBox="0 0 100 100"
            width="100%"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Progress
            </title>
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-rail"
            />
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-ptg"
              stroke-dasharray="calc(var(--progress-r) * 2 * 2.5132741228718345) 9999"
              transform="rotate(-90 50 50)"
            />
          </svg>
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/steps/demo/variant-debug.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-filled css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-error"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-error ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="close"
            class="anticon anticon-close ant-steps-item-icon-error"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-outlined css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-error"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-error ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="close"
            class="anticon anticon-close ant-steps-item-icon-error"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-outlined ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-error"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-error ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="close"
            class="anticon anticon-close ant-steps-item-icon-error"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-horizontal ant-steps-outlined ant-steps-with-progress ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-process"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-process ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <svg
            aria-valuemax="100"
            aria-valuemin="0"
            aria-valuenow="60"
            class="ant-steps-item-progress-icon-svg"
            height="100%"
            viewBox="0 0 100 100"
            width="100%"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Progress
            </title>
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-rail"
            />
            <circle
              class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-ptg"
              stroke-dasharray="calc(var(--progress-r) * 2 * 1.8849555921538756) 9999"
              transform="rotate(-90 50 50)"
            />
          </svg>
          <span
            class="ant-steps-item-icon-number"
          >
            2
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-middle"
  >
    <div
      class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-outlined ant-steps-with-progress css-var-test-id"
      style="--steps-items-offset:0"
    >
      <div
        class="ant-steps-item ant-steps-item-finish"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              aria-label="check"
              class="anticon anticon-check ant-steps-item-icon-finish"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="check"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Finished
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-process"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-process ant-steps-item-active"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <svg
              aria-valuemax="100"
              aria-valuemin="0"
              aria-valuenow="60"
              class="ant-steps-item-progress-icon-svg"
              height="100%"
              viewBox="0 0 100 100"
              width="100%"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                Progress
              </title>
              <circle
                class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-rail"
              />
              <circle
                class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-ptg"
                stroke-dasharray="calc(var(--progress-r) * 2 * 1.8849555921538756) 9999"
                transform="rotate(-90 50 50)"
              />
            </svg>
            <span
              class="ant-steps-item-icon-number"
            >
              2
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                In Progress
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-wait"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-wait"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              class="ant-steps-item-icon-number"
            >
              3
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Waiting
              </div>
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-outlined ant-steps-with-progress ant-steps-small css-var-test-id"
      style="--steps-items-offset:0"
    >
      <div
        class="ant-steps-item ant-steps-item-finish"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              aria-label="check"
              class="anticon anticon-check ant-steps-item-icon-finish"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="check"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Finished
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-process"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-process ant-steps-item-active"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <svg
              aria-valuemax="100"
              aria-valuemin="0"
              aria-valuenow="60"
              class="ant-steps-item-progress-icon-svg"
              height="100%"
              viewBox="0 0 100 100"
              width="100%"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                Progress
              </title>
              <circle
                class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-rail"
              />
              <circle
                class="ant-steps-item-progress-icon-circle ant-steps-item-progress-icon-circle-ptg"
                stroke-dasharray="calc(var(--progress-r) * 2 * 1.8849555921538756) 9999"
                transform="rotate(-90 50 50)"
              />
            </svg>
            <span
              class="ant-steps-item-icon-number"
            >
              2
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                In Progress
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-wait"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-wait"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              class="ant-steps-item-icon-number"
            >
              3
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Waiting
              </div>
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-middle"
  >
    <div
      class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-outlined css-var-test-id"
      style="--steps-items-offset:0"
    >
      <div
        class="ant-steps-item ant-steps-item-finish"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              aria-label="check"
              class="anticon anticon-check ant-steps-item-icon-finish"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="check"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Finished
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-error"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-error ant-steps-item-active"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              aria-label="close"
              class="anticon anticon-close ant-steps-item-icon-error"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                In Progress
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-wait"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-wait"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              class="ant-steps-item-icon-number"
            >
              3
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Waiting
              </div>
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-outlined ant-steps-small css-var-test-id"
      style="--steps-items-offset:0"
    >
      <div
        class="ant-steps-item ant-steps-item-finish"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              aria-label="check"
              class="anticon anticon-check ant-steps-item-icon-finish"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="check"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Finished
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-error"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-error ant-steps-item-active"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              aria-label="close"
              class="anticon anticon-close ant-steps-item-icon-error"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                In Progress
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-wait"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-wait"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              class="ant-steps-item-icon-number"
            >
              3
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Waiting
              </div>
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-outlined ant-steps-dot ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-error"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-error ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-middle"
  >
    <div
      class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-outlined ant-steps-dot ant-steps-small css-var-test-id"
      style="--steps-items-offset:0"
    >
      <div
        class="ant-steps-item ant-steps-item-finish"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          />
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Finished
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-error"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-error ant-steps-item-active"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          />
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                In Progress
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-wait"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-wait"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          />
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Waiting
              </div>
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-outlined ant-steps-navigation ant-steps-small css-var-test-id"
      style="--steps-items-offset:0"
    >
      <div
        class="ant-steps-item ant-steps-item-finish"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              aria-label="check"
              class="anticon anticon-check ant-steps-item-icon-finish"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="check"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Finished
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-error"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-error ant-steps-item-active"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              aria-label="close"
              class="anticon anticon-close ant-steps-item-icon-error"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                In Progress
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-wait"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-wait"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              class="ant-steps-item-icon-number"
            >
              3
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Waiting
              </div>
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-outlined ant-steps-dot css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-error"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-error ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        />
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-outlined css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-error"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-error ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="close"
            class="anticon anticon-close ant-steps-item-icon-error"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps ant-steps-horizontal ant-steps-title-vertical ant-steps-outlined ant-steps-small css-var-test-id"
    style="--steps-items-offset:0"
  >
    <div
      class="ant-steps-item ant-steps-item-finish"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="check"
            class="anticon anticon-check ant-steps-item-icon-finish"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Finished
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-error"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-error ant-steps-item-active"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            aria-label="close"
            class="anticon anticon-close ant-steps-item-icon-error"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              In Progress
            </div>
            <div
              class="ant-steps-item-rail ant-steps-item-rail-wait"
            />
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-steps-item ant-steps-item-wait"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-steps-item-wrapper"
      >
        <div
          class="ant-steps-item-icon ant-wave-target"
        >
          <span
            class="ant-steps-item-icon-number"
          >
            3
          </span>
        </div>
        <div
          class="ant-steps-item-section"
        >
          <div
            class="ant-steps-item-header"
          >
            <div
              class="ant-steps-item-title"
            >
              Waiting
            </div>
          </div>
          <div
            class="ant-steps-item-content"
          >
            This is a content.
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/steps/demo/vertical.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id"
>
  <div
    style="flex:1"
  >
    <div
      class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-filled css-var-test-id"
      style="--steps-items-offset:0"
    >
      <div
        class="ant-steps-item ant-steps-item-finish"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              aria-label="check"
              class="anticon anticon-check ant-steps-item-icon-finish"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="check"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Finished
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-process"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-process ant-steps-item-active"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              class="ant-steps-item-icon-number"
            >
              2
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                In Progress
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-wait"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-wait"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              class="ant-steps-item-icon-number"
            >
              3
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Waiting
              </div>
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    style="flex:1"
  >
    <div
      class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-filled ant-steps-small css-var-test-id"
      style="--steps-items-offset:0"
    >
      <div
        class="ant-steps-item ant-steps-item-finish"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              aria-label="check"
              class="anticon anticon-check ant-steps-item-icon-finish"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="check"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Finished
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-process"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-process ant-steps-item-active"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              class="ant-steps-item-icon-number"
            >
              2
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                In Progress
              </div>
              <div
                class="ant-steps-item-rail ant-steps-item-rail-wait"
              />
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-steps-item ant-steps-item-wait"
      >
        <div
          class="ant-steps-item-wrapper"
        >
          <div
            class="ant-steps-item-icon ant-wave-target"
          >
            <span
              class="ant-steps-item-icon-number"
            >
              3
            </span>
          </div>
          <div
            class="ant-steps-item-section"
          >
            <div
              class="ant-steps-item-header"
            >
              <div
                class="ant-steps-item-title"
              >
                Waiting
              </div>
            </div>
            <div
              class="ant-steps-item-content"
            >
              This is a content.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
