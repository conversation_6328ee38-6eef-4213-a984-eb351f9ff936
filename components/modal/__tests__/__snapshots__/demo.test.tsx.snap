// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/modal/demo/async.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open Modal with async logic
  </span>
</button>
`;

exports[`renders components/modal/demo/basic.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open Modal
  </span>
</button>
`;

exports[`renders components/modal/demo/button-props.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open Modal with customized button props
  </span>
</button>
`;

exports[`renders components/modal/demo/classNames.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Open Modal
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        ConfigProvider
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/modal/demo/component-token.tsx correctly 1`] = `
<div
  style="display:flex;flex-direction:column;row-gap:16px"
>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel css-var-test-id ant-css-var"
    role="dialog"
    style="width:100%"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <button
          aria-label="Close"
          class="ant-modal-close"
          type="button"
        >
          <span
            class="ant-modal-close-x"
          >
            <span
              aria-label="close"
              class="anticon anticon-close ant-modal-close-icon"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="ant-modal-header"
        >
          <div
            class="ant-modal-title"
          >
            Hello World!
          </div>
        </div>
        <div
          class="ant-modal-body"
        >
          Hello World?!
        </div>
        <div
          class="ant-modal-footer"
        >
          <button
            class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              Cancel
            </span>
          </button>
          <button
            class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
            type="button"
          >
            <span>
              OK
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel css-var-test-id ant-css-var"
    role="dialog"
    style="width:100%"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <button
          aria-label="Close"
          class="ant-modal-close"
          type="button"
        >
          <span
            class="ant-modal-close-x"
          >
            <span
              aria-label="close"
              class="anticon anticon-close ant-modal-close-icon"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="ant-modal-header"
        >
          <div
            class="ant-modal-title"
          >
            Hello World!
          </div>
        </div>
        <div
          class="ant-modal-body"
        >
          Hello World?!
        </div>
        <div
          class="ant-modal-footer"
        >
          <button
            class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              Cancel
            </span>
          </button>
          <button
            class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
            type="button"
          >
            <span>
              OK
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel ant-modal-confirm ant-modal-confirm-success css-var-test-id ant-css-var"
    role="dialog"
    style="width:200px"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <div
          class="ant-modal-body"
        >
          <div
            class="ant-modal-confirm-body-wrapper"
          >
            <div
              class="ant-modal-confirm-body"
            >
              <span
                aria-label="check-circle"
                class="anticon anticon-check-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="check-circle"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
                  />
                </svg>
              </span>
              <div
                class="ant-modal-confirm-paragraph"
              >
                <div
                  class="ant-modal-confirm-content"
                >
                  A good news!
                </div>
              </div>
            </div>
            <div
              class="ant-modal-confirm-btns"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel ant-modal-confirm ant-modal-confirm-confirm css-var-test-id ant-css-var"
    role="dialog"
    style="width:300px"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <div
          class="ant-modal-body"
        >
          <div
            class="ant-modal-confirm-body-wrapper"
          >
            <div
              class="ant-modal-confirm-body ant-modal-confirm-body-has-title"
            >
              <span
                aria-label="exclamation-circle"
                class="anticon anticon-exclamation-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="exclamation-circle"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                  />
                </svg>
              </span>
              <div
                class="ant-modal-confirm-paragraph"
              >
                <span
                  class="ant-modal-confirm-title"
                >
                  Confirm This?
                </span>
                <div
                  class="ant-modal-confirm-content"
                >
                  Some descriptions.
                </div>
              </div>
            </div>
            <div
              class="ant-modal-confirm-btns"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                type="button"
              >
                <span>
                  Cancel
                </span>
              </button>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
</div>
`;

exports[`renders components/modal/demo/confirm.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="flex-wrap:wrap"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Confirm
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        With promise
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
      type="button"
    >
      <span>
        Delete
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
      type="button"
    >
      <span>
        With extra props
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/modal/demo/confirm-router.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    Confirm
  </span>
</button>
`;

exports[`renders components/modal/demo/custom-mouse-position.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open Modal
  </span>
</button>
`;

exports[`renders components/modal/demo/dark.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open Modal
  </span>
</button>
`;

exports[`renders components/modal/demo/footer.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open Modal with customized footer
  </span>
</button>
`;

exports[`renders components/modal/demo/footer-render.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Open Modal
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Open Modal Confirm
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/modal/demo/hooks.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Confirm
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Warning
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Info
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Error
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/modal/demo/loading.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open Modal
  </span>
</button>
`;

exports[`renders components/modal/demo/locale.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Modal
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Confirm
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/modal/demo/manual.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    Open modal to close in 5s
  </span>
</button>
`;

exports[`renders components/modal/demo/mask.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Default blur
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Dimmed mask
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        No mask
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/modal/demo/modal-render.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    Open Draggable Modal
  </span>
</button>
`;

exports[`renders components/modal/demo/nested.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      aria-checked="false"
      class="ant-switch css-var-test-id"
      role="switch"
      style="position:relative;z-index:0"
      type="button"
    >
      <div
        class="ant-switch-handle"
      />
      <span
        class="ant-switch-inner"
      >
        <span
          class="ant-switch-inner-checked"
        >
          Open
        </span>
        <span
          class="ant-switch-inner-unchecked"
        >
          Close
        </span>
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Static
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  />
  <div
    class="ant-space-item"
  />
  <div
    class="ant-space-item"
  />
</div>
`;

exports[`renders components/modal/demo/position.tsx correctly 1`] = `
Array [
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Display a modal dialog at 20px to Top
    </span>
  </button>,
  <br />,
  <br />,
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Vertically centered modal dialog
    </span>
  </button>,
]
`;

exports[`renders components/modal/demo/render-panel.tsx correctly 1`] = `
<div
  style="display:flex;flex-direction:column;row-gap:16px"
>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel css-var-test-id ant-css-var"
    role="dialog"
    style="width:100%;height:200px"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <button
          aria-label="Close"
          class="ant-modal-close"
          type="button"
        >
          <span
            class="ant-modal-close-x"
          >
            <span
              aria-label="close"
              class="anticon anticon-close ant-modal-close-icon"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="ant-modal-header"
        >
          <div
            class="ant-modal-title"
          >
            Hello World!
          </div>
        </div>
        <div
          class="ant-modal-body"
        >
          Hello World?!
        </div>
        <div
          class="ant-modal-footer"
        >
          <button
            class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              Cancel
            </span>
          </button>
          <button
            class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
            type="button"
          >
            <span>
              OK
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel ant-modal-confirm ant-modal-confirm-success css-var-test-id ant-css-var"
    role="dialog"
    style="width:200px;height:150px"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <div
          class="ant-modal-body"
        >
          <div
            class="ant-modal-confirm-body-wrapper"
          >
            <div
              class="ant-modal-confirm-body"
            >
              <span
                aria-label="check-circle"
                class="anticon anticon-check-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="check-circle"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
                  />
                </svg>
              </span>
              <div
                class="ant-modal-confirm-paragraph"
              >
                <div
                  class="ant-modal-confirm-content"
                >
                  A good news!
                </div>
              </div>
            </div>
            <div
              class="ant-modal-confirm-btns"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel ant-modal-confirm ant-modal-confirm-confirm css-var-test-id ant-css-var"
    role="dialog"
    style="width:300px;height:200px"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <div
          class="ant-modal-body"
        >
          <div
            class="ant-modal-confirm-body-wrapper"
          >
            <div
              class="ant-modal-confirm-body ant-modal-confirm-body-has-title"
            >
              <span
                aria-label="exclamation-circle"
                class="anticon anticon-exclamation-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="exclamation-circle"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                  />
                </svg>
              </span>
              <div
                class="ant-modal-confirm-paragraph"
              >
                <span
                  class="ant-modal-confirm-title"
                >
                  Confirm This?
                </span>
                <div
                  class="ant-modal-confirm-content"
                >
                  Some descriptions.
                </div>
              </div>
            </div>
            <div
              class="ant-modal-confirm-btns"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                type="button"
              >
                <span>
                  Cancel
                </span>
              </button>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel css-var-test-id ant-css-var"
    role="dialog"
    style="width:380px;height:200px"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <button
          aria-label="Close"
          class="ant-modal-close"
          type="button"
        >
          <span
            class="ant-modal-close-x"
          >
            <span
              aria-label="close"
              class="anticon anticon-close ant-modal-close-icon"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="ant-modal-header"
        >
          <div
            class="ant-modal-title"
          >
            Custom Footer Render
          </div>
        </div>
        <div
          class="ant-modal-body"
        >
          <div
            class="ant-typography css-var-test-id"
          >
            <a
              class="ant-typography css-var-test-id"
              href="https://github.com/ant-design/ant-design/pull/44318"
            >
              Feature #44318
            </a>
          </div>
        </div>
        <div
          class="ant-modal-footer"
        >
          <div
            class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
          >
            <div
              class="ant-space-item"
            >
              <div
                class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
              >
                <div
                  class="ant-space-item"
                >
                  <button
                    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                    type="button"
                  >
                    <span>
                      Cancel
                    </span>
                  </button>
                </div>
                <div
                  class="ant-space-item"
                >
                  <button
                    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                    type="button"
                  >
                    <span>
                      OK
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <div
              class="ant-space-item"
            >
              <div
                class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
              >
                <div
                  class="ant-space-item"
                >
                  <button
                    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                    type="button"
                  >
                    <span>
                      Cancel
                    </span>
                  </button>
                </div>
                <div
                  class="ant-space-item"
                >
                  <button
                    class="ant-btn css-var-test-id ant-btn-primary ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-solid"
                    type="button"
                  >
                    <span>
                      Custom
                    </span>
                  </button>
                </div>
                <div
                  class="ant-space-item"
                >
                  <button
                    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                    type="button"
                  >
                    <span>
                      OK
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
</div>
`;

exports[`renders components/modal/demo/static-info.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="flex-wrap:wrap"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Info
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Success
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Error
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Warning
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/modal/demo/width.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-flex-start ant-flex-gap-middle ant-flex-vertical"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Open Modal of 1000px width
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Open Modal of responsive width
    </span>
  </button>
</div>
`;

exports[`renders components/modal/demo/wireframe.tsx correctly 1`] = `
<div
  style="display:flex;flex-direction:column;row-gap:16px"
>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel css-var-test-id ant-css-var"
    role="dialog"
    style="width:100%"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <button
          aria-label="Close"
          class="ant-modal-close"
          type="button"
        >
          <span
            class="ant-modal-close-x"
          >
            <span
              aria-label="close"
              class="anticon anticon-close ant-modal-close-icon"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="ant-modal-header"
        >
          <div
            class="ant-modal-title"
          >
            Hello World!
          </div>
        </div>
        <div
          class="ant-modal-body"
        >
          Hello World?!
        </div>
        <div
          class="ant-modal-footer"
        >
          <button
            class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              Cancel
            </span>
          </button>
          <button
            class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
            type="button"
          >
            <span>
              OK
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel ant-modal-confirm ant-modal-confirm-success css-var-test-id ant-css-var"
    role="dialog"
    style="width:200px"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <div
          class="ant-modal-body"
        >
          <div
            class="ant-modal-confirm-body-wrapper"
          >
            <div
              class="ant-modal-confirm-body"
            >
              <span
                aria-label="check-circle"
                class="anticon anticon-check-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="check-circle"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
                  />
                </svg>
              </span>
              <div
                class="ant-modal-confirm-paragraph"
              >
                <div
                  class="ant-modal-confirm-content"
                >
                  A good news!
                </div>
              </div>
            </div>
            <div
              class="ant-modal-confirm-btns"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
  <div
    aria-modal="true"
    class="ant-modal ant-modal-pure-panel ant-modal-confirm ant-modal-confirm-confirm css-var-test-id ant-css-var"
    role="dialog"
    style="width:300px"
  >
    <div
      style="outline:none"
      tabindex="0"
    >
      <div
        class="ant-modal-container"
      >
        <div
          class="ant-modal-body"
        >
          <div
            class="ant-modal-confirm-body-wrapper"
          >
            <div
              class="ant-modal-confirm-body ant-modal-confirm-body-has-title"
            >
              <span
                aria-label="exclamation-circle"
                class="anticon anticon-exclamation-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="exclamation-circle"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                  />
                </svg>
              </span>
              <div
                class="ant-modal-confirm-paragraph"
              >
                <span
                  class="ant-modal-confirm-title"
                >
                  Confirm This?
                </span>
                <div
                  class="ant-modal-confirm-content"
                >
                  Some descriptions.
                </div>
              </div>
            </div>
            <div
              class="ant-modal-confirm-btns"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                type="button"
              >
                <span>
                  Cancel
                </span>
              </button>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      style="width:0;height:0;overflow:hidden;outline:none"
      tabindex="0"
    />
  </div>
</div>
`;
