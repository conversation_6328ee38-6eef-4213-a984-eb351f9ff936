// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Modal Both ways should be rendered normally on the page 1`] = `
<div
  class="first-origin"
>
  <button
    class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
    type="button"
  >
    <span>
      Cancel
    </span>
  </button>
  <button
    class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      OK
    </span>
  </button>
</div>
`;

exports[`Modal Both ways should be rendered normally on the page 2`] = `
<div
  class="second-props-origin"
>
  <button
    class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      OK
    </span>
  </button>
  <button
    class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
    type="button"
  >
    <span>
      Cancel
    </span>
  </button>
</div>
`;

exports[`Modal render correctly 1`] = `
<div>
  <div>
    <div
      class="ant-modal-root css-var-root ant-modal-css-var"
    >
      <div
        class="ant-modal-mask ant-fade-appear ant-fade-appear-start ant-fade ant-modal-mask-blur"
      />
      <div
        class="ant-modal-wrap"
        tabindex="-1"
      >
        <div
          aria-modal="true"
          class="ant-modal ant-zoom-appear ant-zoom-appear-prepare ant-zoom"
          role="dialog"
          style="width: 520px;"
        >
          <div
            style="outline: none;"
            tabindex="0"
          >
            <div
              class="ant-modal-container"
            >
              <button
                aria-label="Close"
                class="ant-modal-close"
                type="button"
              >
                <span
                  aria-label="Close"
                  class="ant-modal-close-x"
                >
                  <span
                    aria-label="close"
                    class="anticon anticon-close ant-modal-close-icon"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                      />
                    </svg>
                  </span>
                </span>
              </button>
              <div
                class="ant-modal-body"
              >
                Here is content of Modal
              </div>
              <div
                class="ant-modal-footer"
              >
                <button
                  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                  type="button"
                >
                  <span>
                    Cancel
                  </span>
                </button>
                <button
                  class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                  type="button"
                >
                  <span>
                    OK
                  </span>
                </button>
              </div>
            </div>
          </div>
          <div
            style="width: 0px; height: 0px; overflow: hidden; outline: none;"
            tabindex="0"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Modal render without footer 1`] = `
<div>
  <div>
    <div
      class="ant-modal-root css-var-root ant-modal-css-var"
    >
      <div
        class="ant-modal-mask ant-fade-appear ant-fade-appear-start ant-fade ant-modal-mask-blur"
      />
      <div
        class="ant-modal-wrap"
        tabindex="-1"
      >
        <div
          aria-modal="true"
          class="ant-modal ant-zoom-appear ant-zoom-appear-prepare ant-zoom"
          role="dialog"
          style="width: 520px;"
        >
          <div
            style="outline: none;"
            tabindex="0"
          >
            <div
              class="ant-modal-container"
            >
              <button
                aria-label="Close"
                class="ant-modal-close"
                type="button"
              >
                <span
                  aria-label="Close"
                  class="ant-modal-close-x"
                >
                  <span
                    aria-label="close"
                    class="anticon anticon-close ant-modal-close-icon"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                      />
                    </svg>
                  </span>
                </span>
              </button>
              <div
                class="ant-modal-body"
              >
                Here is content of Modal
              </div>
            </div>
          </div>
          <div
            style="width: 0px; height: 0px; overflow: hidden; outline: none;"
            tabindex="0"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Modal rtl render component should be rendered correctly in RTL direction 1`] = `null`;

exports[`Modal support closeIcon 1`] = `
<div
  class="ant-modal-root css-var-root ant-modal-css-var"
>
  <div
    class="ant-modal-mask ant-fade-appear ant-fade-appear-start ant-fade ant-modal-mask-blur"
  />
  <div
    class="ant-modal-wrap"
    tabindex="-1"
  >
    <div
      aria-modal="true"
      class="ant-modal ant-zoom-appear ant-zoom-appear-prepare ant-zoom"
      role="dialog"
      style="width: 520px;"
    >
      <div
        style="outline: none;"
        tabindex="0"
      >
        <div
          class="ant-modal-container"
        >
          <button
            aria-label="Close"
            class="ant-modal-close"
            type="button"
          >
            <span
              aria-label="Close"
              class="ant-modal-close-x"
            >
              <a>
                closeIcon
              </a>
            </span>
          </button>
          <div
            class="ant-modal-body"
          />
          <div
            class="ant-modal-footer"
          >
            <button
              class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
              type="button"
            >
              <span>
                Cancel
              </span>
            </button>
            <button
              class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="button"
            >
              <span>
                OK
              </span>
            </button>
          </div>
        </div>
      </div>
      <div
        style="width: 0px; height: 0px; overflow: hidden; outline: none;"
        tabindex="0"
      />
    </div>
  </div>
</div>
`;
