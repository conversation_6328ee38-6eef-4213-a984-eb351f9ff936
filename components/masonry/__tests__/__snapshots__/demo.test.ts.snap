// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/masonry/demo/basic.tsx correctly 1`] = `
<div
  class="ant-masonry css-var-test-id"
  style="height:0"
/>
`;

exports[`renders components/masonry/demo/fresh.tsx correctly 1`] = `
<div
  class="ant-masonry css-var-test-id"
  style="height:0"
/>
`;

exports[`renders components/masonry/demo/image.tsx correctly 1`] = `
<div
  class="ant-masonry css-var-test-id"
  style="height:0"
/>
`;

exports[`renders components/masonry/demo/responsive.tsx correctly 1`] = `
<div
  class="ant-masonry css-var-test-id"
  style="height:0"
/>
`;
