// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/button/demo/basic.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Primary Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
    type="button"
  >
    <span>
      Default Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
    type="button"
  >
    <span>
      Dashed Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-text ant-btn-color-default ant-btn-variant-text"
    type="button"
  >
    <span>
      Text Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link"
    type="button"
  >
    <span>
      Link Button
    </span>
  </button>
</div>
`;

exports[`renders components/button/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/block.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-small ant-flex-vertical"
  style="width: 100%;"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-block"
    type="button"
  >
    <span>
      Primary
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-block"
    type="button"
  >
    <span>
      Default
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-block"
    type="button"
  >
    <span>
      Dashed
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-block"
    disabled=""
    type="button"
  >
    <span>
      disabled
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-text ant-btn-color-default ant-btn-variant-text ant-btn-block"
    type="button"
  >
    <span>
      text
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link ant-btn-block"
    type="button"
  >
    <span>
      Link
    </span>
  </button>
</div>
`;

exports[`renders components/button/demo/block.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/chinese-chars-loading.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-two-chinese-chars"
    type="button"
  >
    <span>
      <span>
        部署
      </span>
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-loading"
    type="button"
  >
    <span
      class="ant-btn-icon ant-btn-loading-icon"
    >
      <span
        aria-label="loading"
        class="anticon anticon-loading anticon-spin"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="loading"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
          />
        </svg>
      </span>
    </span>
    <span>
      部 署
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-loading"
    type="button"
  >
    <span
      class="ant-btn-icon ant-btn-loading-icon"
    >
      <span
        aria-label="loading"
        class="anticon anticon-loading anticon-spin"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="loading"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
          />
        </svg>
      </span>
    </span>
    部署
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-loading"
    type="button"
  >
    <span
      class="ant-btn-icon ant-btn-loading-icon"
    >
      <span
        aria-label="loading"
        class="anticon anticon-loading anticon-spin"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="loading"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
          />
        </svg>
      </span>
    </span>
    <span>
      部署
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-loading"
    type="button"
  >
    <span
      class="ant-btn-icon ant-btn-loading-icon"
    >
      <span
        aria-label="loading"
        class="anticon anticon-loading anticon-spin"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="loading"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
          />
        </svg>
      </span>
    </span>
    Submit
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-loading"
    type="button"
  >
    <span
      class="ant-btn-icon ant-btn-loading-icon"
    >
      <span
        aria-label="loading"
        class="anticon anticon-loading anticon-spin"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="loading"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
          />
        </svg>
      </span>
    </span>
    部署
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-loading"
    type="button"
  >
    <span
      class="ant-btn-icon ant-btn-loading-icon"
    >
      <span
        aria-label="loading"
        class="anticon anticon-loading anticon-spin"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="loading"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
          />
        </svg>
      </span>
    </span>
    <span>
      按 钮
    </span>
  </button>
</div>
`;

exports[`renders components/button/demo/chinese-chars-loading.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/chinese-space.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      确定
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      确 定
    </span>
  </button>
</div>
`;

exports[`renders components/button/demo/chinese-space.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/color-variant.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-small ant-flex-vertical"
>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-solid ant-btn-sm"
      type="button"
    >
      <span>
        Solid
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm"
      type="button"
    >
      <span>
        Outlined
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-dashed ant-btn-sm"
      type="button"
    >
      <span>
        Dashed
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-filled ant-btn-sm"
      type="button"
    >
      <span>
        Filled
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-text ant-btn-sm"
      type="button"
    >
      <span>
        Text
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-link ant-btn-sm"
      type="button"
    >
      <span>
        Link
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
      type="button"
    >
      <span>
        Solid
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-primary ant-btn-variant-outlined ant-btn-sm"
      type="button"
    >
      <span>
        Outlined
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-primary ant-btn-variant-dashed ant-btn-sm"
      type="button"
    >
      <span>
        Dashed
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-primary ant-btn-variant-filled ant-btn-sm"
      type="button"
    >
      <span>
        Filled
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-primary ant-btn-variant-text ant-btn-sm"
      type="button"
    >
      <span>
        Text
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-primary ant-btn-variant-link ant-btn-sm"
      type="button"
    >
      <span>
        Link
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-dangerous ant-btn-variant-solid ant-btn-sm"
      type="button"
    >
      <span>
        Solid
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-dangerous ant-btn-variant-outlined ant-btn-sm"
      type="button"
    >
      <span>
        Outlined
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-dangerous ant-btn-variant-dashed ant-btn-sm"
      type="button"
    >
      <span>
        Dashed
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-dangerous ant-btn-variant-filled ant-btn-sm"
      type="button"
    >
      <span>
        Filled
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-dangerous ant-btn-variant-text ant-btn-sm"
      type="button"
    >
      <span>
        Text
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-dangerous ant-btn-variant-link ant-btn-sm"
      type="button"
    >
      <span>
        Link
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-pink ant-btn-variant-solid ant-btn-sm"
      type="button"
    >
      <span>
        Solid
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-pink ant-btn-variant-outlined ant-btn-sm"
      type="button"
    >
      <span>
        Outlined
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-pink ant-btn-variant-dashed ant-btn-sm"
      type="button"
    >
      <span>
        Dashed
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-pink ant-btn-variant-filled ant-btn-sm"
      type="button"
    >
      <span>
        Filled
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-pink ant-btn-variant-text ant-btn-sm"
      type="button"
    >
      <span>
        Text
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-pink ant-btn-variant-link ant-btn-sm"
      type="button"
    >
      <span>
        Link
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-purple ant-btn-variant-solid ant-btn-sm"
      type="button"
    >
      <span>
        Solid
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-purple ant-btn-variant-outlined ant-btn-sm"
      type="button"
    >
      <span>
        Outlined
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-purple ant-btn-variant-dashed ant-btn-sm"
      type="button"
    >
      <span>
        Dashed
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-purple ant-btn-variant-filled ant-btn-sm"
      type="button"
    >
      <span>
        Filled
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-purple ant-btn-variant-text ant-btn-sm"
      type="button"
    >
      <span>
        Text
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-purple ant-btn-variant-link ant-btn-sm"
      type="button"
    >
      <span>
        Link
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-cyan ant-btn-variant-solid ant-btn-sm"
      type="button"
    >
      <span>
        Solid
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-cyan ant-btn-variant-outlined ant-btn-sm"
      type="button"
    >
      <span>
        Outlined
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-cyan ant-btn-variant-dashed ant-btn-sm"
      type="button"
    >
      <span>
        Dashed
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-cyan ant-btn-variant-filled ant-btn-sm"
      type="button"
    >
      <span>
        Filled
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-cyan ant-btn-variant-text ant-btn-sm"
      type="button"
    >
      <span>
        Text
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-cyan ant-btn-variant-link ant-btn-sm"
      type="button"
    >
      <span>
        Link
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/button/demo/color-variant.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/custom-disabled-bg.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    disabled=""
    type="button"
  >
    <span>
      Primary Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
    disabled=""
    type="button"
  >
    <span>
      Default Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
    disabled=""
    type="button"
  >
    <span>
      Dashed Button
    </span>
  </button>
</div>
`;

exports[`renders components/button/demo/custom-disabled-bg.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/danger.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-solid"
    type="button"
  >
    <span>
      Primary
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined"
    type="button"
  >
    <span>
      Default
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-dashed ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-dashed"
    type="button"
  >
    <span>
      Dashed
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-text ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-text"
    type="button"
  >
    <span>
      Text
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-link ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-link"
    type="button"
  >
    <span>
      Link
    </span>
  </button>
</div>
`;

exports[`renders components/button/demo/danger.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/debug-block.tsx extend context correctly 1`] = `
<form
  class="ant-form ant-form-horizontal css-var-test-id ant-form-css-var"
>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-control css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn css-var-test-id ant-btn-round ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-block"
              style="margin-bottom: 12px;"
              type="button"
            >
              <span>
                Submit
              </span>
            </button>
            <button
              class="ant-btn css-var-test-id ant-btn-round ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only"
              type="button"
            >
              <span
                class="ant-btn-icon"
              >
                <span
                  aria-label="download"
                  class="anticon anticon-download"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="download"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
                    />
                  </svg>
                </span>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/button/demo/debug-block.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/debug-color-variant.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-small ant-flex-vertical"
>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link"
      type="button"
    >
      <span>
        Link Button
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link"
      type="button"
    >
      <span>
        Link Button
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid acss-1ncngmd"
      type="button"
    >
      <span>
        Primary Button
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined acss-1ndra3x"
      type="button"
    >
      <span>
        Default Button
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed acss-1ndra3x"
      type="button"
    >
      <span>
        Dashed Button
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-text ant-btn-color-default ant-btn-variant-text acss-v6wxmk"
      type="button"
    >
      <span>
        Text Button
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link acss-1r0846s"
      type="button"
    >
      <span>
        Link Button
      </span>
    </button>
  </div>
  <div
    class="acss-1cu8o9m ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Primary Button
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Default Button
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
      type="button"
    >
      <span>
        Dashed Button
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-text ant-btn-color-default ant-btn-variant-text"
      type="button"
    >
      <span>
        Text Button
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link"
      type="button"
    >
      <span>
        Link Button
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/button/demo/debug-color-variant.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/debug-icon.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="large"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Large
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="default"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Default
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="small"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Small
      </span>
    </label>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start ant-divider-plain"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Preview
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-small ant-flex-vertical"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; bottom: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            search
          </div>
        </div>
      </div>
      <button
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg"
        type="button"
      >
        <span>
          A
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          Search
        </span>
      </button>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; bottom: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            search
          </div>
        </div>
      </div>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          Search
        </span>
      </button>
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; bottom: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            search
          </div>
        </div>
      </div>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          Search
        </span>
      </button>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; bottom: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            search
          </div>
        </div>
      </div>
      <button
        class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-lg"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          Search
        </span>
      </button>
      <a
        aria-disabled="false"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only"
        href="https://www.google.com"
        tabindex="0"
        target="_blank"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </a>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        type="button"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
        <span>
          Search
        </span>
      </button>
    </div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center ant-divider-plain"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
         https://github.com/ant-design/ant-design/issues/51811 
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        type="button"
      >
        <span>
          without icon
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          with icon
        </span>
      </button>
    </div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center ant-divider-plain"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
         https://github.com/ant-design/ant-design/issues/52124 
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        style="height: 60px;"
        type="button"
      >
        <span>
          without icon
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        style="height: 60px;"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          with icon
        </span>
      </button>
    </div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center ant-divider-plain"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
         https://github.com/ant-design/ant-design/issues/51380 
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <svg
            class="my-class-name"
            fill="none"
            height="1em"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            viewBox="0 0 24 24"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 3h7a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-7m0-18H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7m0-18v18"
            />
          </svg>
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <svg
            fill="none"
            height="1em"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            viewBox="0 0 24 24"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 3h7a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-7m0-18H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7m0-18v18"
            />
          </svg>
        </span>
        <span>
          custom icon
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          with icon
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        type="button"
      >
        <span>
          without icon
        </span>
      </button>
      <span
        class="ant-input-group-wrapper ant-input-group-wrapper-lg ant-input-group-wrapper-outlined ant-input-search ant-input-search-large css-var-test-id ant-input-css-var"
        style="width: 100px;"
      >
        <span
          class="ant-input-wrapper ant-input-group"
        >
          <input
            class="ant-input ant-input-lg ant-input-outlined"
            type="search"
            value=""
          />
          <span
            class="ant-input-group-addon"
          >
            <button
              class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only ant-input-search-button"
              type="button"
            >
              <span
                class="ant-btn-icon"
              >
                <span
                  aria-label="search"
                  class="anticon anticon-search"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="search"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                    />
                  </svg>
                </span>
              </span>
            </button>
          </span>
        </span>
      </span>
    </div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center ant-divider-plain"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
         https://github.com/ant-design/ant-design/issues/51380 
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-gap-small"
      style="transform: scale(3); transform-origin: left top;"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="minus-square"
            class="anticon anticon-minus-square"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="minus-square"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
              />
              <path
                d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"
              />
            </svg>
          </span>
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <div
            style="background: red; width: 12px; height: 12px;"
          />
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <div
            style="background: green; width: 16px; height: 16px;"
          />
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <div
            style="background: blue; width: 14px; height: 16px;"
          />
        </span>
      </button>
    </div>
  </div>,
]
`;

exports[`renders components/button/demo/debug-icon.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/disabled.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-flex-start ant-flex-gap-small ant-flex-vertical"
>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Primary
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      disabled=""
      type="button"
    >
      <span>
        Primary(disabled)
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Default
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      disabled=""
      type="button"
    >
      <span>
        Default(disabled)
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
      type="button"
    >
      <span>
        Dashed
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
      disabled=""
      type="button"
    >
      <span>
        Dashed(disabled)
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-text ant-btn-color-default ant-btn-variant-text"
      type="button"
    >
      <span>
        Text
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-text ant-btn-color-default ant-btn-variant-text"
      disabled=""
      type="button"
    >
      <span>
        Text(disabled)
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link"
      type="button"
    >
      <span>
        Link
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link"
      disabled=""
      type="button"
    >
      <span>
        Link(disabled)
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <a
      aria-disabled="false"
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      href="https://ant.design/index-cn"
      tabindex="0"
    >
      <span>
        Href Primary
      </span>
    </a>
    <a
      aria-disabled="true"
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-disabled"
      tabindex="-1"
    >
      <span>
        Href Primary(disabled)
      </span>
    </a>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Danger Default
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined"
      disabled=""
      type="button"
    >
      <span>
        Danger Default(disabled)
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-text ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-text"
      type="button"
    >
      <span>
        Danger Text
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-text ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-text"
      disabled=""
      type="button"
    >
      <span>
        Danger Text(disabled)
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-link ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-link"
      type="button"
    >
      <span>
        Danger Link
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-link ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-link"
      disabled=""
      type="button"
    >
      <span>
        Danger Link(disabled)
      </span>
    </button>
  </div>
  <div
    class="site-button-ghost-wrapper ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-background-ghost"
      type="button"
    >
      <span>
        Ghost
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-background-ghost"
      disabled=""
      type="button"
    >
      <span>
        Ghost(disabled)
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/button/demo/disabled.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/ghost.tsx extend context correctly 1`] = `
<div
  class="site-button-ghost-wrapper ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-outlined ant-btn-background-ghost"
    type="button"
  >
    <span>
      Primary
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-background-ghost"
    type="button"
  >
    <span>
      Default
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-background-ghost"
    type="button"
  >
    <span>
      Dashed
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined ant-btn-background-ghost"
    type="button"
  >
    <span>
      Danger
    </span>
  </button>
</div>
`;

exports[`renders components/button/demo/ghost.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/icon.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-small ant-flex-vertical"
>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-circle ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          search
        </div>
      </div>
    </div>
    <button
      class="ant-btn css-var-test-id ant-btn-circle ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        A
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
      </span>
      <span>
        Search
      </span>
    </button>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-circle ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          search
        </div>
      </div>
    </div>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
      </span>
      <span>
        Search
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-circle ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          search
        </div>
      </div>
    </div>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
      </span>
      <span>
        Search
      </span>
    </button>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-circle ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          search
        </div>
      </div>
    </div>
    <button
      class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
      </span>
      <span>
        Search
      </span>
    </button>
    <a
      aria-disabled="false"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
      href="https://www.google.com"
      tabindex="0"
      target="_blank"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
      </span>
    </a>
  </div>
</div>
`;

exports[`renders components/button/demo/icon.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/icon-placement.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      <div
        class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
      >
        <label
          class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
        >
          <span
            class="ant-radio-button"
          >
            <input
              class="ant-radio-button-input"
              name="test-id"
              type="radio"
              value="start"
            />
            <span
              class="ant-radio-button-inner"
            />
          </span>
          <span
            class="ant-radio-button-label"
          >
            start
          </span>
        </label>
        <label
          class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
        >
          <span
            class="ant-radio-button ant-radio-button-checked"
          >
            <input
              checked=""
              class="ant-radio-button-input"
              name="test-id"
              type="radio"
              value="end"
            />
            <span
              class="ant-radio-button-inner"
            />
          </span>
          <span
            class="ant-radio-button-label"
          >
            end
          </span>
        </label>
      </div>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start ant-divider-plain"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Preview
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-small ant-flex-vertical"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; bottom: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            search
          </div>
        </div>
      </div>
      <button
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          A
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-end"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          Search
        </span>
      </button>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; bottom: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            search
          </div>
        </div>
      </div>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-end"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          Search
        </span>
      </button>
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; bottom: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            search
          </div>
        </div>
      </div>
      <button
        class="ant-btn css-var-test-id ant-btn-text ant-btn-color-default ant-btn-variant-text ant-btn-icon-end"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          Search
        </span>
      </button>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; bottom: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            search
          </div>
        </div>
      </div>
      <button
        class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-icon-end"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
        <span>
          Search
        </span>
      </button>
      <a
        aria-disabled="false"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-icon-end"
        href="https://www.google.com"
        tabindex="0"
        target="_blank"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </a>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-loading ant-btn-icon-end"
        type="button"
      >
        <span
          class="ant-btn-icon ant-btn-loading-icon"
        >
          <span
            aria-label="loading"
            class="anticon anticon-loading anticon-spin"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="loading"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
              />
            </svg>
          </span>
        </span>
        <span>
          Loading
        </span>
      </button>
    </div>
  </div>,
]
`;

exports[`renders components/button/demo/icon-placement.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/legacy-group.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-btn-group ant-btn-group-sm"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
      type="button"
    >
      <span>
        Button 1
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
      type="button"
    >
      <span>
        Button 2
      </span>
    </button>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="download"
          class="anticon anticon-download"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="download"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          Tooltip
        </div>
      </div>
    </div>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="download"
          class="anticon anticon-download"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="download"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          Tooltip
        </div>
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-btn-group"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Button 1
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Button 2
      </span>
    </button>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="download"
          class="anticon anticon-download"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="download"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          Tooltip
        </div>
      </div>
    </div>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="download"
          class="anticon anticon-download"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="download"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          Tooltip
        </div>
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-btn-group ant-btn-group-lg"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg"
      type="button"
    >
      <span>
        Button 1
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg"
      type="button"
    >
      <span>
        Button 2
      </span>
    </button>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-btn-icon-only"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="download"
          class="anticon anticon-download"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="download"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          Tooltip
        </div>
      </div>
    </div>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="download"
          class="anticon anticon-download"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="download"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          Tooltip
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/button/demo/legacy-group.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Button.Group] \`Button.Group\` is deprecated. Please use \`Space.Compact\` instead.",
]
`;

exports[`renders components/button/demo/linear-gradient.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg acss-wkbm77"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="ant-design"
          class="anticon anticon-ant-design"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="ant-design"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M716.3 313.8c19-18.9 19-49.7 0-68.6l-69.9-69.9.1.1c-18.5-18.5-50.3-50.3-95.3-95.2-21.2-20.7-55.5-20.5-76.5.5L80.9 474.2a53.84 53.84 0 000 76.4L474.6 944a54.14 54.14 0 0076.5 0l165.1-165c19-18.9 19-49.7 0-68.6a48.7 48.7 0 00-68.7 0l-125 125.2c-5.2 5.2-13.3 5.2-18.5 0L189.5 521.4c-5.2-5.2-5.2-13.3 0-18.5l314.4-314.2c.4-.4.9-.7 1.3-1.1 5.2-4.1 12.4-3.7 17.2 1.1l125.2 125.1c19 19 49.8 19 68.7 0zM408.6 514.4a106.3 106.2 0 10212.6 0 106.3 106.2 0 10-212.6 0zm536.2-38.6L821.9 353.5c-19-18.9-49.8-18.9-68.7.1a48.4 48.4 0 000 68.6l83 82.9c5.2 5.2 5.2 13.3 0 18.5l-81.8 81.7a48.4 48.4 0 000 68.6 48.7 48.7 0 0068.7 0l121.8-121.7a53.93 53.93 0 00-.1-76.4z"
            />
          </svg>
        </span>
      </span>
      <span>
        Gradient Button
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg acss-wkbm77"
      type="button"
    >
      <span>
        Button
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/button/demo/linear-gradient.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/loading.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-small ant-flex-vertical"
>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-loading"
      type="button"
    >
      <span
        class="ant-btn-icon ant-btn-loading-icon"
      >
        <span
          aria-label="loading"
          class="anticon anticon-loading anticon-spin"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="loading"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="0 0 1024 1024"
            width="1em"
          >
            <path
              d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
            />
          </svg>
        </span>
      </span>
      <span>
        Loading
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-loading"
      type="button"
    >
      <span
        class="ant-btn-icon ant-btn-loading-icon"
      >
        <span
          aria-label="loading"
          class="anticon anticon-loading anticon-spin"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="loading"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="0 0 1024 1024"
            width="1em"
          >
            <path
              d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
            />
          </svg>
        </span>
      </span>
      <span>
        Loading
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only ant-btn-loading"
      type="button"
    >
      <span
        class="ant-btn-icon ant-btn-loading-icon"
      >
        <span
          aria-label="loading"
          class="anticon anticon-loading anticon-spin"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="loading"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="0 0 1024 1024"
            width="1em"
          >
            <path
              d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
            />
          </svg>
        </span>
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-loading"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="sync"
          class="anticon anticon-sync anticon-spin"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="sync"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"
            />
          </svg>
        </span>
      </span>
      <span>
        Loading Icon
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Icon Start
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-end"
      type="button"
    >
      <span>
        Icon End
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="poweroff"
          class="anticon anticon-poweroff"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="poweroff"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M705.6 124.9a8 8 0 00-11.6 7.2v64.2c0 5.5 2.9 10.6 7.5 13.6a352.2 352.2 0 0162.2 49.8c32.7 32.8 58.4 70.9 76.3 113.3a355 355 0 0127.9 138.7c0 48.1-9.4 94.8-27.9 138.7a355.92 355.92 0 01-76.3 113.3 353.06 353.06 0 01-113.2 76.4c-43.8 18.6-90.5 28-138.5 28s-94.7-9.4-138.5-28a353.06 353.06 0 01-113.2-76.4A355.92 355.92 0 01184 650.4a355 355 0 01-27.9-138.7c0-48.1 9.4-94.8 27.9-138.7 17.9-42.4 43.6-80.5 76.3-113.3 19-19 39.8-35.6 62.2-49.8 4.7-2.9 7.5-8.1 7.5-13.6V132c0-6-6.3-9.8-11.6-7.2C178.5 195.2 82 339.3 80 506.3 77.2 745.1 272.5 943.5 511.2 944c239 .5 432.8-193.3 432.8-432.4 0-169.2-97-315.7-238.4-386.7zM480 560h64c4.4 0 8-3.6 8-8V88c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8z"
            />
          </svg>
        </span>
      </span>
      <span>
        Icon Replace
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="poweroff"
          class="anticon anticon-poweroff"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="poweroff"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M705.6 124.9a8 8 0 00-11.6 7.2v64.2c0 5.5 2.9 10.6 7.5 13.6a352.2 352.2 0 0162.2 49.8c32.7 32.8 58.4 70.9 76.3 113.3a355 355 0 0127.9 138.7c0 48.1-9.4 94.8-27.9 138.7a355.92 355.92 0 01-76.3 113.3 353.06 353.06 0 01-113.2 76.4c-43.8 18.6-90.5 28-138.5 28s-94.7-9.4-138.5-28a353.06 353.06 0 01-113.2-76.4A355.92 355.92 0 01184 650.4a355 355 0 01-27.9-138.7c0-48.1 9.4-94.8 27.9-138.7 17.9-42.4 43.6-80.5 76.3-113.3 19-19 39.8-35.6 62.2-49.8 4.7-2.9 7.5-8.1 7.5-13.6V132c0-6-6.3-9.8-11.6-7.2C178.5 195.2 82 339.3 80 506.3 77.2 745.1 272.5 943.5 511.2 944c239 .5 432.8-193.3 432.8-432.4 0-169.2-97-315.7-238.4-386.7zM480 560h64c4.4 0 8-3.6 8-8V88c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8z"
            />
          </svg>
        </span>
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="poweroff"
          class="anticon anticon-poweroff"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="poweroff"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M705.6 124.9a8 8 0 00-11.6 7.2v64.2c0 5.5 2.9 10.6 7.5 13.6a352.2 352.2 0 0162.2 49.8c32.7 32.8 58.4 70.9 76.3 113.3a355 355 0 0127.9 138.7c0 48.1-9.4 94.8-27.9 138.7a355.92 355.92 0 01-76.3 113.3 353.06 353.06 0 01-113.2 76.4c-43.8 18.6-90.5 28-138.5 28s-94.7-9.4-138.5-28a353.06 353.06 0 01-113.2-76.4A355.92 355.92 0 01184 650.4a355 355 0 01-27.9-138.7c0-48.1 9.4-94.8 27.9-138.7 17.9-42.4 43.6-80.5 76.3-113.3 19-19 39.8-35.6 62.2-49.8 4.7-2.9 7.5-8.1 7.5-13.6V132c0-6-6.3-9.8-11.6-7.2C178.5 195.2 82 339.3 80 506.3 77.2 745.1 272.5 943.5 511.2 944c239 .5 432.8-193.3 432.8-432.4 0-169.2-97-315.7-238.4-386.7zM480 560h64c4.4 0 8-3.6 8-8V88c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8z"
            />
          </svg>
        </span>
      </span>
      <span>
        Loading Icon
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/button/demo/loading.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/multiple.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-flex-start ant-flex-gap-small ant-flex-vertical"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      primary
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
    type="button"
  >
    <span>
      secondary
    </span>
  </button>
  <div
    class="ant-space-compact"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
      type="button"
    >
      <span>
        Actions
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="ellipsis"
          class="anticon anticon-ellipsis"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="ellipsis"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-dropdown-css-var ant-dropdown-placement-bottomRight"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <ul
        class="ant-dropdown-menu ant-dropdown-menu-root ant-dropdown-menu-vertical ant-dropdown-menu-light css-var-test-id ant-dropdown-css-var css-var-test-id ant-dropdown-menu-css-var"
        data-menu-list="true"
        role="menu"
        tabindex="0"
      >
        <li
          aria-describedby="test-id"
          class="ant-dropdown-menu-item ant-dropdown-menu-item-only-child"
          data-menu-id="rc-menu-uuid-test-1"
          role="menuitem"
          tabindex="-1"
        >
          <span
            class="ant-dropdown-menu-title-content"
          >
            1st item
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-dropdown-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-dropdown-menu-item ant-dropdown-menu-item-only-child"
          data-menu-id="rc-menu-uuid-test-2"
          role="menuitem"
          tabindex="-1"
        >
          <span
            class="ant-dropdown-menu-title-content"
          >
            2nd item
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-dropdown-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-dropdown-menu-item ant-dropdown-menu-item-only-child"
          data-menu-id="rc-menu-uuid-test-3"
          role="menuitem"
          tabindex="-1"
        >
          <span
            class="ant-dropdown-menu-title-content"
          >
            3rd item
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-dropdown-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
      </ul>
      <div
        aria-hidden="true"
        style="display: none;"
      />
    </div>
  </div>
</div>
`;

exports[`renders components/button/demo/multiple.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/size.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="large"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Large
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="default"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Default
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="small"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Small
      </span>
    </label>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start ant-divider-plain"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Preview
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-flex css-var-test-id ant-flex-align-flex-start ant-flex-gap-small ant-flex-vertical"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg"
        type="button"
      >
        <span>
          Primary
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg"
        type="button"
      >
        <span>
          Default
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-lg"
        type="button"
      >
        <span>
          Dashed
        </span>
      </button>
    </div>
    <button
      class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link ant-btn-lg"
      type="button"
    >
      <span>
        Link
      </span>
    </button>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="download"
            class="anticon anticon-download"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="download"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
              />
            </svg>
          </span>
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-circle ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="download"
            class="anticon anticon-download"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="download"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
              />
            </svg>
          </span>
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-round ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="download"
            class="anticon anticon-download"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="download"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
              />
            </svg>
          </span>
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-round ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="download"
            class="anticon anticon-download"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="download"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
              />
            </svg>
          </span>
        </span>
        <span>
          Download
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-lg"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="download"
            class="anticon anticon-download"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="download"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
              />
            </svg>
          </span>
        </span>
        <span>
          Download
        </span>
      </button>
    </div>
  </div>,
]
`;

exports[`renders components/button/demo/size.tsx extend context correctly 2`] = `[]`;

exports[`renders components/button/demo/style-class.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center css-var-test-id"
  style="flex-wrap: wrap; column-gap: 8px; row-gap: 16px;"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-gap-small"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined demo-btn-root"
        type="button"
      >
        <span
          class="demo-btn-content"
        >
          classNames Object
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid demo-btn-root--primary"
        disabled=""
        type="button"
      >
        <span>
          classNames Function
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-gap-small"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="border-width: 2px; border-style: dashed;"
        type="button"
      >
        <span
          style="font-style: italic;"
        >
          styles Object
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        disabled=""
        style="opacity: 0.5; cursor: not-allowed; border-color: red;"
        type="button"
      >
        <span>
          styles Function
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`renders components/button/demo/style-class.tsx extend context correctly 2`] = `[]`;
