// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Button fixbug renders {0} , 0 and {false} 1`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  0
</button>
`;

exports[`Button fixbug renders {0} , 0 and {false} 2`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    0
  </span>
</button>
`;

exports[`Button fixbug renders {0} , 0 and {false} 3`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
/>
`;

exports[`Button renders Chinese characters correctly 1`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    按 钮
  </span>
</button>
`;

exports[`Button renders Chinese characters correctly 2`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span
    class="ant-btn-icon"
  >
    <span
      aria-label="search"
      class="anticon anticon-search"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="search"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
        />
      </svg>
    </span>
  </span>
  <span>
    按钮
  </span>
</button>
`;

exports[`Button renders Chinese characters correctly 3`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span
    aria-label="search"
    class="anticon anticon-search"
    role="img"
  >
    <svg
      aria-hidden="true"
      data-icon="search"
      fill="currentColor"
      focusable="false"
      height="1em"
      viewBox="64 64 896 896"
      width="1em"
    >
      <path
        d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
      />
    </svg>
  </span>
  <span>
    按钮
  </span>
</button>
`;

exports[`Button renders Chinese characters correctly 4`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span
    class="ant-btn-icon"
  >
    <span
      aria-label="search"
      class="anticon anticon-search"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="search"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
        />
      </svg>
    </span>
  </span>
  <span>
    按钮
  </span>
</button>
`;

exports[`Button renders Chinese characters correctly 5`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-loading"
  type="button"
>
  <span
    class="ant-btn-icon ant-btn-loading-icon"
  >
    <span
      aria-label="loading"
      class="anticon anticon-loading anticon-spin"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="loading"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
      >
        <path
          d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
        />
      </svg>
    </span>
  </span>
  <span>
    按钮
  </span>
</button>
`;

exports[`Button renders Chinese characters correctly 6`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-loading"
  type="button"
>
  <span
    class="ant-btn-icon ant-btn-loading-icon"
  >
    <span
      aria-label="loading"
      class="anticon anticon-loading anticon-spin"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="loading"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
      >
        <path
          d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
        />
      </svg>
    </span>
  </span>
  <span>
    按 钮
  </span>
</button>
`;

exports[`Button renders Chinese characters correctly 7`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    按 钮
  </span>
</button>
`;

exports[`Button renders correctly 1`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    Follow
  </span>
</button>
`;

exports[`Button rtl render component should be rendered correctly in RTL direction 1`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-rtl"
  type="button"
/>
`;

exports[`Button rtl render component should be rendered correctly in RTL direction 2`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-rtl"
  type="button"
/>
`;

exports[`Button rtl render component should be rendered correctly in RTL direction 3`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm ant-btn-rtl"
  type="button"
/>
`;

exports[`Button rtl render component should be rendered correctly in RTL direction 4`] = `
<div
  class="ant-btn-group ant-btn-group-rtl"
/>
`;

exports[`Button rtl render component should be rendered correctly in RTL direction 5`] = `
<div
  class="ant-btn-group ant-btn-group-lg ant-btn-group-rtl"
/>
`;

exports[`Button rtl render component should be rendered correctly in RTL direction 6`] = `
<div
  class="ant-btn-group ant-btn-group-sm ant-btn-group-rtl"
/>
`;

exports[`Button rtl render component should be rendered correctly in RTL direction 7`] = `
<div
  class="ant-btn-group ant-btn-group-rtl"
/>
`;

exports[`Button should handle fragment as children 1`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    text
  </span>
</button>
`;

exports[`Button should merge text if children using variable 1`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    This is a test 1
  </span>
</button>
`;

exports[`Button should not render as link button when href is undefined 1`] = `
<button
  class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    button
  </span>
</button>
`;

exports[`Button should render empty button without errors 1`] = `
<button
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
/>
`;

exports[`Button should support custom icon className 1`] = `
<div>
  <button
    class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only"
    type="button"
  >
    <span
      class="ant-btn-icon custom-icon"
    >
      <span
        aria-label="search"
        class="anticon anticon-search"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="search"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
          />
        </svg>
      </span>
    </span>
  </button>
</div>
`;

exports[`Button should support custom icon styles 1`] = `
<div>
  <button
    class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only"
    type="button"
  >
    <span
      class="ant-btn-icon"
      style="color: rgb(255, 0, 0);"
    >
      <span
        aria-label="search"
        class="anticon anticon-search"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="search"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
          />
        </svg>
      </span>
    </span>
  </button>
</div>
`;

exports[`Button should support link button 1`] = `
<a
  aria-disabled="false"
  class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  href="https://ant.design"
  tabindex="0"
  target="_blank"
>
  <span>
    link button
  </span>
</a>
`;
