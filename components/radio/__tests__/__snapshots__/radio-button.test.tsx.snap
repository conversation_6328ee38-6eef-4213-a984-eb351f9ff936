// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Radio Button rtl render component should be rendered correctly in RTL direction 1`] = `
<label
  class="ant-radio-button-wrapper ant-radio-button-wrapper-rtl css-var-root ant-radio-css-var"
>
  <span
    class="ant-radio-button"
  >
    <input
      class="ant-radio-button-input"
      type="radio"
    />
    <span
      class="ant-radio-button-inner"
    />
  </span>
</label>
`;

exports[`Radio Button should render correctly 1`] = `
<label
  class="ant-radio-button-wrapper customized css-var-root ant-radio-css-var"
>
  <span
    class="ant-radio-button"
  >
    <input
      class="ant-radio-button-input"
      type="radio"
    />
    <span
      class="ant-radio-button-inner"
    />
  </span>
  <span
    class="ant-radio-button-label"
  >
    Test
  </span>
</label>
`;

exports[`Radio Group passes prefixCls down to radio 1`] = `
<div
  class="my-radio-group my-radio-group-outline css-var-root my-radio-css-var"
>
  <label
    class="my-radio-wrapper css-var-root my-radio-css-var"
  >
    <span
      class="my-radio ant-wave-target"
    >
      <input
        class="my-radio-input"
        name="test-id"
        type="radio"
        value="Apple"
      />
      <span
        class="my-radio-inner"
      />
    </span>
    <span
      class="my-radio-label"
    >
      Apple
    </span>
  </label>
  <label
    class="my-radio-wrapper css-var-root my-radio-css-var"
    style="font-size: 12px;"
  >
    <span
      class="my-radio ant-wave-target"
    >
      <input
        class="my-radio-input"
        name="test-id"
        type="radio"
        value="Orange"
      />
      <span
        class="my-radio-inner"
      />
    </span>
    <span
      class="my-radio-label"
    >
      Orange
    </span>
  </label>
</div>
`;
