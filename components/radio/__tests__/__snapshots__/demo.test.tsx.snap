// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/radio/demo/badge.tsx correctly 1`] = `
<div
  class="ant-radio-group ant-radio-group-solid css-var-test-id ant-radio-css-var"
>
  <span
    class="ant-badge css-var-test-id"
  >
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="1"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Click Me
      </span>
    </label>
    <sup
      class="ant-scroll-number ant-badge-count"
      data-show="true"
      title="1"
    >
      <bdi>
        <span
          class="ant-scroll-number-only"
          style="transition:none"
        >
          <span
            class="ant-scroll-number-only-unit current"
          >
            1
          </span>
        </span>
      </bdi>
    </sup>
  </span>
  <span
    class="ant-badge css-var-test-id"
  >
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="2"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Not Me
      </span>
    </label>
    <sup
      class="ant-scroll-number ant-badge-count"
      data-show="true"
      title="2"
    >
      <bdi>
        <span
          class="ant-scroll-number-only"
          style="transition:none"
        >
          <span
            class="ant-scroll-number-only-unit current"
          >
            2
          </span>
        </span>
      </bdi>
    </sup>
  </span>
</div>
`;

exports[`renders components/radio/demo/basic.tsx correctly 1`] = `
<label
  class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
>
  <span
    class="ant-radio ant-wave-target"
  >
    <input
      class="ant-radio-input"
      type="radio"
    />
    <span
      class="ant-radio-inner"
    />
  </span>
  <span
    class="ant-radio-label"
  >
    Radio
  </span>
</label>
`;

exports[`renders components/radio/demo/component-token.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          type="radio"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Test
      </span>
    </label>
  </div>
  <div
    class="ant-space-item"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked ant-radio-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked ant-radio-disabled"
      >
        <input
          checked=""
          class="ant-radio-input"
          disabled=""
          type="radio"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Disabled
      </span>
    </label>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
    >
      <label
        class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button ant-radio-button-checked"
        >
          <input
            checked=""
            class="ant-radio-button-input"
            name="test-id"
            type="radio"
            value="a"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Hangzhou
        </span>
      </label>
      <label
        class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button"
        >
          <input
            class="ant-radio-button-input"
            name="test-id"
            type="radio"
            value="b"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Shanghai
        </span>
      </label>
      <label
        class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button"
        >
          <input
            class="ant-radio-button-input"
            name="test-id"
            type="radio"
            value="c"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Beijing
        </span>
      </label>
      <label
        class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button"
        >
          <input
            class="ant-radio-button-input"
            name="test-id"
            type="radio"
            value="d"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Chengdu
        </span>
      </label>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
    >
      <label
        class="ant-radio-button-wrapper ant-radio-button-wrapper-checked ant-radio-button-wrapper-disabled css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button ant-radio-button-checked ant-radio-button-disabled"
        >
          <input
            checked=""
            class="ant-radio-button-input"
            disabled=""
            name="test-id"
            type="radio"
            value="a"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Hangzhou
        </span>
      </label>
      <label
        class="ant-radio-button-wrapper ant-radio-button-wrapper-disabled css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button ant-radio-button-disabled"
        >
          <input
            class="ant-radio-button-input"
            disabled=""
            name="test-id"
            type="radio"
            value="b"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Shanghai
        </span>
      </label>
      <label
        class="ant-radio-button-wrapper ant-radio-button-wrapper-disabled css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button ant-radio-button-disabled"
        >
          <input
            class="ant-radio-button-input"
            disabled=""
            name="test-id"
            type="radio"
            value="c"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Beijing
        </span>
      </label>
      <label
        class="ant-radio-button-wrapper ant-radio-button-wrapper-disabled css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button ant-radio-button-disabled"
        >
          <input
            class="ant-radio-button-input"
            disabled=""
            name="test-id"
            type="radio"
            value="d"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Chengdu
        </span>
      </label>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-radio-group ant-radio-group-solid css-var-test-id ant-radio-css-var"
    >
      <label
        class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button ant-radio-button-checked"
        >
          <input
            checked=""
            class="ant-radio-button-input"
            name="test-id"
            type="radio"
            value="a"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Hangzhou
        </span>
      </label>
      <label
        class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button"
        >
          <input
            class="ant-radio-button-input"
            name="test-id"
            type="radio"
            value="b"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Shanghai
        </span>
      </label>
      <label
        class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button"
        >
          <input
            class="ant-radio-button-input"
            name="test-id"
            type="radio"
            value="c"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Beijing
        </span>
      </label>
      <label
        class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio-button"
        >
          <input
            class="ant-radio-button-input"
            name="test-id"
            type="radio"
            value="d"
          />
          <span
            class="ant-radio-button-inner"
          />
        </span>
        <span
          class="ant-radio-button-label"
        >
          Chengdu
        </span>
      </label>
    </div>
  </div>
</div>
`;

exports[`renders components/radio/demo/debug-upload.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-upload-wrapper css-var-test-id"
    >
      <div
        class="ant-upload ant-upload-select"
      >
        <span
          class="ant-upload"
        >
          <input
            accept=""
            name="file"
            style="display:none"
            type="file"
          />
          <label
            class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
          >
            <span
              class="ant-radio ant-wave-target"
            >
              <input
                class="ant-radio-input"
                type="radio"
              />
              <span
                class="ant-radio-inner"
              />
            </span>
            <span
              class="ant-radio-label"
            >
              Radio
            </span>
          </label>
        </span>
      </div>
      <div
        class="ant-upload-list ant-upload-list-text"
      />
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-upload-wrapper css-var-test-id"
    >
      <div
        class="ant-upload ant-upload-select"
      >
        <span
          class="ant-upload"
        >
          <input
            accept=""
            name="file"
            style="display:none"
            type="file"
          />
          <label
            class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
          >
            <span
              class="ant-checkbox ant-wave-target"
            >
              <input
                class="ant-checkbox-input"
                type="checkbox"
              />
              <span
                class="ant-checkbox-inner"
              />
            </span>
            <span
              class="ant-checkbox-label"
            >
              Checkbox
            </span>
          </label>
        </span>
      </div>
      <div
        class="ant-upload-list ant-upload-list-text"
      />
    </span>
  </div>
</div>
`;

exports[`renders components/radio/demo/disabled.tsx correctly 1`] = `
Array [
  <label
    class="ant-radio-wrapper ant-radio-wrapper-disabled css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target ant-radio-disabled"
    >
      <input
        class="ant-radio-input"
        disabled=""
        type="radio"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      Disabled
    </span>
  </label>,
  <label
    class="ant-radio-wrapper ant-radio-wrapper-disabled css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target ant-radio-checked ant-radio-disabled"
    >
      <input
        checked=""
        class="ant-radio-input"
        disabled=""
        type="radio"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      Disabled
    </span>
  </label>,
  <br />,
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    style="margin-top:16px"
    type="button"
  >
    <span>
      Toggle disabled
    </span>
  </button>,
]
`;

exports[`renders components/radio/demo/radiobutton.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Chengdu
      </span>
    </label>
  </div>
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-disabled"
      >
        <input
          class="ant-radio-button-input"
          disabled=""
          name="test-id"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Chengdu
      </span>
    </label>
  </div>
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked ant-radio-button-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked ant-radio-button-disabled"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          disabled=""
          name="test-id"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-disabled"
      >
        <input
          class="ant-radio-button-input"
          disabled=""
          name="test-id"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-disabled"
      >
        <input
          class="ant-radio-button-input"
          disabled=""
          name="test-id"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-disabled"
      >
        <input
          class="ant-radio-button-input"
          disabled=""
          name="test-id"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Chengdu
      </span>
    </label>
  </div>
</div>
`;

exports[`renders components/radio/demo/radiobutton-solid.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-radio-group ant-radio-group-solid css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Chengdu
      </span>
    </label>
  </div>
  <div
    class="ant-radio-group ant-radio-group-solid css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-disabled"
      >
        <input
          class="ant-radio-button-input"
          disabled=""
          name="test-id"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Chengdu
      </span>
    </label>
  </div>
</div>
`;

exports[`renders components/radio/demo/radiogroup.tsx correctly 1`] = `
<div
  class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
>
  <label
    class="ant-radio-wrapper ant-radio-wrapper-checked option-1 css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target ant-radio-checked"
    >
      <input
        checked=""
        class="ant-radio-input"
        name="test-id"
        type="radio"
        value="1"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center ant-flex-gap-small ant-flex-vertical"
      >
        <span
          aria-label="line-chart"
          class="anticon anticon-line-chart"
          role="img"
          style="font-size:18px"
        >
          <svg
            aria-hidden="true"
            data-icon="line-chart"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM305.8 637.7c3.1 3.1 8.1 3.1 11.3 0l138.3-137.6L583 628.5c3.1 3.1 8.2 3.1 11.3 0l275.4-275.3c3.1-3.1 3.1-8.2 0-11.3l-39.6-39.6a8.03 8.03 0 00-11.3 0l-230 229.9L461.4 404a8.03 8.03 0 00-11.3 0L266.3 586.7a8.03 8.03 0 000 11.3l39.5 39.7z"
            />
          </svg>
        </span>
        LineChart
      </div>
    </span>
  </label>
  <label
    class="ant-radio-wrapper option-2 css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        name="test-id"
        type="radio"
        value="2"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center ant-flex-gap-small ant-flex-vertical"
      >
        <span
          aria-label="dot-chart"
          class="anticon anticon-dot-chart"
          role="img"
          style="font-size:18px"
        >
          <svg
            aria-hidden="true"
            data-icon="dot-chart"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM288 604a64 64 0 10128 0 64 64 0 10-128 0zm118-224a48 48 0 1096 0 48 48 0 10-96 0zm158 228a96 96 0 10192 0 96 96 0 10-192 0zm148-314a56 56 0 10112 0 56 56 0 10-112 0z"
            />
          </svg>
        </span>
        DotChart
      </div>
    </span>
  </label>
  <label
    class="ant-radio-wrapper option-3 css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        name="test-id"
        type="radio"
        value="3"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center ant-flex-gap-small ant-flex-vertical"
      >
        <span
          aria-label="bar-chart"
          class="anticon anticon-bar-chart"
          role="img"
          style="font-size:18px"
        >
          <svg
            aria-hidden="true"
            data-icon="bar-chart"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"
            />
          </svg>
        </span>
        BarChart
      </div>
    </span>
  </label>
  <label
    class="ant-radio-wrapper option-4 css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        name="test-id"
        type="radio"
        value="4"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center ant-flex-gap-small ant-flex-vertical"
      >
        <span
          aria-label="pie-chart"
          class="anticon anticon-pie-chart"
          role="img"
          style="font-size:18px"
        >
          <svg
            aria-hidden="true"
            data-icon="pie-chart"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M864 518H506V160c0-4.4-3.6-8-8-8h-26a398.46 398.46 0 00-282.8 117.1 398.19 398.19 0 00-85.7 127.1A397.61 397.61 0 0072 552a398.46 398.46 0 00117.1 282.8c36.7 36.7 79.5 65.6 127.1 85.7A397.61 397.61 0 00472 952a398.46 398.46 0 00282.8-117.1c36.7-36.7 65.6-79.5 85.7-127.1A397.61 397.61 0 00872 552v-26c0-4.4-3.6-8-8-8zM705.7 787.8A331.59 331.59 0 01470.4 884c-88.1-.4-170.9-34.9-233.2-97.2C174.5 724.1 140 640.7 140 552c0-88.7 34.5-172.1 97.2-234.8 54.6-54.6 124.9-87.9 200.8-95.5V586h364.3c-7.7 76.3-41.3 147-96.6 201.8zM952 462.4l-2.6-28.2c-8.5-92.1-49.4-179-115.2-244.6A399.4 399.4 0 00589 74.6L560.7 72c-4.7-.4-8.7 3.2-8.7 7.9V464c0 4.4 3.6 8 8 8l384-1c4.7 0 8.4-4 8-8.6zm-332.2-58.2V147.6a332.24 332.24 0 01166.4 89.8c45.7 45.6 77 103.6 90 166.1l-256.4.7z"
            />
          </svg>
        </span>
        PieChart
      </div>
    </span>
  </label>
</div>
`;

exports[`renders components/radio/demo/radiogroup-block.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-radio-group ant-radio-group-outline ant-radio-group-block css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked ant-radio-wrapper-block css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-radio-wrapper ant-radio-wrapper-block css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-radio-wrapper ant-radio-wrapper-block css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Orange
      </span>
    </label>
  </div>
  <div
    class="ant-radio-group ant-radio-group-solid ant-radio-group-block css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked ant-radio-button-wrapper-block css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-block css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-block css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Orange
      </span>
    </label>
  </div>
  <div
    class="ant-radio-group ant-radio-group-outline ant-radio-group-block css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-block css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked ant-radio-button-wrapper-block css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-block css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Orange
      </span>
    </label>
  </div>
</div>
`;

exports[`renders components/radio/demo/radiogroup-more.tsx correctly 1`] = `
<div
  class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var ant-radio-group-vertical"
>
  <label
    class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target ant-radio-checked"
    >
      <input
        checked=""
        class="ant-radio-input"
        name="test-id"
        type="radio"
        value="1"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      Option A
    </span>
  </label>
  <label
    class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        name="test-id"
        type="radio"
        value="2"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      Option B
    </span>
  </label>
  <label
    class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        name="test-id"
        type="radio"
        value="3"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      Option C
    </span>
  </label>
  <label
    class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        name="test-id"
        type="radio"
        value="4"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      More...
    </span>
  </label>
</div>
`;

exports[`renders components/radio/demo/radiogroup-options.tsx correctly 1`] = `
Array [
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Orange
      </span>
    </label>
  </div>,
  <br />,
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked label-1 css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-radio-wrapper label-2 css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-radio-wrapper ant-radio-wrapper-disabled label-3 css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-disabled"
      >
        <input
          class="ant-radio-input"
          disabled=""
          name="test-id"
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Orange
      </span>
    </label>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked label-1 css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper label-2 css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper label-3 css-var-test-id ant-radio-css-var"
      title="Orange"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Orange
      </span>
    </label>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-radio-group ant-radio-group-solid css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked label-1 css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper label-2 css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-disabled label-3 css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-disabled"
      >
        <input
          class="ant-radio-button-input"
          disabled=""
          name="test-id"
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Orange
      </span>
    </label>
  </div>,
]
`;

exports[`renders components/radio/demo/radiogroup-with-name.tsx correctly 1`] = `
<div
  class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
>
  <label
    class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target ant-radio-checked"
    >
      <input
        checked=""
        class="ant-radio-input"
        name="radiogroup"
        type="radio"
        value="1"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      A
    </span>
  </label>
  <label
    class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        name="radiogroup"
        type="radio"
        value="2"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      B
    </span>
  </label>
  <label
    class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        name="radiogroup"
        type="radio"
        value="3"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      C
    </span>
  </label>
  <label
    class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        name="radiogroup"
        type="radio"
        value="4"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span
      class="ant-radio-label"
    >
      D
    </span>
  </label>
</div>
`;

exports[`renders components/radio/demo/size.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-radio-group ant-radio-group-outline ant-radio-group-large css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Chengdu
      </span>
    </label>
  </div>
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Chengdu
      </span>
    </label>
  </div>
  <div
    class="ant-radio-group ant-radio-group-outline ant-radio-group-small css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Chengdu
      </span>
    </label>
  </div>
</div>
`;

exports[`renders components/radio/demo/wireframe.tsx correctly 1`] = `
Array [
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="1"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        A
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="2"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        B
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="3"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        C
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="4"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        D
      </span>
    </label>
  </div>,
  <br />,
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked ant-radio-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked ant-radio-disabled"
      >
        <input
          checked=""
          class="ant-radio-input"
          disabled=""
          name="test-id"
          type="radio"
          value="1"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        A
      </span>
    </label>
    <label
      class="ant-radio-wrapper ant-radio-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-disabled"
      >
        <input
          class="ant-radio-input"
          disabled=""
          name="test-id"
          type="radio"
          value="2"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        B
      </span>
    </label>
    <label
      class="ant-radio-wrapper ant-radio-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-disabled"
      >
        <input
          class="ant-radio-input"
          disabled=""
          name="test-id"
          type="radio"
          value="3"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        C
      </span>
    </label>
    <label
      class="ant-radio-wrapper ant-radio-wrapper-disabled css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-disabled"
      >
        <input
          class="ant-radio-input"
          disabled=""
          name="test-id"
          type="radio"
          value="4"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        D
      </span>
    </label>
  </div>,
]
`;
