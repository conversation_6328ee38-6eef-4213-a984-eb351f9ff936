// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/image/demo/basic.tsx correctly 1`] = `
<div
  class="ant-image css-var-test-id ant-image-css-var"
  style="width:200px"
>
  <img
    alt="basic"
    class="ant-image-img"
    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
    width="200"
  />
  <div
    class="ant-image-cover ant-image-cover-center"
  />
</div>
`;

exports[`renders components/image/demo/component-token.tsx correctly 1`] = `
Array [
  <div
    class="ant-image css-var-test-id ant-image-css-var"
    style="width:150px"
  >
    <img
      alt="svg image"
      class="ant-image-img"
      src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg"
      width="150"
    />
    <div
      class="ant-image-cover ant-image-cover-center"
    />
  </div>,
  <div
    class="ant-image css-var-test-id ant-image-css-var"
    style="width:150px"
  >
    <img
      alt="basic image"
      class="ant-image-img"
      src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
      width="150"
    />
    <div
      class="ant-image-cover ant-image-cover-center"
    />
  </div>,
]
`;

exports[`renders components/image/demo/controlled-preview.tsx correctly 1`] = `
Array [
  <div>
    scaleStep:
    <!-- -->
    <div
      class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          aria-valuemax="5"
          aria-valuemin="0.1"
          aria-valuenow="0.5"
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="0.1"
          value="0.5"
        />
      </div>
    </div>
  </div>,
  <br />,
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      show image preview
    </span>
  </button>,
  <div
    class="ant-image css-var-test-id ant-image-css-var"
    style="width:200px"
  >
    <img
      alt="basic image"
      class="ant-image-img"
      src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?x-oss-process=image/blur,r_50,s_50/quality,q_1/resize,m_mfit,h_200,w_200"
      style="display:none"
      width="200"
    />
    <div
      class="ant-image-cover ant-image-cover-center"
      style="display:none"
    />
  </div>,
]
`;

exports[`renders components/image/demo/coverPlacement.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center css-var-test-id"
  style="column-gap:16px;row-gap:16px"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-image css-var-test-id ant-image-css-var"
      style="width:96px"
    >
      <img
        alt="basic image"
        class="ant-image-img"
        src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
        width="96"
      />
      <div
        class="ant-image-cover ant-image-cover-center"
      >
        <div
          class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        >
          <div
            class="ant-space-item"
          >
            <span
              aria-label="zoom-in"
              class="anticon anticon-zoom-in"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="zoom-in"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-space-item"
          >
            center
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-image css-var-test-id ant-image-css-var"
      style="width:96px"
    >
      <img
        alt="image"
        class="ant-image-img"
        src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
        width="96"
      />
      <div
        class="ant-image-cover ant-image-cover-top"
      >
        <div
          class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        >
          <div
            class="ant-space-item"
          >
            <span
              aria-label="zoom-in"
              class="anticon anticon-zoom-in"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="zoom-in"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-space-item"
          >
            top
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-image css-var-test-id ant-image-css-var"
      style="width:96px"
    >
      <img
        alt="image"
        class="ant-image-img"
        src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
        width="96"
      />
      <div
        class="ant-image-cover ant-image-cover-bottom"
      >
        <div
          class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        >
          <div
            class="ant-space-item"
          >
            <span
              aria-label="zoom-in"
              class="anticon anticon-zoom-in"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="zoom-in"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"
                />
              </svg>
            </span>
          </div>
          <div
            class="ant-space-item"
          >
            bottom
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/image/demo/fallback.tsx correctly 1`] = `
<div
  class="ant-image css-var-test-id ant-image-css-var"
  style="width:200px;height:200px"
>
  <img
    alt="basic image"
    class="ant-image-img"
    height="200"
    src="error"
    style="height:200px"
    width="200"
  />
  <div
    class="ant-image-cover ant-image-cover-center"
  />
</div>
`;

exports[`renders components/image/demo/imageRender.tsx correctly 1`] = `
<div
  class="ant-image css-var-test-id ant-image-css-var"
  style="width:200px"
>
  <img
    alt="basic image"
    class="ant-image-img"
    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
    width="200"
  />
  <div
    class="ant-image-cover ant-image-cover-center"
  />
</div>
`;

exports[`renders components/image/demo/mask.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-image css-var-test-id ant-image-css-var"
      style="width:100px"
    >
      <img
        alt="Default blur"
        class="ant-image-img"
        src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
        width="100"
      />
      <div
        class="ant-image-cover ant-image-cover-center"
      >
        <div
          class="ant-space ant-space-vertical ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        >
          <div
            class="ant-space-item"
          >
            Default blur
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-image css-var-test-id ant-image-css-var"
      style="width:100px"
    >
      <img
        alt="Dimmed mask"
        class="ant-image-img"
        src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
        width="100"
      />
      <div
        class="ant-image-cover ant-image-cover-center"
      >
        <div
          class="ant-space ant-space-vertical ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        >
          <div
            class="ant-space-item"
          >
            Dimmed mask
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-image css-var-test-id ant-image-css-var"
      style="width:100px"
    >
      <img
        alt="No mask"
        class="ant-image-img"
        src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg"
        width="100"
      />
      <div
        class="ant-image-cover ant-image-cover-center"
      >
        <div
          class="ant-space ant-space-vertical ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        >
          <div
            class="ant-space-item"
          >
            No mask
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/image/demo/nested.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    showModal
  </span>
</button>
`;

exports[`renders components/image/demo/placeholder.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center css-var-test-id"
  style="column-gap:12px;row-gap:12px"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-image css-var-test-id ant-image-css-var"
      style="width:200px"
    >
      <img
        alt="basic image"
        class="ant-image-img"
        src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?1479772800000"
        width="200"
      />
      <div
        aria-hidden="true"
        class="ant-image-placeholder"
      >
        <div
          class="ant-image css-var-test-id ant-image-css-var"
          style="width:200px"
        >
          <img
            alt="placeholder image"
            class="ant-image-img"
            src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?x-oss-process=image/blur,r_50,s_50/quality,q_1/resize,m_mfit,h_200,w_200"
            width="200"
          />
        </div>
      </div>
      <div
        class="ant-image-cover ant-image-cover-center"
      />
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Reload
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/image/demo/preview-group.tsx correctly 1`] = `
Array [
  <div
    class="ant-image css-var-test-id ant-image-css-var"
    style="width:200px"
  >
    <img
      alt="svg image"
      class="ant-image-img"
      src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
      width="200"
    />
    <div
      class="ant-image-cover ant-image-cover-center"
    />
  </div>,
  <div
    class="ant-image css-var-test-id ant-image-css-var"
    style="width:200px"
  >
    <img
      alt="svg image"
      class="ant-image-img"
      src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg"
      width="200"
    />
    <div
      class="ant-image-cover ant-image-cover-center"
    />
  </div>,
]
`;

exports[`renders components/image/demo/preview-group-top-progress.tsx correctly 1`] = `
Array [
  <div
    class="ant-image css-var-test-id ant-image-css-var"
    style="width:150px"
  >
    <img
      alt="svg image"
      class="ant-image-img"
      src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
      width="150"
    />
    <div
      class="ant-image-cover ant-image-cover-center"
    />
  </div>,
  <div
    class="ant-image css-var-test-id ant-image-css-var"
    style="width:150px"
  >
    <img
      alt="svg image"
      class="ant-image-img"
      src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg"
      width="150"
    />
    <div
      class="ant-image-cover ant-image-cover-center"
    />
  </div>,
  <div
    class="ant-image css-var-test-id ant-image-css-var"
    style="width:150px"
  >
    <img
      alt="svg image"
      class="ant-image-img"
      src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
      width="150"
    />
    <div
      class="ant-image-cover ant-image-cover-center"
    />
  </div>,
]
`;

exports[`renders components/image/demo/preview-group-visible.tsx correctly 1`] = `
<div
  class="ant-image css-var-test-id ant-image-css-var"
  style="width:200px"
>
  <img
    alt="webp image"
    class="ant-image-img"
    src="https://gw.alipayobjects.com/zos/antfincdn/LlvErxo8H9/photo-1503185912284-5271ff81b9a8.webp"
    width="200"
  />
  <div
    class="ant-image-cover ant-image-cover-center"
  />
</div>
`;

exports[`renders components/image/demo/preview-imgInfo.tsx correctly 1`] = `
<div
  class="ant-image css-var-test-id ant-image-css-var"
  style="width:200px;height:200px"
>
  <img
    alt="test"
    class="ant-image-img"
    height="200px"
    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
    style="height:200px"
    width="200px"
  />
  <div
    class="ant-image-cover ant-image-cover-center"
  />
</div>
`;

exports[`renders components/image/demo/preview-mask.tsx correctly 1`] = `
<div
  class="ant-image css-var-test-id ant-image-css-var"
  style="width:96px"
>
  <img
    alt="basic image"
    class="ant-image-img"
    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
    width="96"
  />
  <div
    class="ant-image-cover ant-image-cover-center"
  >
    <div
      class="ant-space ant-space-vertical ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    >
      <div
        class="ant-space-item"
      >
        <span
          aria-label="zoom-in"
          class="anticon anticon-zoom-in"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="zoom-in"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-space-item"
      >
        示例
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/image/demo/previewSrc.tsx correctly 1`] = `
<div
  class="ant-image css-var-test-id ant-image-css-var"
  style="width:200px"
>
  <img
    alt="basic image"
    class="ant-image-img"
    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?x-oss-process=image/blur,r_50,s_50/quality,q_1/resize,m_mfit,h_200,w_200"
    width="200"
  />
  <div
    class="ant-image-cover ant-image-cover-center"
  />
</div>
`;

exports[`renders components/image/demo/toolbarRender.tsx correctly 1`] = `
Array [
  <div
    class="ant-image css-var-test-id ant-image-css-var"
    style="width:200px"
  >
    <img
      alt="image-0"
      class="ant-image-img"
      src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
      width="200"
    />
    <div
      class="ant-image-cover ant-image-cover-center"
    />
  </div>,
  <div
    class="ant-image css-var-test-id ant-image-css-var"
    style="width:200px"
  >
    <img
      alt="image-1"
      class="ant-image-img"
      src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg"
      width="200"
    />
    <div
      class="ant-image-cover ant-image-cover-center"
    />
  </div>,
]
`;
