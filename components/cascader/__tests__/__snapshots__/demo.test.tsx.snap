// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/cascader/demo/basic.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity:0"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      >
        Please select
      </span>
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/change-on-select.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity:0"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/component-token.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity:0"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      >
        Please select
      </span>
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/custom-dropdown.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity:0"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      >
        Please select
      </span>
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/custom-render.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  style="width:100%"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity:0"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-item"
      >
        <span>
          Zhejiang
          <!-- -->
           / 
        </span>
        <span>
          Hangzhou
          <!-- -->
           / 
        </span>
        <span>
          West Lake
          <!-- -->
           (
          <a>
            752100
          </a>
          )
        </span>
      </span>
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
  <span
    aria-hidden="true"
    class="ant-select-clear"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="close-circle"
      class="anticon anticon-close-circle"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="close-circle"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.***********.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/custom-trigger.tsx correctly 1`] = `
<span>
  Unselect
  <!-- -->
  <a>
    Change city
  </a>
</span>
`;

exports[`renders components/cascader/demo/default-value.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity:0"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-item"
        title="Zhejiang / Hangzhou / West Lake"
      >
        Zhejiang / Hangzhou / West Lake
      </span>
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
  <span
    aria-hidden="true"
    class="ant-select-clear"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="close-circle"
      class="anticon anticon-close-circle"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="close-circle"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.***********.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/disabled-option.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity:0"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/fields-name.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity:0"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      >
        Please select
      </span>
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/hover.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity:0"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/lazy.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity:0"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/multiple.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-multiple ant-select-allow-clear ant-select-show-arrow"
  style="width:100%"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <div
        class="ant-select-selection-overflow"
      >
        <div
          class="ant-select-selection-overflow-item ant-select-selection-overflow-item-suffix"
          style="opacity:1;order:0"
        >
          <div
            class="ant-select-selection-search"
            style="width:0"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              readonly=""
              role="combobox"
              style="opacity:0"
              type="search"
              unselectable="on"
              value=""
            />
            <span
              aria-hidden="true"
              class="ant-select-selection-search-mirror"
            >
            </span>
          </div>
        </div>
      </div>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/panel.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-flex-start ant-flex-gap-small ant-flex-vertical"
>
  <button
    aria-checked="false"
    aria-label="disabled switch"
    class="ant-switch css-var-test-id"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      >
        Enabled
      </span>
      <span
        class="ant-switch-inner-unchecked"
      >
        Disabled
      </span>
    </span>
  </button>
  <div
    class="ant-cascader-panel css-var-test-id ant-cascader-css-var"
  >
    <div
      class="ant-cascader-menus"
    >
      <ul
        class="ant-cascader-menu"
        role="menu"
      >
        <li
          aria-checked="false"
          class="ant-cascader-menu-item ant-cascader-menu-item-expand"
          data-path-key="zhejiang"
          role="menuitemcheckbox"
          title="Zhejiang"
        >
          <div
            class="ant-cascader-menu-item-content"
          >
            Zhejiang
          </div>
          <div
            class="ant-cascader-menu-item-expand-icon"
          >
            <span
              aria-label="right"
              class="anticon anticon-right"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
        </li>
        <li
          aria-checked="false"
          class="ant-cascader-menu-item ant-cascader-menu-item-expand"
          data-path-key="jiangsu"
          role="menuitemcheckbox"
          title="Jiangsu"
        >
          <div
            class="ant-cascader-menu-item-content"
          >
            Jiangsu
          </div>
          <div
            class="ant-cascader-menu-item-expand-icon"
          >
            <span
              aria-label="right"
              class="anticon anticon-right"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
        </li>
      </ul>
    </div>
  </div>
  <div
    class="ant-cascader-panel css-var-test-id ant-cascader-css-var"
  >
    <div
      class="ant-cascader-menus"
    >
      <ul
        class="ant-cascader-menu"
        role="menu"
      >
        <li
          aria-checked="false"
          class="ant-cascader-menu-item ant-cascader-menu-item-expand"
          data-path-key="zhejiang"
          role="menuitemcheckbox"
          title="Zhejiang"
        >
          <span
            class="ant-cascader-checkbox"
          >
            <span
              class="ant-cascader-checkbox-inner"
            />
          </span>
          <div
            class="ant-cascader-menu-item-content"
          >
            Zhejiang
          </div>
          <div
            class="ant-cascader-menu-item-expand-icon"
          >
            <span
              aria-label="right"
              class="anticon anticon-right"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
        </li>
        <li
          aria-checked="false"
          class="ant-cascader-menu-item ant-cascader-menu-item-expand"
          data-path-key="jiangsu"
          role="menuitemcheckbox"
          title="Jiangsu"
        >
          <span
            class="ant-cascader-checkbox"
          >
            <span
              class="ant-cascader-checkbox-inner"
            />
          </span>
          <div
            class="ant-cascader-menu-item-content"
          >
            Jiangsu
          </div>
          <div
            class="ant-cascader-menu-item-expand-icon"
          >
            <span
              aria-label="right"
              class="anticon anticon-right"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
        </li>
      </ul>
    </div>
  </div>
  <div
    class="ant-cascader-panel ant-cascader-panel-empty css-var-test-id ant-cascader-css-var"
  >
    <div
      class="css-var-test-id ant-empty ant-empty-normal ant-empty-small"
    >
      <div
        class="ant-empty-image"
      >
        <svg
          height="41"
          viewBox="0 0 64 41"
          width="64"
          xmlns="http://www.w3.org/2000/svg"
        >
          <title>
            No data
          </title>
          <g
            fill="none"
            fill-rule="evenodd"
            transform="translate(0 1)"
          >
            <ellipse
              cx="32"
              cy="33"
              fill="#f5f5f5"
              rx="32"
              ry="7"
            />
            <g
              fill-rule="nonzero"
              stroke="#d9d9d9"
            >
              <path
                d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
              />
              <path
                d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                fill="#fafafa"
              />
            </g>
          </g>
        </svg>
      </div>
      <div
        class="ant-empty-description"
      >
        No data
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/cascader/demo/placement.tsx correctly 1`] = `
Array [
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="topLeft"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        topLeft
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="topRight"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        topRight
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="bottomLeft"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        bottomLeft
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="bottomRight"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        bottomRight
      </span>
    </label>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
]
`;

exports[`renders components/cascader/demo/render-panel.tsx correctly 1`] = `
<div
  style="padding-bottom:0;position:relative;min-width:0"
>
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
    style="margin:0"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/cascader/demo/search.tsx correctly 1`] = `
<div
  class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow ant-select-show-search"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          role="combobox"
          type="search"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      >
        Please select
      </span>
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select:none;-webkit-user-select:none"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/cascader/demo/showCheckedStrategy.tsx correctly 1`] = `
Array [
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-multiple ant-select-allow-clear ant-select-show-arrow"
    style="width:100%"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <div
          class="ant-select-selection-overflow"
        >
          <div
            aria-hidden="true"
            class="ant-select-selection-overflow-item ant-select-selection-overflow-item-rest"
            style="opacity:0;height:0;overflow-y:hidden;order:9007199254740991;pointer-events:none;position:absolute"
          >
            <span
              class="ant-select-selection-item"
              title="+ 2 ..."
            >
              <span
                class="ant-select-selection-item-content"
              >
                + 2 ...
              </span>
            </span>
          </div>
          <div
            class="ant-select-selection-overflow-item ant-select-selection-overflow-item-suffix"
            style="opacity:1;order:0"
          >
            <div
              class="ant-select-selection-search"
              style="width:0"
            >
              <input
                aria-autocomplete="list"
                aria-controls="test-id_list"
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-owns="test-id_list"
                autocomplete="off"
                class="ant-select-selection-search-input"
                id="test-id"
                readonly=""
                role="combobox"
                style="opacity:0"
                type="search"
                unselectable="on"
                value=""
              />
              <span
                aria-hidden="true"
                class="ant-select-selection-search-mirror"
              >
              </span>
            </div>
          </div>
        </div>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
    <span
      aria-hidden="true"
      class="ant-select-clear"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="close-circle"
        class="anticon anticon-close-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close-circle"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.***********.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-multiple ant-select-allow-clear ant-select-show-arrow"
    style="width:100%"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <div
          class="ant-select-selection-overflow"
        >
          <div
            aria-hidden="true"
            class="ant-select-selection-overflow-item ant-select-selection-overflow-item-rest"
            style="opacity:0;height:0;overflow-y:hidden;order:9007199254740991;pointer-events:none;position:absolute"
          >
            <span
              class="ant-select-selection-item"
              title="+ 0 ..."
            >
              <span
                class="ant-select-selection-item-content"
              >
                + 0 ...
              </span>
            </span>
          </div>
          <div
            class="ant-select-selection-overflow-item ant-select-selection-overflow-item-suffix"
            style="opacity:1;order:0"
          >
            <div
              class="ant-select-selection-search"
              style="width:0"
            >
              <input
                aria-autocomplete="list"
                aria-controls="test-id_list"
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-owns="test-id_list"
                autocomplete="off"
                class="ant-select-selection-search-input"
                id="test-id"
                readonly=""
                role="combobox"
                style="opacity:0"
                type="search"
                unselectable="on"
                value=""
              />
              <span
                aria-hidden="true"
                class="ant-select-selection-search-mirror"
              >
              </span>
            </div>
          </div>
        </div>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
    <span
      aria-hidden="true"
      class="ant-select-clear"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="close-circle"
        class="anticon anticon-close-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close-circle"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.***********.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
          />
        </svg>
      </span>
    </span>
  </div>,
]
`;

exports[`renders components/cascader/demo/size.tsx correctly 1`] = `
Array [
  <div
    class="ant-select ant-cascader ant-select-lg ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        />
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        />
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-cascader ant-select-sm ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        />
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <br />,
  <br />,
]
`;

exports[`renders components/cascader/demo/status.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-select ant-cascader ant-select-outlined ant-select-status-error ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              readonly=""
              role="combobox"
              style="opacity:0"
              type="search"
              unselectable="on"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-placeholder"
          >
            Error
          </span>
        </span>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select:none;-webkit-user-select:none"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-select ant-cascader ant-select-outlined ant-select-status-warning ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-multiple ant-select-allow-clear ant-select-show-arrow"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <div
            class="ant-select-selection-overflow"
          >
            <div
              class="ant-select-selection-overflow-item ant-select-selection-overflow-item-suffix"
              style="opacity:1"
            >
              <div
                class="ant-select-selection-search"
                style="width:0"
              >
                <input
                  aria-autocomplete="list"
                  aria-controls="test-id_list"
                  aria-expanded="false"
                  aria-haspopup="listbox"
                  aria-owns="test-id_list"
                  autocomplete="off"
                  class="ant-select-selection-search-input"
                  id="test-id"
                  readonly=""
                  role="combobox"
                  style="opacity:0"
                  type="search"
                  unselectable="on"
                  value=""
                />
                <span
                  aria-hidden="true"
                  class="ant-select-selection-search-mirror"
                >
                </span>
              </div>
            </div>
          </div>
          <span
            class="ant-select-selection-placeholder"
          >
            Warning multiple
          </span>
        </span>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select:none;-webkit-user-select:none"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/cascader/demo/suffix.tsx correctly 1`] = `
Array [
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="smile"
        class="anticon anticon-smile"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="smile"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      ab
    </span>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <div
        class="ant-select-prefix"
      >
        <span
          aria-label="smile"
          class="anticon anticon-smile"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="smile"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
]
`;

exports[`renders components/cascader/demo/variant.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-select ant-cascader ant-select-borderless ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-select ant-cascader ant-select-filled ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-select ant-cascader ant-select-underlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity:0"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Please select
        </span>
      </span>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select:none;-webkit-user-select:none"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;
