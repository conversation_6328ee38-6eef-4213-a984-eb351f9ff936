// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`TimeLine rtl render component should be rendered correctly in RTL direction 1`] = `
<ol
  class="ant-steps ant-steps-vertical ant-steps-title-horizontal ant-steps-outlined ant-steps-dot ant-steps-rtl ant-timeline css-var-root ant-timeline-rtl css-var-root"
  style="--steps-items-offset: 0;"
/>
`;

exports[`TimeLine rtl render component should be rendered correctly in RTL direction 2`] = `null`;
