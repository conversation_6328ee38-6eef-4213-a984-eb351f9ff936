// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/grid/demo/basic.tsx correctly 1`] = `
Array [
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-24 css-var-test-id"
    >
      col
    </div>
  </div>,
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-12 css-var-test-id"
    >
      col-12
    </div>
    <div
      class="ant-col ant-col-12 css-var-test-id"
    >
      col-12
    </div>
  </div>,
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-8 css-var-test-id"
    >
      col-8
    </div>
    <div
      class="ant-col ant-col-8 css-var-test-id"
    >
      col-8
    </div>
    <div
      class="ant-col ant-col-8 css-var-test-id"
    >
      col-8
    </div>
  </div>,
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-6 css-var-test-id"
    >
      col-6
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
    >
      col-6
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
    >
      col-6
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
    >
      col-6
    </div>
  </div>,
]
`;

exports[`renders components/grid/demo/flex.tsx correctly 1`] = `
Array [
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      sub-element align left
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row ant-row-start css-var-test-id"
  >
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      sub-element align center
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row ant-row-center css-var-test-id"
  >
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      sub-element align right
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row ant-row-end css-var-test-id"
  >
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      sub-element monospaced arrangement
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row ant-row-space-between css-var-test-id"
  >
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      sub-element align full
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row ant-row-space-around css-var-test-id"
  >
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      sub-element align evenly
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row ant-row-space-evenly css-var-test-id"
  >
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      col-4
    </div>
  </div>,
]
`;

exports[`renders components/grid/demo/flex-align.tsx correctly 1`] = `
Array [
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Align Top
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row ant-row-center ant-row-top css-var-test-id"
  >
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-100"
      >
        col-4
      </p>
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-50"
      >
        col-4
      </p>
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-120"
      >
        col-4
      </p>
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-80"
      >
        col-4
      </p>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Align Middle
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row ant-row-space-around ant-row-middle css-var-test-id"
  >
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-100"
      >
        col-4
      </p>
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-50"
      >
        col-4
      </p>
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-120"
      >
        col-4
      </p>
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-80"
      >
        col-4
      </p>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Align Bottom
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row ant-row-space-between ant-row-bottom css-var-test-id"
  >
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-100"
      >
        col-4
      </p>
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-50"
      >
        col-4
      </p>
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-120"
      >
        col-4
      </p>
    </div>
    <div
      class="ant-col ant-col-4 css-var-test-id"
    >
      <p
        class="height-80"
      >
        col-4
      </p>
    </div>
  </div>,
]
`;

exports[`renders components/grid/demo/flex-order.tsx correctly 1`] = `
Array [
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Normal
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-6 ant-col-order-4 css-var-test-id"
    >
      1 col-order-4
    </div>
    <div
      class="ant-col ant-col-6 ant-col-order-3 css-var-test-id"
    >
      2 col-order-3
    </div>
    <div
      class="ant-col ant-col-6 ant-col-order-2 css-var-test-id"
    >
      3 col-order-2
    </div>
    <div
      class="ant-col ant-col-6 ant-col-order-1 css-var-test-id"
    >
      4 col-order-1
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Responsive
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-6 ant-col-xs-order-1 ant-col-sm-order-2 ant-col-md-order-3 ant-col-lg-order-4 css-var-test-id"
    >
      1 col-order-responsive
    </div>
    <div
      class="ant-col ant-col-6 ant-col-xs-order-2 ant-col-sm-order-1 ant-col-md-order-4 ant-col-lg-order-3 css-var-test-id"
    >
      2 col-order-responsive
    </div>
    <div
      class="ant-col ant-col-6 ant-col-xs-order-3 ant-col-sm-order-4 ant-col-md-order-2 ant-col-lg-order-1 css-var-test-id"
    >
      3 col-order-responsive
    </div>
    <div
      class="ant-col ant-col-6 ant-col-xs-order-4 ant-col-sm-order-3 ant-col-md-order-1 ant-col-lg-order-2 css-var-test-id"
    >
      4 col-order-responsive
    </div>
  </div>,
]
`;

exports[`renders components/grid/demo/flex-stretch.tsx correctly 1`] = `
Array [
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Percentage columns
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col css-var-test-id"
      style="flex:2 2 auto"
    >
      2 / 5
    </div>
    <div
      class="ant-col css-var-test-id"
      style="flex:3 3 auto"
    >
      3 / 5
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Fill rest
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col css-var-test-id"
      style="flex:0 0 100px"
    >
      100px
    </div>
    <div
      class="ant-col css-var-test-id"
      style="flex:1 1 auto"
    >
      Fill Rest
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Raw flex style
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col css-var-test-id"
      style="flex:1 1 200px"
    >
      1 1 200px
    </div>
    <div
      class="ant-col css-var-test-id"
      style="flex:0 1 300px"
    >
      0 1 300px
    </div>
  </div>,
  <div
    class="ant-row ant-row-no-wrap css-var-test-id"
  >
    <div
      class="ant-col css-var-test-id"
      style="flex:none;min-width:0"
    >
      <div
        style="padding:0 16px"
      >
        none
      </div>
    </div>
    <div
      class="ant-col css-var-test-id"
      style="flex:1 1 auto;min-width:0"
    >
      auto with no-wrap
    </div>
  </div>,
]
`;

exports[`renders components/grid/demo/gutter.tsx correctly 1`] = `
Array [
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Horizontal
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row css-var-test-id"
    style="margin-inline:-8px"
  >
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Responsive
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row css-var-test-id"
    style="margin-inline:-16px"
  >
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:16px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:16px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:16px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:16px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Vertical
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row css-var-test-id"
    style="margin-inline:-8px;row-gap:24px"
  >
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:8px"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Gutter(string)
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-row css-var-test-id"
    style="margin-inline:calc(2rem / -2)"
  >
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:calc(2rem / 2)"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:calc(2rem / 2)"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:calc(2rem / 2)"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
    <div
      class="ant-col ant-col-6 gutter-row css-var-test-id"
      style="padding-inline:calc(2rem / 2)"
    >
      <div
        style="background:#0092ff;padding:8px 0"
      >
        col-6
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/grid/demo/offset.tsx correctly 1`] = `
Array [
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-8 css-var-test-id"
    >
      col-8
    </div>
    <div
      class="ant-col ant-col-8 ant-col-offset-8 css-var-test-id"
    >
      col-8
    </div>
  </div>,
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-6 ant-col-offset-6 css-var-test-id"
    >
      col-6 col-offset-6
    </div>
    <div
      class="ant-col ant-col-6 ant-col-offset-6 css-var-test-id"
    >
      col-6 col-offset-6
    </div>
  </div>,
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-12 ant-col-offset-6 css-var-test-id"
    >
      col-12 col-offset-6
    </div>
  </div>,
]
`;

exports[`renders components/grid/demo/playground.tsx correctly 1`] = `
Array [
  <span>
    Horizontal Gutter (px): 
  </span>,
  <div
    style="width:50%"
  >
    <div
      class="ant-slider css-var-test-id ant-slider-horizontal ant-slider-with-marks"
    >
      <div
        class="ant-slider-rail"
      />
      <div
        class="ant-slider-track"
        style="left:0%;width:20%"
      />
      <div
        class="ant-slider-step"
      >
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="left:0%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="left:20%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:40%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:60%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:80%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:100%;transform:translateX(-50%)"
        />
      </div>
      <div
        aria-describedby="test-id"
        aria-disabled="false"
        aria-orientation="horizontal"
        aria-valuemax="5"
        aria-valuemin="0"
        aria-valuenow="1"
        class="ant-slider-handle"
        role="slider"
        style="left:20%;transform:translateX(-50%)"
        tabindex="0"
      />
      <div
        class="ant-slider-mark"
      >
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="left:0%;transform:translateX(-50%)"
        >
          8
        </span>
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="left:20%;transform:translateX(-50%)"
        >
          16
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:40%;transform:translateX(-50%)"
        >
          24
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:60%;transform:translateX(-50%)"
        >
          32
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:80%;transform:translateX(-50%)"
        >
          40
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:100%;transform:translateX(-50%)"
        >
          48
        </span>
      </div>
    </div>
  </div>,
  <span>
    Vertical Gutter (px): 
  </span>,
  <div
    style="width:50%"
  >
    <div
      class="ant-slider css-var-test-id ant-slider-horizontal ant-slider-with-marks"
    >
      <div
        class="ant-slider-rail"
      />
      <div
        class="ant-slider-track"
        style="left:0%;width:20%"
      />
      <div
        class="ant-slider-step"
      >
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="left:0%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="left:20%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:40%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:60%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:80%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:100%;transform:translateX(-50%)"
        />
      </div>
      <div
        aria-describedby="test-id"
        aria-disabled="false"
        aria-orientation="horizontal"
        aria-valuemax="5"
        aria-valuemin="0"
        aria-valuenow="1"
        class="ant-slider-handle"
        role="slider"
        style="left:20%;transform:translateX(-50%)"
        tabindex="0"
      />
      <div
        class="ant-slider-mark"
      >
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="left:0%;transform:translateX(-50%)"
        >
          8
        </span>
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="left:20%;transform:translateX(-50%)"
        >
          16
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:40%;transform:translateX(-50%)"
        >
          24
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:60%;transform:translateX(-50%)"
        >
          32
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:80%;transform:translateX(-50%)"
        >
          40
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:100%;transform:translateX(-50%)"
        >
          48
        </span>
      </div>
    </div>
  </div>,
  <span>
    Column Count:
  </span>,
  <div
    style="width:50%;margin-bottom:48px"
  >
    <div
      class="ant-slider css-var-test-id ant-slider-horizontal ant-slider-with-marks"
    >
      <div
        class="ant-slider-rail"
      />
      <div
        class="ant-slider-track"
        style="left:0%;width:40%"
      />
      <div
        class="ant-slider-step"
      >
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="left:0%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="left:20%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="left:40%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:60%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:80%;transform:translateX(-50%)"
        />
        <span
          class="ant-slider-dot"
          style="left:100%;transform:translateX(-50%)"
        />
      </div>
      <div
        aria-describedby="test-id"
        aria-disabled="false"
        aria-orientation="horizontal"
        aria-valuemax="5"
        aria-valuemin="0"
        aria-valuenow="2"
        class="ant-slider-handle"
        role="slider"
        style="left:40%;transform:translateX(-50%)"
        tabindex="0"
      />
      <div
        class="ant-slider-mark"
      >
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="left:0%;transform:translateX(-50%)"
        >
          2
        </span>
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="left:20%;transform:translateX(-50%)"
        >
          3
        </span>
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="left:40%;transform:translateX(-50%)"
        >
          4
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:60%;transform:translateX(-50%)"
        >
          6
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:80%;transform:translateX(-50%)"
        >
          8
        </span>
        <span
          class="ant-slider-mark-text"
          style="left:100%;transform:translateX(-50%)"
        >
          12
        </span>
      </div>
    </div>
  </div>,
  <div
    class="ant-row css-var-test-id"
    style="margin-inline:-8px;row-gap:16px"
  >
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
  </div>,
  Another Row:,
  <div
    class="ant-row css-var-test-id"
    style="margin-inline:-8px;row-gap:16px"
  >
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
      style="padding-inline:8px"
    >
      <div>
        Column
      </div>
    </div>
  </div>,
  <pre
    class="demo-code"
  >
    &lt;Row gutter={[16, 16]}&gt;
  &lt;Col span={6} /&gt;
  &lt;Col span={6} /&gt;
  &lt;Col span={6} /&gt;
  &lt;Col span={6} /&gt;
  &lt;Col span={6} /&gt;
  &lt;Col span={6} /&gt;
  &lt;Col span={6} /&gt;
  &lt;Col span={6} /&gt;
&lt;/Row&gt;
  </pre>,
  <pre
    class="demo-code"
  >
    &lt;Row gutter={[16, 16]}&gt;
  &lt;Col span={6} /&gt;
  &lt;Col span={6} /&gt;
  &lt;Col span={6} /&gt;
  &lt;Col span={6} /&gt;
&lt;/Row&gt;
  </pre>,
]
`;

exports[`renders components/grid/demo/responsive.tsx correctly 1`] = `
<div
  class="ant-row css-var-test-id"
>
  <div
    class="ant-col ant-col-xs-2 ant-col-sm-4 ant-col-md-6 ant-col-lg-8 ant-col-xl-10 css-var-test-id"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-20 ant-col-sm-16 ant-col-md-12 ant-col-lg-8 ant-col-xl-4 css-var-test-id"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-2 ant-col-sm-4 ant-col-md-6 ant-col-lg-8 ant-col-xl-10 css-var-test-id"
  >
    Col
  </div>
</div>
`;

exports[`renders components/grid/demo/responsive-flex.tsx correctly 1`] = `
<div
  class="ant-row css-var-test-id"
>
  <div
    class="ant-col ant-col-xs-flex ant-col-sm-flex ant-col-md-flex ant-col-lg-flex ant-col-xl-flex css-var-test-id"
    style="--ant-col-xs-flex:0 0 100%;--ant-col-sm-flex:0 0 50%;--ant-col-md-flex:0 0 40%;--ant-col-lg-flex:0 0 20%;--ant-col-xl-flex:0 0 10%"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-flex ant-col-sm-flex ant-col-md-flex ant-col-lg-flex ant-col-xl-flex css-var-test-id"
    style="--ant-col-xs-flex:0 0 100%;--ant-col-sm-flex:0 0 50%;--ant-col-md-flex:0 0 40%;--ant-col-lg-flex:0 0 20%;--ant-col-xl-flex:0 0 10%"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-flex ant-col-sm-flex ant-col-md-flex ant-col-lg-flex ant-col-xl-flex css-var-test-id"
    style="--ant-col-xs-flex:0 0 100%;--ant-col-sm-flex:0 0 50%;--ant-col-md-flex:0 0 40%;--ant-col-lg-flex:0 0 20%;--ant-col-xl-flex:0 0 10%"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-flex ant-col-sm-flex ant-col-md-flex ant-col-lg-flex ant-col-xl-flex css-var-test-id"
    style="--ant-col-xs-flex:0 0 100%;--ant-col-sm-flex:0 0 50%;--ant-col-md-flex:0 0 40%;--ant-col-lg-flex:0 0 20%;--ant-col-xl-flex:0 0 10%"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-flex ant-col-sm-flex ant-col-md-flex ant-col-lg-flex ant-col-xl-flex css-var-test-id"
    style="--ant-col-xs-flex:0 0 100%;--ant-col-sm-flex:0 0 50%;--ant-col-md-flex:0 0 40%;--ant-col-lg-flex:0 0 20%;--ant-col-xl-flex:0 0 10%"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-flex ant-col-sm-flex ant-col-md-flex ant-col-lg-flex ant-col-xl-flex css-var-test-id"
    style="--ant-col-xs-flex:0 0 100%;--ant-col-sm-flex:0 0 50%;--ant-col-md-flex:0 0 40%;--ant-col-lg-flex:0 0 20%;--ant-col-xl-flex:0 0 10%"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-flex ant-col-sm-flex ant-col-md-flex ant-col-lg-flex ant-col-xl-flex css-var-test-id"
    style="--ant-col-xs-flex:0 0 100%;--ant-col-sm-flex:0 0 50%;--ant-col-md-flex:0 0 40%;--ant-col-lg-flex:0 0 20%;--ant-col-xl-flex:0 0 10%"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-flex ant-col-sm-flex ant-col-md-flex ant-col-lg-flex ant-col-xl-flex css-var-test-id"
    style="--ant-col-xs-flex:0 0 100%;--ant-col-sm-flex:0 0 50%;--ant-col-md-flex:0 0 40%;--ant-col-lg-flex:0 0 20%;--ant-col-xl-flex:0 0 10%"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-flex ant-col-sm-flex ant-col-md-flex ant-col-lg-flex ant-col-xl-flex css-var-test-id"
    style="--ant-col-xs-flex:0 0 100%;--ant-col-sm-flex:0 0 50%;--ant-col-md-flex:0 0 40%;--ant-col-lg-flex:0 0 20%;--ant-col-xl-flex:0 0 10%"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-flex ant-col-sm-flex ant-col-md-flex ant-col-lg-flex ant-col-xl-flex css-var-test-id"
    style="--ant-col-xs-flex:0 0 100%;--ant-col-sm-flex:0 0 50%;--ant-col-md-flex:0 0 40%;--ant-col-lg-flex:0 0 20%;--ant-col-xl-flex:0 0 10%"
  >
    Col
  </div>
</div>
`;

exports[`renders components/grid/demo/responsive-more.tsx correctly 1`] = `
<div
  class="ant-row css-var-test-id"
>
  <div
    class="ant-col ant-col-xs-5 ant-col-xs-offset-1 ant-col-lg-6 ant-col-lg-offset-2 css-var-test-id"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-11 ant-col-xs-offset-1 ant-col-lg-6 ant-col-lg-offset-2 css-var-test-id"
  >
    Col
  </div>
  <div
    class="ant-col ant-col-xs-5 ant-col-xs-offset-1 ant-col-lg-6 ant-col-lg-offset-2 css-var-test-id"
  >
    Col
  </div>
</div>
`;

exports[`renders components/grid/demo/sort.tsx correctly 1`] = `
<div
  class="ant-row css-var-test-id"
>
  <div
    class="ant-col ant-col-18 ant-col-push-6 css-var-test-id"
  >
    col-18 col-push-6
  </div>
  <div
    class="ant-col ant-col-6 ant-col-pull-18 css-var-test-id"
  >
    col-6 col-pull-18
  </div>
</div>
`;

exports[`renders components/grid/demo/useBreakpoint.tsx correctly 1`] = `
Array [
  Current break point:,
  <!-- -->,
   ,
]
`;
