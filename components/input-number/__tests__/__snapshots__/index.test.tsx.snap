// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`InputNumber renders correctly when controls has custom upIcon and downIcon 1`] = `
<div
  class="ant-input-number ant-input-number-outlined css-var-root ant-input-number-css-var"
>
  <div
    class="ant-input-number-handler-wrap"
  >
    <span
      aria-disabled="false"
      aria-label="Increase Value"
      class="ant-input-number-handler ant-input-number-handler-up"
      role="button"
      unselectable="on"
    >
      <span
        class="ant-input-number-handler-up-inner"
      >
        <span
          aria-label="arrow-up"
          class="anticon anticon-arrow-up"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="arrow-up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"
            />
          </svg>
        </span>
      </span>
    </span>
    <span
      aria-disabled="false"
      aria-label="Decrease Value"
      class="ant-input-number-handler ant-input-number-handler-down"
      role="button"
      unselectable="on"
    >
      <span
        class="ant-input-number-handler-down-inner"
      >
        <span
          aria-label="arrow-down"
          class="anticon anticon-arrow-down"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="arrow-down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"
            />
          </svg>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-input-number-input-wrap"
  >
    <input
      autocomplete="off"
      class="ant-input-number-input"
      role="spinbutton"
      step="1"
      value=""
    />
  </div>
</div>
`;

exports[`InputNumber renders correctly when controls is {} 1`] = `
<div
  class="ant-input-number ant-input-number-outlined css-var-root ant-input-number-css-var"
>
  <div
    class="ant-input-number-handler-wrap"
  >
    <span
      aria-disabled="false"
      aria-label="Increase Value"
      class="ant-input-number-handler ant-input-number-handler-up"
      role="button"
      unselectable="on"
    >
      <span
        aria-label="up"
        class="anticon anticon-up ant-input-number-handler-up-inner"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="up"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
          />
        </svg>
      </span>
    </span>
    <span
      aria-disabled="false"
      aria-label="Decrease Value"
      class="ant-input-number-handler ant-input-number-handler-down"
      role="button"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-input-number-handler-down-inner"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-input-number-input-wrap"
  >
    <input
      autocomplete="off"
      class="ant-input-number-input"
      role="spinbutton"
      step="1"
      value=""
    />
  </div>
</div>
`;

exports[`InputNumber renders correctly when controls is boolean 1`] = `
<div
  class="ant-input-number ant-input-number-outlined css-var-root ant-input-number-css-var"
>
  <div
    class="ant-input-number-input-wrap"
  >
    <input
      autocomplete="off"
      class="ant-input-number-input"
      role="spinbutton"
      step="1"
      value=""
    />
  </div>
</div>
`;

exports[`InputNumber rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-input-number ant-input-number-rtl ant-input-number-outlined css-var-root ant-input-number-css-var"
>
  <div
    class="ant-input-number-handler-wrap"
  >
    <span
      aria-disabled="false"
      aria-label="Increase Value"
      class="ant-input-number-handler ant-input-number-handler-up"
      role="button"
      unselectable="on"
    >
      <span
        aria-label="up"
        class="anticon anticon-up ant-input-number-handler-up-inner"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="up"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
          />
        </svg>
      </span>
    </span>
    <span
      aria-disabled="false"
      aria-label="Decrease Value"
      class="ant-input-number-handler ant-input-number-handler-down"
      role="button"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-input-number-handler-down-inner"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-input-number-input-wrap"
  >
    <input
      autocomplete="off"
      class="ant-input-number-input"
      role="spinbutton"
      step="1"
      value=""
    />
  </div>
</div>
`;
