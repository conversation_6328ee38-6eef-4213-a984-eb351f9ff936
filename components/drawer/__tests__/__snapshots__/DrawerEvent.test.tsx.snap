// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Drawer render correctly 1`] = `
<div
  class="ant-drawer ant-drawer-right css-var-root ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-motion-leave ant-drawer-mask-motion-leave-start ant-drawer-mask-motion ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper ant-drawer-panel-motion-right-leave ant-drawer-panel-motion-right-leave-start ant-drawer-panel-motion-right"
    style="width: 378px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-header ant-drawer-header-close-only"
      >
        <div
          class="ant-drawer-header-title"
        >
          <button
            aria-label="Close"
            class="ant-drawer-close"
            type="button"
          >
            <span
              aria-label="close"
              class="anticon anticon-close"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
      <div
        class="ant-drawer-body"
      >
        Here is content of Drawer
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;
