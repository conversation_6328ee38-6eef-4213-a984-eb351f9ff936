// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/drawer/demo/basic-right.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open
  </span>
</button>
`;

exports[`renders components/drawer/demo/classNames.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Open
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        ConfigProvider
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/drawer/demo/closable-placement.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open
  </span>
</button>
`;

exports[`renders components/drawer/demo/component-token.tsx correctly 1`] = `
<div
  style="padding:32px;background:#e6e6e6"
>
  <div
    class="ant-drawer ant-drawer-pure ant-drawer-right css-var-test-id"
    style="height:300px"
  >
    <div
      class="ant-drawer-header"
    >
      <div
        class="ant-drawer-header-title"
      >
        <button
          aria-label="Close"
          class="ant-drawer-close"
          type="button"
        >
          <span
            aria-label="close"
            class="anticon anticon-close"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </button>
        <div
          class="ant-drawer-title"
        >
          Hello Title
        </div>
      </div>
    </div>
    <div
      class="ant-drawer-body"
    >
      Hello Content
    </div>
    <div
      class="ant-drawer-footer"
    >
      Footer!
    </div>
  </div>
</div>
`;

exports[`renders components/drawer/demo/config-provider.tsx correctly 1`] = `
<div
  class="site-drawer-render-in-current-wrapper"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Open
    </span>
  </button>
</div>
`;

exports[`renders components/drawer/demo/extra.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
    >
      <label
        class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target"
        >
          <input
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="top"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          top
        </span>
      </label>
      <label
        class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target ant-radio-checked"
        >
          <input
            checked=""
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="right"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          right
        </span>
      </label>
      <label
        class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target"
        >
          <input
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="bottom"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          bottom
        </span>
      </label>
      <label
        class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target"
        >
          <input
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="left"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          left
        </span>
      </label>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Open
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/drawer/demo/form-in-drawer.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span
    class="ant-btn-icon"
  >
    <span
      aria-label="plus"
      class="anticon anticon-plus"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="plus"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"
        />
        <path
          d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
        />
      </svg>
    </span>
  </span>
  <span>
    New account
  </span>
</button>
`;

exports[`renders components/drawer/demo/loading.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open Drawer
  </span>
</button>
`;

exports[`renders components/drawer/demo/mask.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="flex-wrap:wrap"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Default blur
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  />
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Dimmed mask
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  />
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        No mask
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  />
</div>
`;

exports[`renders components/drawer/demo/multi-level-drawer.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open drawer
  </span>
</button>
`;

exports[`renders components/drawer/demo/no-mask.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open
  </span>
</button>
`;

exports[`renders components/drawer/demo/placement.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
    >
      <label
        class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target"
        >
          <input
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="top"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          top
        </span>
      </label>
      <label
        class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target"
        >
          <input
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="right"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          right
        </span>
      </label>
      <label
        class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target"
        >
          <input
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="bottom"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          bottom
        </span>
      </label>
      <label
        class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target ant-radio-checked"
        >
          <input
            checked=""
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="left"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          left
        </span>
      </label>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Open
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/drawer/demo/render-in-current.tsx correctly 1`] = `
<div
  style="position:relative;height:200px;padding:48px;overflow:hidden;background:rgba(0,0,0,0.02);border:1px solid #f0f0f0;border-radius:8px"
>
  Render in this
  <div
    style="margin-top:16px"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Open
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/drawer/demo/render-panel.tsx correctly 1`] = `
<div
  style="padding:32px;background:#e6e6e6"
>
  <div
    class="ant-drawer ant-drawer-pure ant-drawer-right css-var-test-id"
    style="height:300px"
  >
    <div
      class="ant-drawer-header"
    >
      <div
        class="ant-drawer-header-title"
      >
        <button
          aria-label="Close"
          class="ant-drawer-close"
          type="button"
        >
          <span
            aria-label="close"
            class="anticon anticon-close"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </button>
        <div
          class="ant-drawer-title"
        >
          Hello Title
        </div>
      </div>
    </div>
    <div
      class="ant-drawer-body"
    >
      Hello Content
    </div>
    <div
      class="ant-drawer-footer"
    >
      Footer!
    </div>
  </div>
</div>
`;

exports[`renders components/drawer/demo/scroll-debug.tsx correctly 1`] = `
<div
  style="position:relative;z-index:999999"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      <button
        aria-checked="false"
        class="ant-switch css-var-test-id"
        role="switch"
        type="button"
      >
        <div
          class="ant-switch-handle"
        />
        <span
          class="ant-switch-inner"
        >
          <span
            class="ant-switch-inner-checked"
          >
            Drawer
          </span>
          <span
            class="ant-switch-inner-unchecked"
          >
            Drawer
          </span>
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        aria-checked="false"
        class="ant-switch css-var-test-id"
        role="switch"
        type="button"
      >
        <div
          class="ant-switch-handle"
        />
        <span
          class="ant-switch-inner"
        >
          <span
            class="ant-switch-inner-checked"
          >
            Drawer2
          </span>
          <span
            class="ant-switch-inner-unchecked"
          >
            Drawer2
          </span>
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        aria-checked="false"
        class="ant-switch css-var-test-id"
        role="switch"
        type="button"
      >
        <div
          class="ant-switch-handle"
        />
        <span
          class="ant-switch-inner"
        >
          <span
            class="ant-switch-inner-checked"
          >
            Modal
          </span>
          <span
            class="ant-switch-inner-unchecked"
          >
            Modal
          </span>
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        aria-checked="false"
        class="ant-switch css-var-test-id"
        role="switch"
        type="button"
      >
        <div
          class="ant-switch-handle"
        />
        <span
          class="ant-switch-inner"
        >
          <span
            class="ant-switch-inner-checked"
          >
            Modal2
          </span>
          <span
            class="ant-switch-inner-unchecked"
          >
            Modal2
          </span>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`renders components/drawer/demo/size.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Open Default Size (378px)
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Open Large Size (736px)
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/drawer/demo/user-profile.tsx correctly 1`] = `
<div
  class="ant-list ant-list-split ant-list-bordered css-var-test-id"
>
  <div
    class="ant-spin-nested-loading css-var-test-id"
  >
    <div
      class="ant-spin-container"
    >
      <ul
        class="ant-list-items"
      >
        <li
          class="ant-list-item"
        >
          <div
            class="ant-list-item-meta"
          >
            <div
              class="ant-list-item-meta-avatar"
            >
              <span
                class="ant-avatar ant-avatar-circle ant-avatar-image css-var-test-id ant-avatar-css-var"
              >
                <img
                  src="https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"
                />
              </span>
            </div>
            <div
              class="ant-list-item-meta-content"
            >
              <h4
                class="ant-list-item-meta-title"
              >
                <a
                  href="https://ant.design/index-cn"
                >
                  Lily
                </a>
              </h4>
              <div
                class="ant-list-item-meta-description"
              >
                Progresser XTech
              </div>
            </div>
          </div>
          <ul
            class="ant-list-item-action"
          >
            <li>
              <a>
                View Profile
              </a>
            </li>
          </ul>
        </li>
        <li
          class="ant-list-item"
        >
          <div
            class="ant-list-item-meta"
          >
            <div
              class="ant-list-item-meta-avatar"
            >
              <span
                class="ant-avatar ant-avatar-circle ant-avatar-image css-var-test-id ant-avatar-css-var"
              >
                <img
                  src="https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"
                />
              </span>
            </div>
            <div
              class="ant-list-item-meta-content"
            >
              <h4
                class="ant-list-item-meta-title"
              >
                <a
                  href="https://ant.design/index-cn"
                >
                  Lily
                </a>
              </h4>
              <div
                class="ant-list-item-meta-description"
              >
                Progresser XTech
              </div>
            </div>
          </div>
          <ul
            class="ant-list-item-action"
          >
            <li>
              <a>
                View Profile
              </a>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</div>
`;
