// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Drawer Drawer loading have a spinner 1`] = `
<div
  class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="width: 378px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-header ant-drawer-header-close-only"
      >
        <div
          class="ant-drawer-header-title"
        >
          <button
            aria-label="Close"
            class="ant-drawer-close"
            type="button"
          >
            <span
              aria-label="close"
              class="anticon anticon-close"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
      <div
        class="ant-drawer-body"
      >
        <div
          class="ant-skeleton ant-skeleton-active ant-drawer-body-skeleton css-var-root"
        >
          <div
            class="ant-skeleton-section"
          >
            <ul
              class="ant-skeleton-paragraph"
            >
              <li />
              <li />
              <li />
              <li />
              <li
                style="width: 61%;"
              />
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;

exports[`Drawer Drawer mask blur className should support closable placement with end 1`] = `
<div
  class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="width: 378px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-header ant-drawer-header-close-only"
      >
        <div
          class="ant-drawer-header-title"
        />
        <button
          aria-label="Close"
          class="ant-drawer-close ant-drawer-close-end"
          type="button"
        >
          <span
            aria-label="close"
            class="anticon anticon-close"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </button>
      </div>
      <div
        class="ant-drawer-body"
      >
        Test
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;

exports[`Drawer Drawer mask blur className should support closable placement with start 1`] = `
<div
  class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="width: 378px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-header ant-drawer-header-close-only"
      >
        <div
          class="ant-drawer-header-title"
        >
          <button
            aria-label="Close"
            class="ant-drawer-close"
            type="button"
          >
            <span
              aria-label="close"
              class="anticon anticon-close"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
      <div
        class="ant-drawer-body"
      >
        Test
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;

exports[`Drawer className is test_drawer 1`] = `
<div
  class="ant-drawer ant-drawer-right test_drawer css-var-root ant-drawer-open ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="width: 378px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-header ant-drawer-header-close-only"
      >
        <div
          class="ant-drawer-header-title"
        >
          <button
            aria-label="Close"
            class="ant-drawer-close"
            type="button"
          >
            <span
              aria-label="close"
              class="anticon anticon-close"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
      <div
        class="ant-drawer-body"
      >
        Here is content of Drawer
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;

exports[`Drawer closable is false 1`] = `
<div
  class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="width: 378px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-body"
      >
        Here is content of Drawer
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;

exports[`Drawer destroyOnHidden is true 1`] = `null`;

exports[`Drawer getContainer return undefined 1`] = `<div />`;

exports[`Drawer getContainer return undefined 2`] = `
<div>
  <div
    class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
    tabindex="-1"
  >
    <div
      class="ant-drawer-mask ant-drawer-mask-blur"
    />
    <div
      aria-hidden="true"
      data-sentinel="start"
      style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
      tabindex="0"
    />
    <div
      class="ant-drawer-content-wrapper"
      style="width: 400px;"
    >
      <div
        aria-modal="true"
        class="ant-drawer-section"
        role="dialog"
      >
        <div
          class="ant-drawer-header ant-drawer-header-close-only"
        >
          <div
            class="ant-drawer-header-title"
          >
            <button
              aria-label="Close"
              class="ant-drawer-close"
              type="button"
            >
              <span
                aria-label="close"
                class="anticon anticon-close"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="ant-drawer-body"
        >
          Here is content of Drawer
        </div>
      </div>
    </div>
    <div
      aria-hidden="true"
      data-sentinel="end"
      style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
      tabindex="0"
    />
  </div>
</div>
`;

exports[`Drawer have a footer 1`] = `
<div
  class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="width: 378px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-header ant-drawer-header-close-only"
      >
        <div
          class="ant-drawer-header-title"
        >
          <button
            aria-label="Close"
            class="ant-drawer-close"
            type="button"
          >
            <span
              aria-label="close"
              class="anticon anticon-close"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
      <div
        class="ant-drawer-body"
      >
        Here is content of Drawer
      </div>
      <div
        class="ant-drawer-footer"
      >
        Test Footer
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;

exports[`Drawer have a title 1`] = `
<div
  class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="width: 378px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-header"
      >
        <div
          class="ant-drawer-header-title"
        >
          <button
            aria-label="Close"
            class="ant-drawer-close"
            type="button"
          >
            <span
              aria-label="close"
              class="anticon anticon-close"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </button>
          <div
            class="ant-drawer-title"
          >
            Test Title
          </div>
        </div>
      </div>
      <div
        class="ant-drawer-body"
      >
        Here is content of Drawer
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;

exports[`Drawer render correctly 1`] = `
<div
  class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="width: 400px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-header ant-drawer-header-close-only"
      >
        <div
          class="ant-drawer-header-title"
        >
          <button
            aria-label="Close"
            class="ant-drawer-close"
            type="button"
          >
            <span
              aria-label="close"
              class="anticon anticon-close"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
      <div
        class="ant-drawer-body"
      >
        Here is content of Drawer
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;

exports[`Drawer render top drawer 1`] = `
<div
  class="ant-drawer ant-drawer-top css-var-root ant-drawer-open ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="height: 400px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-header ant-drawer-header-close-only"
      >
        <div
          class="ant-drawer-header-title"
        >
          <button
            aria-label="Close"
            class="ant-drawer-close"
            type="button"
          >
            <span
              aria-label="close"
              class="anticon anticon-close"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
      <div
        class="ant-drawer-body"
      >
        Here is content of Drawer
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;

exports[`Drawer rtl render component should be rendered correctly in RTL direction 1`] = `null`;

exports[`Drawer style migrate match between styles and deprecated style prop 1`] = `
<div>
  <div
    class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
    tabindex="-1"
  >
    <div
      class="ant-drawer-mask ant-drawer-mask-motion-enter ant-drawer-mask-motion-enter-start ant-drawer-mask-motion ant-drawer-mask-blur"
      style="font-size: 15px;"
    />
    <div
      aria-hidden="true"
      data-sentinel="start"
      style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
      tabindex="0"
    />
    <div
      class="ant-drawer-content-wrapper ant-drawer-panel-motion-right-enter ant-drawer-panel-motion-right-enter-start ant-drawer-panel-motion-right"
      style="width: 378px; font-size: 14px;"
    >
      <div
        aria-modal="true"
        class="ant-drawer-section"
        role="dialog"
        style="font-size: 13px;"
      >
        <div
          class="ant-drawer-header ant-drawer-header-close-only"
          style="font-size: 10px;"
        >
          <div
            class="ant-drawer-header-title"
          >
            <button
              aria-label="Close"
              class="ant-drawer-close"
              type="button"
            >
              <span
                aria-label="close"
                class="anticon anticon-close"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="ant-drawer-body"
          style="font-size: 11px;"
        >
          <p>
            Some contents...
          </p>
        </div>
        <div
          class="ant-drawer-footer"
          style="font-size: 12px;"
        >
          footer
        </div>
      </div>
    </div>
    <div
      aria-hidden="true"
      data-sentinel="end"
      style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
      tabindex="0"
    />
  </div>
</div>
`;

exports[`Drawer style migrate match between styles and deprecated style prop 2`] = `
<div>
  <div
    class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
    tabindex="-1"
  >
    <div
      class="ant-drawer-mask ant-drawer-mask-motion-enter ant-drawer-mask-motion-enter-start ant-drawer-mask-motion ant-drawer-mask-blur"
      style="font-size: 15px;"
    />
    <div
      aria-hidden="true"
      data-sentinel="start"
      style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
      tabindex="0"
    />
    <div
      class="ant-drawer-content-wrapper ant-drawer-panel-motion-right-enter ant-drawer-panel-motion-right-enter-start ant-drawer-panel-motion-right"
      style="width: 378px; font-size: 14px;"
    >
      <div
        aria-modal="true"
        class="ant-drawer-section"
        role="dialog"
        style="font-size: 13px;"
      >
        <div
          class="ant-drawer-header ant-drawer-header-close-only"
          style="font-size: 10px;"
        >
          <div
            class="ant-drawer-header-title"
          >
            <button
              aria-label="Close"
              class="ant-drawer-close"
              type="button"
            >
              <span
                aria-label="close"
                class="anticon anticon-close"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="ant-drawer-body"
          style="font-size: 11px;"
        >
          <p>
            Some contents...
          </p>
        </div>
        <div
          class="ant-drawer-footer"
          style="font-size: 12px;"
        >
          footer
        </div>
      </div>
    </div>
    <div
      aria-hidden="true"
      data-sentinel="end"
      style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
      tabindex="0"
    />
  </div>
</div>
`;

exports[`Drawer style/drawerStyle/headerStyle/bodyStyle should work 1`] = `
<div
  class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
  style="background-color: rgb(0, 136, 204);"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="width: 378px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
      style="background-color: rgb(0, 136, 204);"
    >
      <div
        class="ant-drawer-header ant-drawer-header-close-only"
        style="background-color: rgb(0, 136, 204);"
      >
        <div
          class="ant-drawer-header-title"
        >
          <button
            aria-label="Close"
            class="ant-drawer-close"
            type="button"
          >
            <span
              aria-label="close"
              class="anticon anticon-close"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
      <div
        class="ant-drawer-body"
        style="background-color: rgb(0, 136, 204);"
      >
        Here is content of Drawer
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;

exports[`Drawer support closeIcon 1`] = `
<div
  class="ant-drawer ant-drawer-right css-var-root ant-drawer-open ant-drawer-inline"
  tabindex="-1"
>
  <div
    class="ant-drawer-mask ant-drawer-mask-blur"
  />
  <div
    aria-hidden="true"
    data-sentinel="start"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
  <div
    class="ant-drawer-content-wrapper"
    style="width: 400px;"
  >
    <div
      aria-modal="true"
      class="ant-drawer-section"
      role="dialog"
    >
      <div
        class="ant-drawer-header ant-drawer-header-close-only"
      >
        <div
          class="ant-drawer-header-title"
        >
          <button
            aria-label="Close"
            class="ant-drawer-close"
            type="button"
          >
            <span>
              close
            </span>
          </button>
        </div>
      </div>
      <div
        class="ant-drawer-body"
      >
        Here is content of Drawer
      </div>
    </div>
  </div>
  <div
    aria-hidden="true"
    data-sentinel="end"
    style="width: 0px; height: 0px; overflow: hidden; outline: none; position: absolute;"
    tabindex="0"
  />
</div>
`;
