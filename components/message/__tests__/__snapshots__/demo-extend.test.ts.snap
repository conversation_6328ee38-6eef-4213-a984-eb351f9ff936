// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/message/demo/component-token.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-message-notice ant-message-notice-pure-panel css-var-test-id ant-message-css-var"
  >
    <div
      class="ant-message-notice-content"
    >
      <div
        class="ant-message-custom-content ant-message-error"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
            />
          </svg>
        </span>
        <span
          class=""
        >
          Hello World!
        </span>
      </div>
    </div>
  </div>,
  <div
    class="ant-message-notice ant-message-notice-pure-panel css-var-test-id ant-message-css-var"
  >
    <div
      class="ant-message-notice-content"
    >
      <div
        class="ant-message-custom-content ant-message-error"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
            />
          </svg>
        </span>
        <span
          class=""
        >
          Hello World!
        </span>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/message/demo/component-token.tsx extend context correctly 2`] = `[]`;

exports[`renders components/message/demo/custom-style.tsx extend context correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    Customized style
  </span>
</button>
`;

exports[`renders components/message/demo/custom-style.tsx extend context correctly 2`] = `[]`;

exports[`renders components/message/demo/duration.tsx extend context correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    Customized display duration
  </span>
</button>
`;

exports[`renders components/message/demo/duration.tsx extend context correctly 2`] = `[]`;

exports[`renders components/message/demo/hooks.tsx extend context correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Display normal message
  </span>
</button>
`;

exports[`renders components/message/demo/hooks.tsx extend context correctly 2`] = `[]`;

exports[`renders components/message/demo/info.tsx extend context correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Static Method
  </span>
</button>
`;

exports[`renders components/message/demo/info.tsx extend context correctly 2`] = `[]`;

exports[`renders components/message/demo/loading.tsx extend context correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    Display a loading indicator
  </span>
</button>
`;

exports[`renders components/message/demo/loading.tsx extend context correctly 2`] = `[]`;

exports[`renders components/message/demo/other.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Success
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Error
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Warning
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/message/demo/other.tsx extend context correctly 2`] = `[]`;

exports[`renders components/message/demo/render-panel.tsx extend context correctly 1`] = `
<div
  class="ant-message-notice ant-message-notice-pure-panel css-var-test-id ant-message-css-var"
>
  <div
    class="ant-message-notice-content"
  >
    <div
      class="ant-message-custom-content ant-message-error"
    >
      <span
        aria-label="close-circle"
        class="anticon anticon-close-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close-circle"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
          />
        </svg>
      </span>
      <span
        class=""
      >
        Hello World!
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/message/demo/render-panel.tsx extend context correctly 2`] = `[]`;

exports[`renders components/message/demo/thenable.tsx extend context correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
  type="button"
>
  <span>
    Display sequential messages
  </span>
</button>
`;

exports[`renders components/message/demo/thenable.tsx extend context correctly 2`] = `[]`;

exports[`renders components/message/demo/update.tsx extend context correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open the message box
  </span>
</button>
`;

exports[`renders components/message/demo/update.tsx extend context correctly 2`] = `[]`;
