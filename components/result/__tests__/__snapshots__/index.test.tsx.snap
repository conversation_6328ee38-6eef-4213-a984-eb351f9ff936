// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Result rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-result ant-result-info ant-result-rtl css-var-root"
>
  <div
    class="ant-result-icon"
  >
    <span
      aria-label="exclamation-circle"
      class="anticon anticon-exclamation-circle"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="exclamation-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
        />
      </svg>
    </span>
  </div>
  <div
    class="ant-result-title"
  />
</div>
`;
