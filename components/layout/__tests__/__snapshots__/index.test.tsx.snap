// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Layout renders string width correctly 1`] = `
<aside
  class="ant-layout-sider ant-layout-sider-dark css-var-root"
  style="flex: 0 0 200px; max-width: 200px; min-width: 200px; width: 200px;"
>
  <div
    class="ant-layout-sider-children"
  >
    Sider
  </div>
</aside>
`;

exports[`Layout rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-layout ant-layout-rtl css-var-root"
/>
`;

exports[`Layout rtl render component should be rendered correctly in RTL direction 2`] = `
<main
  class="ant-layout-content"
/>
`;

exports[`Layout rtl render component should be rendered correctly in RTL direction 3`] = `
<aside
  class="ant-layout-sider ant-layout-sider-dark css-var-root"
  style="flex: 0 0 200px; max-width: 200px; min-width: 200px; width: 200px;"
>
  <div
    class="ant-layout-sider-children"
  />
</aside>
`;

exports[`Layout should be controlled by collapsed 1`] = `
<aside
  class="ant-layout-sider ant-layout-sider-dark css-var-root"
  style="flex: 0 0 200px; max-width: 200px; min-width: 200px; width: 200px;"
>
  <div
    class="ant-layout-sider-children"
  >
    Sider
  </div>
</aside>
`;

exports[`Layout should be controlled by collapsed 2`] = `
<aside
  class="ant-layout-sider ant-layout-sider-dark ant-layout-sider-collapsed css-var-root"
  style="max-width: 80px; min-width: 80px; width: 80px; flex: 0 0 80px;"
>
  <div
    class="ant-layout-sider-children"
  >
    Sider
  </div>
</aside>
`;
