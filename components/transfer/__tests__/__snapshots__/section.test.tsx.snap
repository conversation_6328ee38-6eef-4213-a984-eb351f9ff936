// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Transfer.List should render correctly 1`] = `
<div
  class="ant-transfer-section"
>
  <div
    class="ant-transfer-list-header"
  >
    <label
      class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-checkbox-indeterminate ant-wave-target"
      >
        <input
          class="ant-checkbox-input"
          type="checkbox"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
    </label>
    <span
      aria-label="down"
      class="anticon anticon-down ant-dropdown-trigger"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
    <span
      class="ant-transfer-list-header-selected"
    >
      1/3
    </span>
    <span
      class="ant-transfer-list-header-title"
    />
  </div>
  <div
    class="ant-transfer-list-body"
  >
    <ul
      class="ant-transfer-list-content"
    >
      <li
        class="ant-transfer-list-content-item ant-transfer-list-content-item-checked"
      >
        <label
          class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
        >
          <span
            class="ant-checkbox ant-wave-target ant-checkbox-checked"
          >
            <input
              checked=""
              class="ant-checkbox-input"
              type="checkbox"
            />
            <span
              class="ant-checkbox-inner"
            />
          </span>
        </label>
        <span
          class="ant-transfer-list-content-item-text"
        />
      </li>
      <li
        class="ant-transfer-list-content-item"
      >
        <label
          class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
        >
          <span
            class="ant-checkbox ant-wave-target"
          >
            <input
              class="ant-checkbox-input"
              type="checkbox"
            />
            <span
              class="ant-checkbox-inner"
            />
          </span>
        </label>
        <span
          class="ant-transfer-list-content-item-text"
        />
      </li>
      <li
        class="ant-transfer-list-content-item ant-transfer-list-content-item-disabled"
      >
        <label
          class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
        >
          <span
            class="ant-checkbox ant-wave-target ant-checkbox-disabled"
          >
            <input
              class="ant-checkbox-input"
              disabled=""
              type="checkbox"
            />
            <span
              class="ant-checkbox-inner"
            />
          </span>
        </label>
        <span
          class="ant-transfer-list-content-item-text"
        />
      </li>
    </ul>
  </div>
</div>
`;
