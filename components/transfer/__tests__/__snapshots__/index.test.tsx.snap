// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Transfer rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-transfer ant-transfer-rtl css-var-root"
>
  <div
    class="ant-transfer-section"
  >
    <div
      class="ant-transfer-list-header"
    >
      <label
        class="ant-checkbox-wrapper ant-checkbox-rtl ant-checkbox-wrapper-disabled ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target ant-checkbox-disabled"
        >
          <input
            class="ant-checkbox-input"
            disabled=""
            type="checkbox"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
      <span
        aria-label="down"
        class="anticon anticon-down ant-dropdown-trigger ant-dropdown-rtl"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
      <span
        class="ant-transfer-list-header-selected"
      >
        0
        item
      </span>
      <span
        class="ant-transfer-list-header-title"
      />
    </div>
    <div
      class="ant-transfer-list-body"
    >
      <div
        class="ant-transfer-list-body-not-found"
      >
        <div
          class="css-var-root ant-empty ant-empty-normal ant-empty-rtl ant-empty-small"
        >
          <div
            class="ant-empty-image"
          >
            <svg
              height="41"
              viewBox="0 0 64 41"
              width="64"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                No data
              </title>
              <g
                fill="none"
                fill-rule="evenodd"
                transform="translate(0 1)"
              >
                <ellipse
                  cx="32"
                  cy="33"
                  fill="#f5f5f5"
                  rx="32"
                  ry="7"
                />
                <g
                  fill-rule="nonzero"
                  stroke="#d9d9d9"
                >
                  <path
                    d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                  />
                  <path
                    d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                    fill="#fafafa"
                  />
                </g>
              </g>
            </svg>
          </div>
          <div
            class="ant-empty-description"
          >
            No data
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-transfer-actions"
  >
    <button
      class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only ant-btn-rtl"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="left"
          class="anticon anticon-left"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="left"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
            />
          </svg>
        </span>
      </span>
    </button>
    <button
      class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only ant-btn-rtl"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="right"
          class="anticon anticon-right"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </span>
    </button>
  </div>
  <div
    class="ant-transfer-section"
  >
    <div
      class="ant-transfer-list-header"
    >
      <label
        class="ant-checkbox-wrapper ant-checkbox-rtl ant-checkbox-wrapper-disabled ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target ant-checkbox-disabled"
        >
          <input
            class="ant-checkbox-input"
            disabled=""
            type="checkbox"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
      <span
        aria-label="down"
        class="anticon anticon-down ant-dropdown-trigger ant-dropdown-rtl"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
      <span
        class="ant-transfer-list-header-selected"
      >
        0
        item
      </span>
      <span
        class="ant-transfer-list-header-title"
      />
    </div>
    <div
      class="ant-transfer-list-body"
    >
      <div
        class="ant-transfer-list-body-not-found"
      >
        <div
          class="css-var-root ant-empty ant-empty-normal ant-empty-rtl ant-empty-small"
        >
          <div
            class="ant-empty-image"
          >
            <svg
              height="41"
              viewBox="0 0 64 41"
              width="64"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                No data
              </title>
              <g
                fill="none"
                fill-rule="evenodd"
                transform="translate(0 1)"
              >
                <ellipse
                  cx="32"
                  cy="33"
                  fill="#f5f5f5"
                  rx="32"
                  ry="7"
                />
                <g
                  fill-rule="nonzero"
                  stroke="#d9d9d9"
                >
                  <path
                    d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                  />
                  <path
                    d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                    fill="#fafafa"
                  />
                </g>
              </g>
            </svg>
          </div>
          <div
            class="ant-empty-description"
          >
            No data
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Transfer should render correctly 1`] = `
<div
  class="ant-transfer css-var-root"
>
  <div
    class="ant-transfer-section"
  >
    <div
      class="ant-transfer-list-header"
    >
      <label
        class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target ant-checkbox-checked"
        >
          <input
            checked=""
            class="ant-checkbox-input"
            type="checkbox"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
      <span
        aria-label="down"
        class="anticon anticon-down ant-dropdown-trigger"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
      <span
        class="ant-transfer-list-header-selected"
      >
        1/2
        items
      </span>
      <span
        class="ant-transfer-list-header-title"
      />
    </div>
    <div
      class="ant-transfer-list-body"
    >
      <ul
        class="ant-transfer-list-content"
      >
        <li
          class="ant-transfer-list-content-item ant-transfer-list-content-item-checked"
        >
          <label
            class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
          >
            <span
              class="ant-checkbox ant-wave-target ant-checkbox-checked"
            >
              <input
                checked=""
                class="ant-checkbox-input"
                type="checkbox"
              />
              <span
                class="ant-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="ant-transfer-list-content-item-text"
          />
        </li>
        <li
          class="ant-transfer-list-content-item ant-transfer-list-content-item-disabled"
        >
          <label
            class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
          >
            <span
              class="ant-checkbox ant-wave-target ant-checkbox-disabled"
            >
              <input
                class="ant-checkbox-input"
                disabled=""
                type="checkbox"
              />
              <span
                class="ant-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="ant-transfer-list-content-item-text"
          />
        </li>
      </ul>
    </div>
  </div>
  <div
    class="ant-transfer-actions"
  >
    <button
      class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="right"
          class="anticon anticon-right"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </span>
    </button>
    <button
      class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="left"
          class="anticon anticon-left"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="left"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
            />
          </svg>
        </span>
      </span>
    </button>
  </div>
  <div
    class="ant-transfer-section"
  >
    <div
      class="ant-transfer-list-header"
    >
      <label
        class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
      <span
        aria-label="down"
        class="anticon anticon-down ant-dropdown-trigger"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
      <span
        class="ant-transfer-list-header-selected"
      >
        1
        item
      </span>
      <span
        class="ant-transfer-list-header-title"
      />
    </div>
    <div
      class="ant-transfer-list-body"
    >
      <ul
        class="ant-transfer-list-content"
      >
        <li
          class="ant-transfer-list-content-item"
        >
          <label
            class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
          >
            <span
              class="ant-checkbox ant-wave-target"
            >
              <input
                class="ant-checkbox-input"
                type="checkbox"
              />
              <span
                class="ant-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="ant-transfer-list-content-item-text"
          />
        </li>
      </ul>
    </div>
  </div>
</div>
`;

exports[`Transfer should show sorted targetKey 1`] = `
<div
  class="ant-transfer css-var-root"
>
  <div
    class="ant-transfer-section"
  >
    <div
      class="ant-transfer-list-header"
    >
      <label
        class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
      <span
        aria-label="down"
        class="anticon anticon-down ant-dropdown-trigger"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
      <span
        class="ant-transfer-list-header-selected"
      >
        1
        item
      </span>
      <span
        class="ant-transfer-list-header-title"
      />
    </div>
    <div
      class="ant-transfer-list-body"
    >
      <ul
        class="ant-transfer-list-content"
      >
        <li
          class="ant-transfer-list-content-item"
          title="a"
        >
          <label
            class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
          >
            <span
              class="ant-checkbox ant-wave-target"
            >
              <input
                class="ant-checkbox-input"
                type="checkbox"
              />
              <span
                class="ant-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="ant-transfer-list-content-item-text"
          >
            a
          </span>
        </li>
      </ul>
    </div>
  </div>
  <div
    class="ant-transfer-actions"
  >
    <button
      class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="right"
          class="anticon anticon-right"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </span>
    </button>
    <button
      class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="left"
          class="anticon anticon-left"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="left"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
            />
          </svg>
        </span>
      </span>
    </button>
  </div>
  <div
    class="ant-transfer-section"
  >
    <div
      class="ant-transfer-list-header"
    >
      <label
        class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
      <span
        aria-label="down"
        class="anticon anticon-down ant-dropdown-trigger"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
      <span
        class="ant-transfer-list-header-selected"
      >
        2
        items
      </span>
      <span
        class="ant-transfer-list-header-title"
      />
    </div>
    <div
      class="ant-transfer-list-body"
    >
      <ul
        class="ant-transfer-list-content"
      >
        <li
          class="ant-transfer-list-content-item"
          title="c"
        >
          <label
            class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
          >
            <span
              class="ant-checkbox ant-wave-target"
            >
              <input
                class="ant-checkbox-input"
                type="checkbox"
              />
              <span
                class="ant-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="ant-transfer-list-content-item-text"
          >
            c
          </span>
        </li>
        <li
          class="ant-transfer-list-content-item"
          title="b"
        >
          <label
            class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
          >
            <span
              class="ant-checkbox ant-wave-target"
            >
              <input
                class="ant-checkbox-input"
                type="checkbox"
              />
              <span
                class="ant-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="ant-transfer-list-content-item-text"
          >
            b
          </span>
        </li>
      </ul>
    </div>
  </div>
</div>
`;

exports[`Transfer should support render value and label in item 1`] = `
<div
  class="ant-transfer css-var-root"
>
  <div
    class="ant-transfer-section"
  >
    <div
      class="ant-transfer-list-header"
    >
      <label
        class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
      <span
        aria-label="down"
        class="anticon anticon-down ant-dropdown-trigger"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
      <span
        class="ant-transfer-list-header-selected"
      >
        1
        item
      </span>
      <span
        class="ant-transfer-list-header-title"
      />
    </div>
    <div
      class="ant-transfer-list-body"
    >
      <ul
        class="ant-transfer-list-content"
      >
        <li
          class="ant-transfer-list-content-item"
          title="title value"
        >
          <label
            class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
          >
            <span
              class="ant-checkbox ant-wave-target"
            >
              <input
                class="ant-checkbox-input"
                type="checkbox"
              />
              <span
                class="ant-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="ant-transfer-list-content-item-text"
          >
            label
          </span>
        </li>
      </ul>
    </div>
  </div>
  <div
    class="ant-transfer-actions"
  >
    <button
      class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="right"
          class="anticon anticon-right"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </span>
    </button>
    <button
      class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="left"
          class="anticon anticon-left"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="left"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
            />
          </svg>
        </span>
      </span>
    </button>
  </div>
  <div
    class="ant-transfer-section"
  >
    <div
      class="ant-transfer-list-header"
    >
      <label
        class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target ant-checkbox-disabled"
        >
          <input
            class="ant-checkbox-input"
            disabled=""
            type="checkbox"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
      <span
        aria-label="down"
        class="anticon anticon-down ant-dropdown-trigger"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
      <span
        class="ant-transfer-list-header-selected"
      >
        0
        item
      </span>
      <span
        class="ant-transfer-list-header-title"
      />
    </div>
    <div
      class="ant-transfer-list-body"
    >
      <div
        class="ant-transfer-list-body-not-found"
      >
        <div
          class="css-var-root ant-empty ant-empty-normal ant-empty-small"
        >
          <div
            class="ant-empty-image"
          >
            <svg
              height="41"
              viewBox="0 0 64 41"
              width="64"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                No data
              </title>
              <g
                fill="none"
                fill-rule="evenodd"
                transform="translate(0 1)"
              >
                <ellipse
                  cx="32"
                  cy="33"
                  fill="#f5f5f5"
                  rx="32"
                  ry="7"
                />
                <g
                  fill-rule="nonzero"
                  stroke="#d9d9d9"
                >
                  <path
                    d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                  />
                  <path
                    d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                    fill="#fafafa"
                  />
                </g>
              </g>
            </svg>
          </div>
          <div
            class="ant-empty-description"
          >
            No data
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`immutable data dataSource is frozen 1`] = `
<div
  class="ant-transfer css-var-root"
>
  <div
    class="ant-transfer-section"
  >
    <div
      class="ant-transfer-list-header"
    >
      <label
        class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
      <span
        aria-label="down"
        class="anticon anticon-down ant-dropdown-trigger"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
      <span
        class="ant-transfer-list-header-selected"
      >
        1
        item
      </span>
      <span
        class="ant-transfer-list-header-title"
      />
    </div>
    <div
      class="ant-transfer-list-body"
    >
      <ul
        class="ant-transfer-list-content"
      >
        <li
          class="ant-transfer-list-content-item"
        >
          <label
            class="ant-checkbox-wrapper ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
          >
            <span
              class="ant-checkbox ant-wave-target"
            >
              <input
                class="ant-checkbox-input"
                type="checkbox"
              />
              <span
                class="ant-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="ant-transfer-list-content-item-text"
          />
        </li>
      </ul>
    </div>
  </div>
  <div
    class="ant-transfer-actions"
  >
    <button
      class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="right"
          class="anticon anticon-right"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </span>
    </button>
    <button
      class="ant-btn css-var-root ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only"
      disabled=""
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="left"
          class="anticon anticon-left"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="left"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
            />
          </svg>
        </span>
      </span>
    </button>
  </div>
  <div
    class="ant-transfer-section"
  >
    <div
      class="ant-transfer-list-header"
    >
      <label
        class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled ant-transfer-list-checkbox css-var-root ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target ant-checkbox-disabled"
        >
          <input
            class="ant-checkbox-input"
            disabled=""
            type="checkbox"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
      <span
        aria-label="down"
        class="anticon anticon-down ant-dropdown-trigger"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
      <span
        class="ant-transfer-list-header-selected"
      >
        0
        item
      </span>
      <span
        class="ant-transfer-list-header-title"
      />
    </div>
    <div
      class="ant-transfer-list-body"
    >
      <div
        class="ant-transfer-list-body-not-found"
      >
        <div
          class="css-var-root ant-empty ant-empty-normal ant-empty-small"
        >
          <div
            class="ant-empty-image"
          >
            <svg
              height="41"
              viewBox="0 0 64 41"
              width="64"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                No data
              </title>
              <g
                fill="none"
                fill-rule="evenodd"
                transform="translate(0 1)"
              >
                <ellipse
                  cx="32"
                  cy="33"
                  fill="#f5f5f5"
                  rx="32"
                  ry="7"
                />
                <g
                  fill-rule="nonzero"
                  stroke="#d9d9d9"
                >
                  <path
                    d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                  />
                  <path
                    d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                    fill="#fafafa"
                  />
                </g>
              </g>
            </svg>
          </div>
          <div
            class="ant-empty-description"
          >
            No data
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
