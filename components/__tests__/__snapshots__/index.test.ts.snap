// Jest <PERSON>napshot v1, https://jestjs.io/docs/snapshot-testing

exports[`antd exports modules correctly 1`] = `
[
  "Affix",
  "Alert",
  "Anchor",
  "App",
  "AutoComplete",
  "Avatar",
  "Badge",
  "Breadcrumb",
  "Button",
  "Calendar",
  "Card",
  "Carousel",
  "Cascader",
  "Checkbox",
  "Col",
  "Collapse",
  "ColorPicker",
  "ConfigProvider",
  "DatePicker",
  "Descriptions",
  "Divider",
  "Drawer",
  "Dropdown",
  "Empty",
  "Flex",
  "FloatButton",
  "Form",
  "Grid",
  "Image",
  "Input",
  "InputNumber",
  "Layout",
  "List",
  "Masonry",
  "Mentions",
  "Menu",
  "Modal",
  "Pagination",
  "Popconfirm",
  "Popover",
  "Progress",
  "QRCode",
  "Radio",
  "Rate",
  "Result",
  "Row",
  "Segmented",
  "Select",
  "Skeleton",
  "Slider",
  "Space",
  "Spin",
  "Splitter",
  "Statistic",
  "Steps",
  "Switch",
  "Table",
  "Tabs",
  "Tag",
  "TimePicker",
  "Timeline",
  "Tooltip",
  "Tour",
  "Transfer",
  "Tree",
  "TreeSelect",
  "Typography",
  "Upload",
  "Watermark",
  "message",
  "notification",
  "theme",
  "unstableSetRender",
  "version",
]
`;
