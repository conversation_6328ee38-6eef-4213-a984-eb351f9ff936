// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/slider/demo/basic.tsx correctly 1`] = `
Array [
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track"
      style="left:0%;width:30%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-describedby="test-id"
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="30"
      class="ant-slider-handle"
      role="slider"
      style="left:30%;transform:translateX(-50%)"
      tabindex="0"
    />
  </div>,
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track ant-slider-track-1"
      style="left:20%;width:30%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="20"
      class="ant-slider-handle ant-slider-handle-1"
      role="slider"
      style="left:20%;transform:translateX(-50%)"
      tabindex="0"
    />
    <div
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="50"
      class="ant-slider-handle ant-slider-handle-2"
      role="slider"
      style="left:50%;transform:translateX(-50%)"
      tabindex="0"
    />
  </div>,
  Disabled: ,
  <button
    aria-checked="false"
    class="ant-switch ant-switch-small css-var-test-id"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>,
]
`;

exports[`renders components/slider/demo/component-token.tsx correctly 1`] = `
Array [
  <div
    class="ant-slider css-var-test-id ant-slider-disabled ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track"
      style="left:0%;width:30%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-describedby="test-id"
      aria-disabled="true"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="30"
      class="ant-slider-handle"
      role="slider"
      style="left:30%;transform:translateX(-50%)"
    />
  </div>,
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track ant-slider-track-1 ant-slider-track-draggable"
      style="left:20%;width:30%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="20"
      class="ant-slider-handle ant-slider-handle-1"
      role="slider"
      style="left:20%;transform:translateX(-50%)"
      tabindex="0"
    />
    <div
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="50"
      class="ant-slider-handle ant-slider-handle-2"
      role="slider"
      style="left:50%;transform:translateX(-50%)"
      tabindex="0"
    />
  </div>,
  <div
    style="display:inline-block;height:300px;margin-inline-start:70px"
  >
    <div
      class="ant-slider css-var-test-id ant-slider-vertical"
    >
      <div
        class="ant-slider-rail"
      />
      <div
        class="ant-slider-track"
        style="bottom:0%;height:30%"
      />
      <div
        class="ant-slider-step"
      />
      <div
        aria-describedby="test-id"
        aria-disabled="false"
        aria-orientation="vertical"
        aria-valuemax="100"
        aria-valuemin="0"
        aria-valuenow="30"
        class="ant-slider-handle"
        role="slider"
        style="bottom:30%;transform:translateY(50%)"
        tabindex="0"
      />
    </div>
  </div>,
  <div
    style="display:inline-block;height:300px;margin-inline-start:70px"
  >
    <div
      class="ant-slider css-var-test-id ant-slider-vertical"
    >
      <div
        class="ant-slider-rail"
      />
      <div
        class="ant-slider-track ant-slider-track-1"
        style="bottom:20%;height:30%"
      />
      <div
        class="ant-slider-step"
      />
      <div
        aria-disabled="false"
        aria-orientation="vertical"
        aria-valuemax="100"
        aria-valuemin="0"
        aria-valuenow="20"
        class="ant-slider-handle ant-slider-handle-1"
        role="slider"
        style="bottom:20%;transform:translateY(50%)"
        tabindex="0"
      />
      <div
        aria-disabled="false"
        aria-orientation="vertical"
        aria-valuemax="100"
        aria-valuemin="0"
        aria-valuenow="50"
        class="ant-slider-handle ant-slider-handle-2"
        role="slider"
        style="bottom:50%;transform:translateY(50%)"
        tabindex="0"
      />
    </div>
  </div>,
  <div
    style="display:inline-block;height:300px;margin-inline-start:70px"
  >
    <div
      class="ant-slider css-var-test-id ant-slider-vertical ant-slider-with-marks"
    >
      <div
        class="ant-slider-rail"
      />
      <div
        class="ant-slider-track ant-slider-track-1"
        style="bottom:26%;height:11%"
      />
      <div
        class="ant-slider-step"
      >
        <span
          class="ant-slider-dot"
          style="bottom:0%;transform:translateY(50%)"
        />
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="bottom:26%;transform:translateY(50%)"
        />
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="bottom:37%;transform:translateY(50%)"
        />
        <span
          class="ant-slider-dot"
          style="bottom:100%;transform:translateY(50%)"
        />
      </div>
      <div
        aria-disabled="false"
        aria-orientation="vertical"
        aria-valuemax="100"
        aria-valuemin="0"
        aria-valuenow="26"
        class="ant-slider-handle ant-slider-handle-1"
        role="slider"
        style="bottom:26%;transform:translateY(50%)"
        tabindex="0"
      />
      <div
        aria-disabled="false"
        aria-orientation="vertical"
        aria-valuemax="100"
        aria-valuemin="0"
        aria-valuenow="37"
        class="ant-slider-handle ant-slider-handle-2"
        role="slider"
        style="bottom:37%;transform:translateY(50%)"
        tabindex="0"
      />
      <div
        class="ant-slider-mark"
      >
        <span
          class="ant-slider-mark-text"
          style="bottom:0%;transform:translateY(50%)"
        >
          0°C
        </span>
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="bottom:26%;transform:translateY(50%)"
        >
          26°C
        </span>
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="bottom:37%;transform:translateY(50%)"
        >
          37°C
        </span>
        <span
          class="ant-slider-mark-text"
          style="bottom:100%;transform:translateY(50%);color:#f50"
        >
          <strong>
            100°C
          </strong>
        </span>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/slider/demo/draggableTrack.tsx correctly 1`] = `
<div
  class="ant-slider css-var-test-id ant-slider-horizontal"
>
  <div
    class="ant-slider-rail"
  />
  <div
    class="ant-slider-track ant-slider-track-1 ant-slider-track-draggable"
    style="left:20%;width:30%"
  />
  <div
    class="ant-slider-step"
  />
  <div
    aria-disabled="false"
    aria-orientation="horizontal"
    aria-valuemax="100"
    aria-valuemin="0"
    aria-valuenow="20"
    class="ant-slider-handle ant-slider-handle-1"
    role="slider"
    style="left:20%;transform:translateX(-50%)"
    tabindex="0"
  />
  <div
    aria-disabled="false"
    aria-orientation="horizontal"
    aria-valuemax="100"
    aria-valuemin="0"
    aria-valuenow="50"
    class="ant-slider-handle ant-slider-handle-2"
    role="slider"
    style="left:50%;transform:translateX(-50%)"
    tabindex="0"
  />
</div>
`;

exports[`renders components/slider/demo/editable.tsx correctly 1`] = `
<div
  class="ant-slider css-var-test-id ant-slider-horizontal"
>
  <div
    class="ant-slider-rail"
  />
  <div
    class="ant-slider-track ant-slider-track-1"
    style="left:20%;width:60%"
  />
  <div
    class="ant-slider-step"
  />
  <div
    aria-disabled="false"
    aria-orientation="horizontal"
    aria-valuemax="100"
    aria-valuemin="0"
    aria-valuenow="20"
    class="ant-slider-handle ant-slider-handle-1"
    role="slider"
    style="left:20%;transform:translateX(-50%)"
    tabindex="0"
  />
  <div
    aria-disabled="false"
    aria-orientation="horizontal"
    aria-valuemax="100"
    aria-valuemin="0"
    aria-valuenow="80"
    class="ant-slider-handle ant-slider-handle-2"
    role="slider"
    style="left:80%;transform:translateX(-50%)"
    tabindex="0"
  />
</div>
`;

exports[`renders components/slider/demo/event.tsx correctly 1`] = `
Array [
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track"
      style="left:0%;width:30%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-describedby="test-id"
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="30"
      class="ant-slider-handle"
      role="slider"
      style="left:30%;transform:translateX(-50%)"
      tabindex="0"
    />
  </div>,
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track ant-slider-track-1"
      style="left:20%;width:30%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="20"
      class="ant-slider-handle ant-slider-handle-1"
      role="slider"
      style="left:20%;transform:translateX(-50%)"
      tabindex="0"
    />
    <div
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="50"
      class="ant-slider-handle ant-slider-handle-2"
      role="slider"
      style="left:50%;transform:translateX(-50%)"
      tabindex="0"
    />
  </div>,
]
`;

exports[`renders components/slider/demo/icon-slider.tsx correctly 1`] = `
<div
  class="icon-wrapper"
>
  <span
    aria-label="frown"
    class="anticon anticon-frown icon-wrapper-active"
    role="img"
  >
    <svg
      aria-hidden="true"
      data-icon="frown"
      fill="currentColor"
      focusable="false"
      height="1em"
      viewBox="64 64 896 896"
      width="1em"
    >
      <path
        d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"
      />
    </svg>
  </span>
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track"
      style="left:0%;width:0%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-describedby="test-id"
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="20"
      aria-valuemin="0"
      aria-valuenow="0"
      class="ant-slider-handle"
      role="slider"
      style="left:0%;transform:translateX(-50%)"
      tabindex="0"
    />
  </div>
  <span
    aria-label="smile"
    class="anticon anticon-smile"
    role="img"
  >
    <svg
      aria-hidden="true"
      data-icon="smile"
      fill="currentColor"
      focusable="false"
      height="1em"
      viewBox="64 64 896 896"
      width="1em"
    >
      <path
        d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
      />
    </svg>
  </span>
</div>
`;

exports[`renders components/slider/demo/input-number.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="width:100%"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-row css-var-test-id"
    >
      <div
        class="ant-col ant-col-12 css-var-test-id"
      >
        <div
          class="ant-slider css-var-test-id ant-slider-horizontal"
        >
          <div
            class="ant-slider-rail"
          />
          <div
            class="ant-slider-track"
            style="left:0%;width:0%"
          />
          <div
            class="ant-slider-step"
          />
          <div
            aria-describedby="test-id"
            aria-disabled="false"
            aria-orientation="horizontal"
            aria-valuemax="20"
            aria-valuemin="1"
            aria-valuenow="1"
            class="ant-slider-handle"
            role="slider"
            style="left:0%;transform:translateX(-50%)"
            tabindex="0"
          />
        </div>
      </div>
      <div
        class="ant-col ant-col-4 css-var-test-id"
      >
        <div
          class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
          style="margin:0 16px"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="true"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down ant-input-number-handler-down-disabled"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              aria-valuemax="20"
              aria-valuemin="1"
              aria-valuenow="1"
              autocomplete="off"
              class="ant-input-number-input"
              role="spinbutton"
              step="1"
              value="1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-row css-var-test-id"
    >
      <div
        class="ant-col ant-col-12 css-var-test-id"
      >
        <div
          class="ant-slider css-var-test-id ant-slider-horizontal"
        >
          <div
            class="ant-slider-rail"
          />
          <div
            class="ant-slider-track"
            style="left:0%;width:0%"
          />
          <div
            class="ant-slider-step"
          />
          <div
            aria-describedby="test-id"
            aria-disabled="false"
            aria-orientation="horizontal"
            aria-valuemax="1"
            aria-valuemin="0"
            aria-valuenow="0"
            class="ant-slider-handle"
            role="slider"
            style="left:0%;transform:translateX(-50%)"
            tabindex="0"
          />
        </div>
      </div>
      <div
        class="ant-col ant-col-4 css-var-test-id"
      >
        <div
          class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
          style="margin:0 16px"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="true"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down ant-input-number-handler-down-disabled"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              aria-valuemax="1"
              aria-valuemin="0"
              aria-valuenow="0"
              autocomplete="off"
              class="ant-input-number-input"
              role="spinbutton"
              step="0.01"
              value="0.00"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/slider/demo/mark.tsx correctly 1`] = `
Array [
  <h4>
    included=true
  </h4>,
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal ant-slider-with-marks"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track"
      style="left:0%;width:37%"
    />
    <div
      class="ant-slider-step"
    >
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:0%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:26%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:37%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot"
        style="left:100%;transform:translateX(-50%)"
      />
    </div>
    <div
      aria-describedby="test-id"
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="37"
      class="ant-slider-handle"
      role="slider"
      style="left:37%;transform:translateX(-50%)"
      tabindex="0"
    />
    <div
      class="ant-slider-mark"
    >
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:0%;transform:translateX(-50%)"
      >
        0°C
      </span>
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:26%;transform:translateX(-50%)"
      >
        26°C
      </span>
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:37%;transform:translateX(-50%)"
      >
        37°C
      </span>
      <span
        class="ant-slider-mark-text"
        style="left:100%;transform:translateX(-50%);color:#f50"
      >
        <strong>
          100°C
        </strong>
      </span>
    </div>
  </div>,
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal ant-slider-with-marks"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track ant-slider-track-1"
      style="left:26%;width:11%"
    />
    <div
      class="ant-slider-step"
    >
      <span
        class="ant-slider-dot"
        style="left:0%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:26%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:37%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot"
        style="left:100%;transform:translateX(-50%)"
      />
    </div>
    <div
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="26"
      class="ant-slider-handle ant-slider-handle-1"
      role="slider"
      style="left:26%;transform:translateX(-50%)"
      tabindex="0"
    />
    <div
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="37"
      class="ant-slider-handle ant-slider-handle-2"
      role="slider"
      style="left:37%;transform:translateX(-50%)"
      tabindex="0"
    />
    <div
      class="ant-slider-mark"
    >
      <span
        class="ant-slider-mark-text"
        style="left:0%;transform:translateX(-50%)"
      >
        0°C
      </span>
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:26%;transform:translateX(-50%)"
      >
        26°C
      </span>
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:37%;transform:translateX(-50%)"
      >
        37°C
      </span>
      <span
        class="ant-slider-mark-text"
        style="left:100%;transform:translateX(-50%);color:#f50"
      >
        <strong>
          100°C
        </strong>
      </span>
    </div>
  </div>,
  <h4>
    included=false
  </h4>,
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal ant-slider-with-marks"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-step"
    >
      <span
        class="ant-slider-dot"
        style="left:0%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot"
        style="left:26%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot"
        style="left:37%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot"
        style="left:100%;transform:translateX(-50%)"
      />
    </div>
    <div
      aria-describedby="test-id"
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="37"
      class="ant-slider-handle"
      role="slider"
      style="left:37%;transform:translateX(-50%)"
      tabindex="0"
    />
    <div
      class="ant-slider-mark"
    >
      <span
        class="ant-slider-mark-text"
        style="left:0%;transform:translateX(-50%)"
      >
        0°C
      </span>
      <span
        class="ant-slider-mark-text"
        style="left:26%;transform:translateX(-50%)"
      >
        26°C
      </span>
      <span
        class="ant-slider-mark-text"
        style="left:37%;transform:translateX(-50%)"
      >
        37°C
      </span>
      <span
        class="ant-slider-mark-text"
        style="left:100%;transform:translateX(-50%);color:#f50"
      >
        <strong>
          100°C
        </strong>
      </span>
    </div>
  </div>,
  <h4>
    marks & step
  </h4>,
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal ant-slider-with-marks"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track"
      style="left:0%;width:37%"
    />
    <div
      class="ant-slider-step"
    >
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:0%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:26%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:37%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot"
        style="left:100%;transform:translateX(-50%)"
      />
    </div>
    <div
      aria-describedby="test-id"
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="37"
      class="ant-slider-handle"
      role="slider"
      style="left:37%;transform:translateX(-50%)"
      tabindex="0"
    />
    <div
      class="ant-slider-mark"
    >
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:0%;transform:translateX(-50%)"
      >
        0°C
      </span>
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:26%;transform:translateX(-50%)"
      >
        26°C
      </span>
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:37%;transform:translateX(-50%)"
      >
        37°C
      </span>
      <span
        class="ant-slider-mark-text"
        style="left:100%;transform:translateX(-50%);color:#f50"
      >
        <strong>
          100°C
        </strong>
      </span>
    </div>
  </div>,
  <h4>
    step=null
  </h4>,
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal ant-slider-with-marks"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track"
      style="left:0%;width:37%"
    />
    <div
      class="ant-slider-step"
    >
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:0%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:26%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot ant-slider-dot-active"
        style="left:37%;transform:translateX(-50%)"
      />
      <span
        class="ant-slider-dot"
        style="left:100%;transform:translateX(-50%)"
      />
    </div>
    <div
      aria-describedby="test-id"
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="37"
      class="ant-slider-handle"
      role="slider"
      style="left:37%;transform:translateX(-50%)"
      tabindex="0"
    />
    <div
      class="ant-slider-mark"
    >
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:0%;transform:translateX(-50%)"
      >
        0°C
      </span>
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:26%;transform:translateX(-50%)"
      >
        26°C
      </span>
      <span
        class="ant-slider-mark-text ant-slider-mark-text-active"
        style="left:37%;transform:translateX(-50%)"
      >
        37°C
      </span>
      <span
        class="ant-slider-mark-text"
        style="left:100%;transform:translateX(-50%);color:#f50"
      >
        <strong>
          100°C
        </strong>
      </span>
    </div>
  </div>,
]
`;

exports[`renders components/slider/demo/multiple.tsx correctly 1`] = `
<div
  class="ant-slider css-var-test-id ant-slider-horizontal"
>
  <div
    class="ant-slider-rail"
  />
  <div
    class="ant-slider-tracks"
    style="left:0%;width:20%;background:linear-gradient(to right, rgb(135,208,104) 0%, rgb(159,207,123) 100%)"
  />
  <div
    class="ant-slider-track ant-slider-track-1"
    style="left:0%;width:10%;background:transparent"
  />
  <div
    class="ant-slider-track ant-slider-track-2"
    style="left:10%;width:10%;background:transparent"
  />
  <div
    class="ant-slider-step"
  />
  <div
    aria-disabled="false"
    aria-orientation="horizontal"
    aria-valuemax="100"
    aria-valuemin="0"
    aria-valuenow="0"
    class="ant-slider-handle ant-slider-handle-1"
    role="slider"
    style="left:0%;transform:translateX(-50%)"
    tabindex="0"
  />
  <div
    aria-disabled="false"
    aria-orientation="horizontal"
    aria-valuemax="100"
    aria-valuemin="0"
    aria-valuenow="10"
    class="ant-slider-handle ant-slider-handle-2"
    role="slider"
    style="left:10%;transform:translateX(-50%)"
    tabindex="0"
  />
  <div
    aria-disabled="false"
    aria-orientation="horizontal"
    aria-valuemax="100"
    aria-valuemin="0"
    aria-valuenow="20"
    class="ant-slider-handle ant-slider-handle-3"
    role="slider"
    style="left:20%;transform:translateX(-50%)"
    tabindex="0"
  />
</div>
`;

exports[`renders components/slider/demo/reverse.tsx correctly 1`] = `
Array [
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track"
      style="right:0%;width:30%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-describedby="test-id"
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="30"
      class="ant-slider-handle"
      role="slider"
      style="right:30%;transform:translateX(50%)"
      tabindex="0"
    />
  </div>,
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track ant-slider-track-1"
      style="right:20%;width:30%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="20"
      class="ant-slider-handle ant-slider-handle-1"
      role="slider"
      style="right:20%;transform:translateX(50%)"
      tabindex="0"
    />
    <div
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="50"
      class="ant-slider-handle ant-slider-handle-2"
      role="slider"
      style="right:50%;transform:translateX(50%)"
      tabindex="0"
    />
  </div>,
  Reversed: ,
  <button
    aria-checked="true"
    class="ant-switch ant-switch-small css-var-test-id ant-switch-checked"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>,
]
`;

exports[`renders components/slider/demo/tip-formatter.tsx correctly 1`] = `
Array [
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track"
      style="left:0%;width:0%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-describedby="test-id"
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="0"
      class="ant-slider-handle"
      role="slider"
      style="left:0%;transform:translateX(-50%)"
      tabindex="0"
    />
  </div>,
  <div
    class="ant-slider css-var-test-id ant-slider-horizontal"
  >
    <div
      class="ant-slider-rail"
    />
    <div
      class="ant-slider-track"
      style="left:0%;width:0%"
    />
    <div
      class="ant-slider-step"
    />
    <div
      aria-describedby="test-id"
      aria-disabled="false"
      aria-orientation="horizontal"
      aria-valuemax="100"
      aria-valuemin="0"
      aria-valuenow="0"
      class="ant-slider-handle"
      role="slider"
      style="left:0%;transform:translateX(-50%)"
      tabindex="0"
    />
  </div>,
]
`;

exports[`renders components/slider/demo/vertical.tsx correctly 1`] = `
Array [
  <div
    style="display:inline-block;height:300px;margin-inline-start:70px"
  >
    <div
      class="ant-slider css-var-test-id ant-slider-vertical"
    >
      <div
        class="ant-slider-rail"
      />
      <div
        class="ant-slider-track"
        style="bottom:0%;height:30%"
      />
      <div
        class="ant-slider-step"
      />
      <div
        aria-describedby="test-id"
        aria-disabled="false"
        aria-orientation="vertical"
        aria-valuemax="100"
        aria-valuemin="0"
        aria-valuenow="30"
        class="ant-slider-handle"
        role="slider"
        style="bottom:30%;transform:translateY(50%)"
        tabindex="0"
      />
    </div>
  </div>,
  <div
    style="display:inline-block;height:300px;margin-inline-start:70px"
  >
    <div
      class="ant-slider css-var-test-id ant-slider-vertical"
    >
      <div
        class="ant-slider-rail"
      />
      <div
        class="ant-slider-track ant-slider-track-1"
        style="bottom:20%;height:30%"
      />
      <div
        class="ant-slider-step"
      />
      <div
        aria-disabled="false"
        aria-orientation="vertical"
        aria-valuemax="100"
        aria-valuemin="0"
        aria-valuenow="20"
        class="ant-slider-handle ant-slider-handle-1"
        role="slider"
        style="bottom:20%;transform:translateY(50%)"
        tabindex="0"
      />
      <div
        aria-disabled="false"
        aria-orientation="vertical"
        aria-valuemax="100"
        aria-valuemin="0"
        aria-valuenow="50"
        class="ant-slider-handle ant-slider-handle-2"
        role="slider"
        style="bottom:50%;transform:translateY(50%)"
        tabindex="0"
      />
    </div>
  </div>,
  <div
    style="display:inline-block;height:300px;margin-inline-start:70px"
  >
    <div
      class="ant-slider css-var-test-id ant-slider-vertical ant-slider-with-marks"
    >
      <div
        class="ant-slider-rail"
      />
      <div
        class="ant-slider-track ant-slider-track-1"
        style="bottom:26%;height:11%"
      />
      <div
        class="ant-slider-step"
      >
        <span
          class="ant-slider-dot"
          style="bottom:0%;transform:translateY(50%)"
        />
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="bottom:26%;transform:translateY(50%)"
        />
        <span
          class="ant-slider-dot ant-slider-dot-active"
          style="bottom:37%;transform:translateY(50%)"
        />
        <span
          class="ant-slider-dot"
          style="bottom:100%;transform:translateY(50%)"
        />
      </div>
      <div
        aria-disabled="false"
        aria-orientation="vertical"
        aria-valuemax="100"
        aria-valuemin="0"
        aria-valuenow="26"
        class="ant-slider-handle ant-slider-handle-1"
        role="slider"
        style="bottom:26%;transform:translateY(50%)"
        tabindex="0"
      />
      <div
        aria-disabled="false"
        aria-orientation="vertical"
        aria-valuemax="100"
        aria-valuemin="0"
        aria-valuenow="37"
        class="ant-slider-handle ant-slider-handle-2"
        role="slider"
        style="bottom:37%;transform:translateY(50%)"
        tabindex="0"
      />
      <div
        class="ant-slider-mark"
      >
        <span
          class="ant-slider-mark-text"
          style="bottom:0%;transform:translateY(50%)"
        >
          0°C
        </span>
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="bottom:26%;transform:translateY(50%)"
        >
          26°C
        </span>
        <span
          class="ant-slider-mark-text ant-slider-mark-text-active"
          style="bottom:37%;transform:translateY(50%)"
        >
          37°C
        </span>
        <span
          class="ant-slider-mark-text"
          style="bottom:100%;transform:translateY(50%);color:#f50"
        >
          <strong>
            100°C
          </strong>
        </span>
      </div>
    </div>
  </div>,
]
`;
