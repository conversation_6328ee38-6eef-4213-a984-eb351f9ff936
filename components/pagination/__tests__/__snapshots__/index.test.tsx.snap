// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Pagination ConfigProvider should be rendered correctly in RTL 1`] = `
<ul
  class="ant-pagination ant-pagination-rtl css-var-root"
>
  <li
    aria-disabled="true"
    class="ant-pagination-prev ant-pagination-disabled"
    title="Previous Page"
  >
    <button
      class="ant-pagination-item-link"
      disabled=""
      tabindex="-1"
      type="button"
    >
      <span
        aria-label="right"
        class="anticon anticon-right"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="right"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
          />
        </svg>
      </span>
    </button>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
    tabindex="0"
    title="1"
  >
    <a
      rel="nofollow"
    >
      1
    </a>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-2"
    tabindex="0"
    title="2"
  >
    <a
      rel="nofollow"
    >
      2
    </a>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-3"
    tabindex="0"
    title="3"
  >
    <a
      rel="nofollow"
    >
      3
    </a>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-4"
    tabindex="0"
    title="4"
  >
    <a
      rel="nofollow"
    >
      4
    </a>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-5"
    tabindex="0"
    title="5"
  >
    <a
      rel="nofollow"
    >
      5
    </a>
  </li>
  <li
    aria-disabled="false"
    class="ant-pagination-next"
    tabindex="0"
    title="Next Page"
  >
    <button
      class="ant-pagination-item-link"
      tabindex="-1"
      type="button"
    >
      <span
        aria-label="left"
        class="anticon anticon-left"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="left"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
          />
        </svg>
      </span>
    </button>
  </li>
</ul>
`;

exports[`Pagination ConfigProvider should be rendered correctly when componentSize is large 1`] = `
<ul
  class="ant-pagination css-var-root"
>
  <li
    aria-disabled="true"
    class="ant-pagination-prev ant-pagination-disabled"
    title="Previous Page"
  >
    <button
      class="ant-pagination-item-link"
      disabled=""
      tabindex="-1"
      type="button"
    >
      <span
        aria-label="left"
        class="anticon anticon-left"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="left"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
          />
        </svg>
      </span>
    </button>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
    tabindex="0"
    title="1"
  >
    <a
      rel="nofollow"
    >
      1
    </a>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-2"
    tabindex="0"
    title="2"
  >
    <a
      rel="nofollow"
    >
      2
    </a>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-3"
    tabindex="0"
    title="3"
  >
    <a
      rel="nofollow"
    >
      3
    </a>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-4"
    tabindex="0"
    title="4"
  >
    <a
      rel="nofollow"
    >
      4
    </a>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-5"
    tabindex="0"
    title="5"
  >
    <a
      rel="nofollow"
    >
      5
    </a>
  </li>
  <li
    aria-disabled="false"
    class="ant-pagination-next"
    tabindex="0"
    title="Next Page"
  >
    <button
      class="ant-pagination-item-link"
      tabindex="-1"
      type="button"
    >
      <span
        aria-label="right"
        class="anticon anticon-right"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="right"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
          />
        </svg>
      </span>
    </button>
  </li>
  <li
    class="ant-pagination-options"
  >
    <div
      aria-label="Page Size"
      class="ant-select ant-select-outlined ant-pagination-options-size-changer css-var-root ant-select-css-var ant-select-single ant-select-show-arrow ant-select-show-search"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-label="Page Size"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-item"
            title="10 / page"
          >
            10 / page
          </span>
        </span>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
  </li>
</ul>
`;

exports[`Pagination rtl render component should be rendered correctly in RTL direction 1`] = `
<ul
  class="ant-pagination ant-pagination-rtl css-var-root"
>
  <li
    aria-disabled="true"
    class="ant-pagination-prev ant-pagination-disabled"
    title="Previous Page"
  >
    <button
      class="ant-pagination-item-link"
      disabled=""
      tabindex="-1"
      type="button"
    >
      <span
        aria-label="right"
        class="anticon anticon-right"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="right"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
          />
        </svg>
      </span>
    </button>
  </li>
  <li
    class="ant-pagination-item ant-pagination-item-1 ant-pagination-item-disabled"
    tabindex="0"
    title="1"
  >
    <a
      rel="nofollow"
    >
      1
    </a>
  </li>
  <li
    aria-disabled="true"
    class="ant-pagination-next ant-pagination-disabled"
    title="Next Page"
  >
    <button
      class="ant-pagination-item-link"
      disabled=""
      tabindex="-1"
      type="button"
    >
      <span
        aria-label="left"
        class="anticon anticon-left"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="left"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
          />
        </svg>
      </span>
    </button>
  </li>
</ul>
`;
