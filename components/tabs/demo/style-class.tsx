import React from 'react';
import { Flex, Space, Tabs } from 'antd';
import type { TabsProps } from 'antd';

const classNamesObject: TabsProps['classNames'] = {
  root: 'demo-tabs-root',
  header: 'demo-tabs-header',
  item: 'demo-tabs-item',
  indicator: 'demo-tabs-indicator',
  content: 'demo-tabs-content',
};

const classNamesFn: TabsProps['classNames'] = (info) => {
  if (info.props.type === 'card') {
    return { root: 'demo-tabs-root--card' };
  }
  return { root: 'demo-tabs-root--line' };
};

const stylesObject: TabsProps['styles'] = {
  root: { borderWidth: 2, borderStyle: 'dashed', padding: 16 },
  header: { backgroundColor: '#f5f5f5' },
  item: { fontWeight: 'bold', color: '#1890ff' },
  indicator: { backgroundColor: '#ff4d4f', height: 4 },
  content: { backgroundColor: '#e6f7ff', padding: 16 },
};

const stylesFn: TabsProps['styles'] = (info) => {
  if (info.props.centered) {
    return { 
      root: { backgroundColor: '#f0f2f5', borderColor: '#1890ff' },
      header: { textAlign: 'center' }
    };
  }
  return { 
    root: { backgroundColor: '#fafafa', borderColor: '#d9d9d9' },
    header: { textAlign: 'left' }
  };
};

const items = [
  {
    key: '1',
    label: 'Tab 1',
    children: 'Content of Tab Pane 1',
  },
  {
    key: '2',
    label: 'Tab 2',
    children: 'Content of Tab Pane 2',
  },
  {
    key: '3',
    label: 'Tab 3',
    children: 'Content of Tab Pane 3',
  },
];

const App: React.FC = () => {
  return (
    <Space size={[8, 24]} direction="vertical" style={{ width: '100%' }}>
      <Flex vertical gap="middle">
        <h4>classNames Object</h4>
        <Tabs defaultActiveKey="1" items={items} classNames={classNamesObject} />
      </Flex>
      <Flex vertical gap="middle">
        <h4>classNames Function</h4>
        <Tabs defaultActiveKey="1" items={items} type="card" classNames={classNamesFn} />
      </Flex>
      <Flex vertical gap="middle">
        <h4>styles Object</h4>
        <Tabs defaultActiveKey="1" items={items} styles={stylesObject} />
      </Flex>
      <Flex vertical gap="middle">
        <h4>styles Function</h4>
        <Tabs defaultActiveKey="1" items={items} centered styles={stylesFn} />
      </Flex>
    </Space>
  );
};

export default App;
