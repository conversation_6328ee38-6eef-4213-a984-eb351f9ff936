// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Tabs renderTabBar custom-tab-bar 1`] = `
<div
  class="ant-tabs ant-tabs-top css-var-root ant-tabs-css-var"
>
  <div>
    custom-tab-bar
  </div>
  <div
    class="ant-tabs-content-holder"
  >
    <div
      class="ant-tabs-content ant-tabs-content-top"
    >
      <div
        aria-hidden="false"
        aria-labelledby="rc-tabs-test-tab-1"
        class="ant-tabs-tabpane ant-tabs-tabpane-active"
        id="rc-tabs-test-panel-1"
        role="tabpanel"
        tabindex="0"
      >
        foo
      </div>
    </div>
  </div>
</div>
`;

exports[`Tabs rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-tabs ant-tabs-top ant-tabs-rtl css-var-root ant-tabs-css-var"
>
  <div
    aria-orientation="horizontal"
    class="ant-tabs-nav"
    role="tablist"
  >
    <div
      class="ant-tabs-nav-wrap"
    >
      <div
        class="ant-tabs-nav-list"
        style="transform: translate(0px, 0px);"
      >
        <div
          class="ant-tabs-tab ant-tabs-tab-active"
          data-node-key="xx"
        >
          <div
            aria-controls="rc-tabs-test-panel-xx"
            aria-selected="true"
            class="ant-tabs-tab-btn"
            id="rc-tabs-test-tab-xx"
            role="tab"
            tabindex="0"
          >
            xx
          </div>
        </div>
        <div
          class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"
        />
      </div>
    </div>
    <div
      class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"
    >
      <button
        aria-controls="rc-tabs-test-more-popup"
        aria-expanded="false"
        aria-haspopup="listbox"
        class="ant-tabs-nav-more"
        id="rc-tabs-test-more"
        style="visibility: hidden; order: 1;"
        type="button"
      >
        <span
          aria-label="ellipsis"
          class="anticon anticon-ellipsis"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="ellipsis"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-tabs-content-holder"
  >
    <div
      class="ant-tabs-content ant-tabs-content-top"
    >
      <div
        aria-hidden="false"
        aria-labelledby="rc-tabs-test-tab-xx"
        class="ant-tabs-tabpane ant-tabs-tabpane-active"
        id="rc-tabs-test-panel-xx"
        role="tabpanel"
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

exports[`Tabs tabBarGutter should work 1`] = `
<div
  class="ant-tabs ant-tabs-top css-var-root ant-tabs-css-var"
>
  <div
    aria-orientation="horizontal"
    class="ant-tabs-nav"
    role="tablist"
  >
    <div
      class="ant-tabs-nav-wrap"
    >
      <div
        class="ant-tabs-nav-list"
        style="transform: translate(0px, 0px);"
      >
        <div
          class="ant-tabs-tab ant-tabs-tab-active"
          data-node-key="null"
        >
          <div
            aria-controls="rc-tabs-test-panel-null"
            aria-selected="true"
            class="ant-tabs-tab-btn"
            id="rc-tabs-test-tab-null"
            role="tab"
            tabindex="0"
          />
        </div>
        <div
          class="ant-tabs-tab ant-tabs-tab-active"
          data-node-key="null"
          style="margin-left: 0px;"
        >
          <div
            aria-controls="rc-tabs-test-panel-null"
            aria-selected="true"
            class="ant-tabs-tab-btn"
            id="rc-tabs-test-tab-null"
            role="tab"
            tabindex="0"
          />
        </div>
        <div
          class="ant-tabs-tab ant-tabs-tab-active"
          data-node-key="null"
          style="margin-left: 0px;"
        >
          <div
            aria-controls="rc-tabs-test-panel-null"
            aria-selected="true"
            class="ant-tabs-tab-btn"
            id="rc-tabs-test-tab-null"
            role="tab"
            tabindex="0"
          />
        </div>
        <div
          class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"
        />
      </div>
    </div>
    <div
      class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"
    >
      <button
        aria-controls="rc-tabs-test-more-popup"
        aria-expanded="false"
        aria-haspopup="listbox"
        class="ant-tabs-nav-more"
        id="rc-tabs-test-more"
        style="margin-left: 0px; visibility: hidden; order: 1;"
        type="button"
      >
        <span
          aria-label="ellipsis"
          class="anticon anticon-ellipsis"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="ellipsis"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-tabs-content-holder"
  >
    <div
      class="ant-tabs-content ant-tabs-content-top"
    >
      <div
        aria-hidden="false"
        aria-labelledby="rc-tabs-test-tab-null"
        class="ant-tabs-tabpane ant-tabs-tabpane-active"
        id="rc-tabs-test-panel-null"
        role="tabpanel"
        tabindex="-1"
      />
      <div
        aria-hidden="false"
        aria-labelledby="rc-tabs-test-tab-null"
        class="ant-tabs-tabpane ant-tabs-tabpane-active"
        id="rc-tabs-test-panel-null"
        role="tabpanel"
        tabindex="-1"
      />
      <div
        aria-hidden="false"
        aria-labelledby="rc-tabs-test-tab-null"
        class="ant-tabs-tabpane ant-tabs-tabpane-active"
        id="rc-tabs-test-panel-null"
        role="tabpanel"
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

exports[`Tabs tabBarGutter should work 2`] = `
<div
  class="ant-tabs ant-tabs-left css-var-root ant-tabs-css-var"
>
  <div
    aria-orientation="vertical"
    class="ant-tabs-nav"
    role="tablist"
  >
    <div
      class="ant-tabs-nav-wrap"
    >
      <div
        class="ant-tabs-nav-list"
        style="transform: translate(0px, 0px);"
      >
        <div
          class="ant-tabs-tab ant-tabs-tab-active"
          data-node-key="null"
        >
          <div
            aria-controls="rc-tabs-test-panel-null"
            aria-selected="true"
            class="ant-tabs-tab-btn"
            id="rc-tabs-test-tab-null"
            role="tab"
            tabindex="0"
          />
        </div>
        <div
          class="ant-tabs-tab ant-tabs-tab-active"
          data-node-key="null"
          style="margin-top: 0px;"
        >
          <div
            aria-controls="rc-tabs-test-panel-null"
            aria-selected="true"
            class="ant-tabs-tab-btn"
            id="rc-tabs-test-tab-null"
            role="tab"
            tabindex="0"
          />
        </div>
        <div
          class="ant-tabs-tab ant-tabs-tab-active"
          data-node-key="null"
          style="margin-top: 0px;"
        >
          <div
            aria-controls="rc-tabs-test-panel-null"
            aria-selected="true"
            class="ant-tabs-tab-btn"
            id="rc-tabs-test-tab-null"
            role="tab"
            tabindex="0"
          />
        </div>
        <div
          class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"
        />
      </div>
    </div>
    <div
      class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"
    >
      <button
        aria-controls="rc-tabs-test-more-popup"
        aria-expanded="false"
        aria-haspopup="listbox"
        class="ant-tabs-nav-more"
        id="rc-tabs-test-more"
        style="margin-left: 0px; visibility: hidden; order: 1;"
        type="button"
      >
        <span
          aria-label="ellipsis"
          class="anticon anticon-ellipsis"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="ellipsis"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-tabs-content-holder"
  >
    <div
      class="ant-tabs-content ant-tabs-content-left"
    >
      <div
        aria-hidden="false"
        aria-labelledby="rc-tabs-test-tab-null"
        class="ant-tabs-tabpane ant-tabs-tabpane-active"
        id="rc-tabs-test-panel-null"
        role="tabpanel"
        tabindex="-1"
      />
      <div
        aria-hidden="false"
        aria-labelledby="rc-tabs-test-tab-null"
        class="ant-tabs-tabpane ant-tabs-tabpane-active"
        id="rc-tabs-test-panel-null"
        role="tabpanel"
        tabindex="-1"
      />
      <div
        aria-hidden="false"
        aria-labelledby="rc-tabs-test-tab-null"
        class="ant-tabs-tabpane ant-tabs-tabpane-active"
        id="rc-tabs-test-panel-null"
        role="tabpanel"
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

exports[`Tabs tabPosition remove card 1`] = `
<div
  class="ant-tabs ant-tabs-left css-var-root ant-tabs-css-var"
>
  <div
    aria-orientation="vertical"
    class="ant-tabs-nav"
    role="tablist"
  >
    <div
      class="ant-tabs-nav-wrap"
    >
      <div
        class="ant-tabs-nav-list"
        style="transform: translate(0px, 0px);"
      >
        <div
          class="ant-tabs-tab ant-tabs-tab-active"
          data-node-key="1"
        >
          <div
            aria-controls="rc-tabs-test-panel-1"
            aria-selected="true"
            class="ant-tabs-tab-btn"
            id="rc-tabs-test-tab-1"
            role="tab"
            tabindex="0"
          >
            foo
          </div>
        </div>
        <div
          class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"
        />
      </div>
    </div>
    <div
      class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"
    >
      <button
        aria-controls="rc-tabs-test-more-popup"
        aria-expanded="false"
        aria-haspopup="listbox"
        class="ant-tabs-nav-more"
        id="rc-tabs-test-more"
        style="visibility: hidden; order: 1;"
        type="button"
      >
        <span
          aria-label="ellipsis"
          class="anticon anticon-ellipsis"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="ellipsis"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
            />
          </svg>
        </span>
      </button>
    </div>
    <div
      class="ant-tabs-extra-content"
    >
      xxx
    </div>
  </div>
  <div
    class="ant-tabs-content-holder"
  >
    <div
      class="ant-tabs-content ant-tabs-content-left"
    >
      <div
        aria-hidden="false"
        aria-labelledby="rc-tabs-test-tab-1"
        class="ant-tabs-tabpane ant-tabs-tabpane-active"
        id="rc-tabs-test-panel-1"
        role="tabpanel"
        tabindex="0"
      >
        foo
      </div>
    </div>
  </div>
</div>
`;
