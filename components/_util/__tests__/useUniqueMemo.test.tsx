import React from 'react';

import { act, render } from '../../../tests/utils';
import useUniqueMemo from '../hooks/useUniqueMemo';

describe('useUniqueMemo', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  it('should share memoized values across instances', () => {
    const sharedObjDeps1 = {};
    const sharedObjDeps2 = {};

    let calledTimes = 0;

    const Test: React.FC<{ depName?: string }> = ({ depName }) => {
      useUniqueMemo(() => {
        calledTimes += 1;
        return depName;
      }, [depName, sharedObjDeps1, 'bamboo', sharedObjDeps2]);
      return null;
    };

    // Reuse the same memo
    const { rerender } = render(
      <>
        <Test depName="light" />
        <Test depName="light" />
        <Test depName="light" />
      </>,
    );

    expect(calledTimes).toBe(1);

    // Different deps should clean up the cache
    act(() => {
      jest.advanceTimersByTime(1000 * 60 * 20);
    });

    for (let i = 0; i < 20000; i += 1) {
      rerender(<Test depName="diff" key={i} />);
    }
    rerender(<Test depName="clear" />);
    calledTimes = 0;

    // Back should recompute
    rerender(<Test depName="light" />);
    expect(calledTimes).toBe(1);
  });

  it('should handle primitive dependencies correctly', () => {
    let calledTimes = 0;
    const Test: React.FC<{ str: string; num: number; bool: boolean }> = ({ str, num, bool }) => {
      useUniqueMemo(() => {
        calledTimes += 1;
        return `${str}-${num}-${bool}`;
      }, [str, num, bool]);
      return null;
    };

    const { rerender } = render(<Test str="test" num={1} bool={true} />);
    expect(calledTimes).toBe(1);

    rerender(<Test str="test" num={1} bool={true} />);
    expect(calledTimes).toBe(1);

    rerender(<Test str="test2" num={1} bool={true} />);
    expect(calledTimes).toBe(2);
  });

  it('should handle object dependencies correctly', () => {
    let calledTimes = 0;
    const obj1 = { key: 'value1' };
    const obj2 = { key: 'value2' };

    const Test: React.FC<{ obj: any }> = ({ obj }) => {
      useUniqueMemo(() => {
        calledTimes += 1;
        return obj.key;
      }, [obj]);
      return null;
    };

    const { rerender } = render(<Test obj={obj1} />);
    expect(calledTimes).toBe(1);

    rerender(<Test obj={obj1} />);
    expect(calledTimes).toBe(1);

    rerender(<Test obj={obj2} />);
    expect(calledTimes).toBe(2);
  });

  it('should handle empty dependencies', () => {
    let calledTimes = 0;
    const Test = () => {
      useUniqueMemo(() => {
        calledTimes += 1;
        return 'constant';
      }, []);
      return null;
    };

    const { rerender } = render(<Test />);
    expect(calledTimes).toBe(1);

    rerender(<Test />);
    expect(calledTimes).toBe(1);
  });
});
