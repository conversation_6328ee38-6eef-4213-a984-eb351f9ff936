import React from 'react';

import { fireEvent, render } from '../../../tests/utils';
import useSyncState from '../hooks/useSyncState';

describe('useSyncState', () => {
  it('should work with basic usage', () => {
    const Test = () => {
      const [getVal, setVal] = useSyncState('light');
      return <span onClick={() => setVal('bamboo')}>{getVal()}</span>;
    };

    const { container } = render(<Test />);
    expect(container.querySelector('span')?.innerHTML).toBe('light');
    fireEvent.click(container.querySelector('span')!);
    expect(container.querySelector('span')?.innerHTML).toBe('bamboo');
  });

  it('should handle value changes correctly', () => {
    const Test = () => {
      const [getVal, setVal] = useSyncState('initial');
      return (
        <div>
          <span data-testid="value">{getVal()}</span>
          <button data-testid="same" onClick={() => setVal('initial')}>Same Value</button>
          <button data-testid="new" onClick={() => setVal('new')}>New Value</button>
        </div>
      );
    };

    const { container } = render(<Test />);
    expect(container.querySelector('[data-testid="value"]')?.textContent).toBe('initial');

    fireEvent.click(container.querySelector('[data-testid="same"]')!);
    expect(container.querySelector('[data-testid="value"]')?.textContent).toBe('initial');

    fireEvent.click(container.querySelector('[data-testid="new"]')!);
    expect(container.querySelector('[data-testid="value"]')?.textContent).toBe('new');
  });

  it('should work with different data types', () => {
    const Test = () => {
      const [getNum, setNum] = useSyncState(0);
      const [getBool, setBool] = useSyncState(false);
      const [getObj, setObj] = useSyncState({ key: 'value' });

      return (
        <div>
          <span data-testid="num">{getNum()}</span>
          <span data-testid="bool">{getBool().toString()}</span>
          <span data-testid="obj">{getObj().key}</span>
          <button onClick={() => setNum(42)}>Set Number</button>
          <button onClick={() => setBool(true)}>Set Boolean</button>
          <button onClick={() => setObj({ key: 'new' })}>Set Object</button>
        </div>
      );
    };

    const { container } = render(<Test />);
    expect(container.querySelector('[data-testid="num"]')?.innerHTML).toBe('0');
    expect(container.querySelector('[data-testid="bool"]')?.innerHTML).toBe('false');
    expect(container.querySelector('[data-testid="obj"]')?.innerHTML).toBe('value');

    fireEvent.click(container.querySelectorAll('button')[0]);
    expect(container.querySelector('[data-testid="num"]')?.innerHTML).toBe('42');

    fireEvent.click(container.querySelectorAll('button')[1]);
    expect(container.querySelector('[data-testid="bool"]')?.innerHTML).toBe('true');

    fireEvent.click(container.querySelectorAll('button')[2]);
    expect(container.querySelector('[data-testid="obj"]')?.innerHTML).toBe('new');
  });
});
