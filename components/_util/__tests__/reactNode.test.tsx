import React from 'react';

import { cloneElement, isFragment, replaceElement } from '../reactNode';

describe('reactNode test', () => {
  it('isFragment', () => {
    expect(isFragment(<p>test</p>)).toBe(false);
    expect(isFragment(<>test</>)).toBe(true);
    expect(isFragment(null)).toBe(false);
    expect(isFragment(undefined)).toBe(false);
    expect(isFragment('string')).toBe(false);
    expect(isFragment(123)).toBe(false);
  });

  it('replaceElement', () => {
    const node = <p>test</p>;
    const replacement = <div>replacement</div>;
    expect(replaceElement(null, node)).toBe(node);
    expect(replaceElement(node, replacement)).toStrictEqual(node);
    expect(replaceElement('string', replacement)).toBe(replacement);
    expect(replaceElement(123, replacement)).toBe(replacement);
  });

  it('replaceElement with props', () => {
    const node = <p className="original">test</p>;
    const replacement = <div>replacement</div>;
    const result = replaceElement(node, replacement, { className: 'new' });
    expect(React.isValidElement(result)).toBe(true);
    if (React.isValidElement(result)) {
      expect((result.props as any).className).toBe('new');
    }
  });

  it('replaceElement with function props', () => {
    const node = <p className="original">test</p>;
    const replacement = <div>replacement</div>;
    const result = replaceElement(node, replacement, (props) => ({
      className: `${props.className} added`
    }));
    expect(React.isValidElement(result)).toBe(true);
    if (React.isValidElement(result)) {
      expect((result.props as any).className).toBe('original added');
    }
  });

  it('cloneElement', () => {
    const node = <p>test</p>;
    expect(cloneElement(null)).toBe(null);
    expect(cloneElement(node)).toStrictEqual(node);
    expect(cloneElement('string')).toBe(null);
    expect(cloneElement(123)).toBe(null);
  });

  it('cloneElement with props', () => {
    const node = <p className="original">test</p>;
    const result = cloneElement(node, { className: 'new' });
    expect(result?.props.className).toBe('new');
  });
});
