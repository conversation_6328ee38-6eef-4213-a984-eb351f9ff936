import capitalize from '../capitalize';

describe('capitalize', () => {
  it('should capitalize the first character of a string', () => {
    expect(capitalize('antd')).toBe('Antd');
    expect(capitalize('Antd')).toBe('Antd');
    expect(capitalize(' antd')).toBe(' antd');
    expect(capitalize('')).toBe('');
    expect(capitalize('a')).toBe('A');
    expect(capitalize('ABC')).toBe('ABC');
    expect(capitalize('123abc')).toBe('123abc');
  });

  it('should return the original value when is not a string', () => {
    expect(capitalize(1 as any)).toBe(1);
    expect(capitalize(true as any)).toBe(true);
    expect(capitalize(undefined as any)).toBe(undefined);
    expect(capitalize(null as any)).toBe(null);

    const obj = {};
    expect(capitalize(obj as any)).toBe(obj);
  });
});
