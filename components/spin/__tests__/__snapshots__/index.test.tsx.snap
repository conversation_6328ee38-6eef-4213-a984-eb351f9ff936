// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Spin if indicator set null should not be render default indicator 1`] = `
<div
  aria-busy="true"
  aria-live="polite"
  class="ant-spin ant-spin-spinning css-var-root"
>
  <span
    class="ant-spin-dot-holder"
  >
    <span
      class="ant-spin-dot ant-spin-dot-spin"
    >
      <i
        class="ant-spin-dot-item"
      />
      <i
        class="ant-spin-dot-item"
      />
      <i
        class="ant-spin-dot-item"
      />
      <i
        class="ant-spin-dot-item"
      />
    </span>
  </span>
</div>
`;

exports[`Spin rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  aria-busy="true"
  aria-live="polite"
  class="ant-spin ant-spin-spinning ant-spin-rtl css-var-root"
>
  <span
    class="ant-spin-dot-holder"
  >
    <span
      class="ant-spin-dot ant-spin-dot-spin"
    >
      <i
        class="ant-spin-dot-item"
      />
      <i
        class="ant-spin-dot-item"
      />
      <i
        class="ant-spin-dot-item"
      />
      <i
        class="ant-spin-dot-item"
      />
    </span>
  </span>
</div>
`;

exports[`Spin should render custom indicator when it's set 1`] = `
<div
  aria-busy="true"
  aria-live="polite"
  class="ant-spin ant-spin-spinning css-var-root"
>
  <div
    class="custom-indicator ant-spin-dot"
  />
</div>
`;

exports[`Spin should support static method Spin.setDefaultIndicator 1`] = `
<div
  aria-busy="true"
  aria-live="polite"
  class="ant-spin ant-spin-spinning css-var-root"
>
  <em
    class="custom-spinner ant-spin-dot"
  />
</div>
`;
