// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/spin/demo/basic.tsx correctly 1`] = `
<div
  aria-busy="true"
  aria-live="polite"
  class="ant-spin ant-spin-spinning css-var-test-id"
>
  <span
    class="ant-spin-dot-holder"
  >
    <span
      class="ant-spin-dot ant-spin-dot-spin"
    >
      <i
        class="ant-spin-dot-item"
      />
      <i
        class="ant-spin-dot-item"
      />
      <i
        class="ant-spin-dot-item"
      />
      <i
        class="ant-spin-dot-item"
      />
    </span>
  </span>
</div>
`;

exports[`renders components/spin/demo/custom-indicator.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-center ant-flex-gap-middle"
>
  <div
    aria-busy="true"
    aria-live="polite"
    class="ant-spin ant-spin-sm ant-spin-spinning css-var-test-id"
  >
    <span
      aria-label="loading"
      class="anticon anticon-loading anticon-spin ant-spin-dot"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="loading"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
      >
        <path
          d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
        />
      </svg>
    </span>
  </div>
  <div
    aria-busy="true"
    aria-live="polite"
    class="ant-spin ant-spin-spinning css-var-test-id"
  >
    <span
      aria-label="loading"
      class="anticon anticon-loading anticon-spin ant-spin-dot"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="loading"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
      >
        <path
          d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
        />
      </svg>
    </span>
  </div>
  <div
    aria-busy="true"
    aria-live="polite"
    class="ant-spin ant-spin-lg ant-spin-spinning css-var-test-id"
  >
    <span
      aria-label="loading"
      class="anticon anticon-loading anticon-spin ant-spin-dot"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="loading"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
      >
        <path
          d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
        />
      </svg>
    </span>
  </div>
  <div
    aria-busy="true"
    aria-live="polite"
    class="ant-spin ant-spin-spinning css-var-test-id"
  >
    <span
      aria-label="loading"
      class="anticon anticon-loading anticon-spin ant-spin-dot"
      role="img"
      style="font-size:48px"
    >
      <svg
        aria-hidden="true"
        data-icon="loading"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
      >
        <path
          d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
        />
      </svg>
    </span>
  </div>
</div>
`;

exports[`renders components/spin/demo/delayAndDebounce.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-spin-nested-loading css-var-test-id"
  >
    <div
      class="ant-spin-container"
    >
      <div
        class="ant-alert ant-alert-info ant-alert-with-description ant-alert-no-icon css-var-test-id"
        data-show="true"
        role="alert"
      >
        <div
          class="ant-alert-section"
        >
          <div
            class="ant-alert-title"
          >
            Alert message title
          </div>
          <div
            class="ant-alert-description"
          >
            Further details about the context of this alert.
          </div>
        </div>
      </div>
    </div>
  </div>
  <p>
    Loading state：
    <button
      aria-checked="false"
      class="ant-switch css-var-test-id"
      role="switch"
      type="button"
    >
      <div
        class="ant-switch-handle"
      />
      <span
        class="ant-switch-inner"
      >
        <span
          class="ant-switch-inner-checked"
        />
        <span
          class="ant-switch-inner-unchecked"
        />
      </span>
    </button>
  </p>
</div>
`;

exports[`renders components/spin/demo/fullscreen.tsx correctly 1`] = `
Array [
  <button
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
    type="button"
  >
    <span>
      Show fullscreen
    </span>
  </button>,
  <div
    class="ant-spin-fullscreen css-var-test-id"
  >
    <div
      aria-busy="false"
      aria-live="polite"
      class="ant-spin css-var-test-id"
    >
      <span
        class="ant-spin-dot-holder"
      >
        <span
          class="ant-spin-dot ant-spin-dot-spin"
        >
          <i
            class="ant-spin-dot-item"
          />
          <i
            class="ant-spin-dot-item"
          />
          <i
            class="ant-spin-dot-item"
          />
          <i
            class="ant-spin-dot-item"
          />
        </span>
      </span>
    </div>
  </div>,
]
`;

exports[`renders components/spin/demo/nested.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-spin-nested-loading css-var-test-id"
  >
    <div
      class="ant-spin-container"
    >
      <div
        class="ant-alert ant-alert-info ant-alert-with-description ant-alert-no-icon css-var-test-id"
        data-show="true"
        role="alert"
      >
        <div
          class="ant-alert-section"
        >
          <div
            class="ant-alert-title"
          >
            Alert message title
          </div>
          <div
            class="ant-alert-description"
          >
            Further details about the context of this alert.
          </div>
        </div>
      </div>
    </div>
  </div>
  <p>
    Loading state：
    <button
      aria-checked="false"
      class="ant-switch css-var-test-id"
      role="switch"
      type="button"
    >
      <div
        class="ant-switch-handle"
      />
      <span
        class="ant-switch-inner"
      >
        <span
          class="ant-switch-inner-checked"
        />
        <span
          class="ant-switch-inner-unchecked"
        />
      </span>
    </button>
  </p>
</div>
`;

exports[`renders components/spin/demo/percent.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-center ant-flex-gap-middle"
>
  <button
    aria-checked="false"
    class="ant-switch css-var-test-id"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      >
        Auto
      </span>
      <span
        class="ant-switch-inner-unchecked"
      >
        Auto
      </span>
    </span>
  </button>
  <div
    aria-busy="true"
    aria-live="polite"
    class="ant-spin ant-spin-sm ant-spin-spinning css-var-test-id"
  >
    <span
      class="ant-spin-dot-holder"
    >
      <span
        class="ant-spin-dot ant-spin-dot-spin"
      >
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
      </span>
    </span>
  </div>
  <div
    aria-busy="true"
    aria-live="polite"
    class="ant-spin ant-spin-spinning css-var-test-id"
  >
    <span
      class="ant-spin-dot-holder"
    >
      <span
        class="ant-spin-dot ant-spin-dot-spin"
      >
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
      </span>
    </span>
  </div>
  <div
    aria-busy="true"
    aria-live="polite"
    class="ant-spin ant-spin-lg ant-spin-spinning css-var-test-id"
  >
    <span
      class="ant-spin-dot-holder"
    >
      <span
        class="ant-spin-dot ant-spin-dot-spin"
      >
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/spin/demo/size.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-center ant-flex-gap-middle"
>
  <div
    aria-busy="true"
    aria-live="polite"
    class="ant-spin ant-spin-sm ant-spin-spinning css-var-test-id"
  >
    <span
      class="ant-spin-dot-holder"
    >
      <span
        class="ant-spin-dot ant-spin-dot-spin"
      >
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
      </span>
    </span>
  </div>
  <div
    aria-busy="true"
    aria-live="polite"
    class="ant-spin ant-spin-spinning css-var-test-id"
  >
    <span
      class="ant-spin-dot-holder"
    >
      <span
        class="ant-spin-dot ant-spin-dot-spin"
      >
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
      </span>
    </span>
  </div>
  <div
    aria-busy="true"
    aria-live="polite"
    class="ant-spin ant-spin-lg ant-spin-spinning css-var-test-id"
  >
    <span
      class="ant-spin-dot-holder"
    >
      <span
        class="ant-spin-dot ant-spin-dot-spin"
      >
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
        <i
          class="ant-spin-dot-item"
        />
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/spin/demo/tip.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-middle"
  >
    <div
      class="ant-spin-nested-loading css-var-test-id"
    >
      <div>
        <div
          aria-busy="true"
          aria-live="polite"
          class="ant-spin ant-spin-sm ant-spin-spinning ant-spin-show-text css-var-test-id"
        >
          <span
            class="ant-spin-dot-holder"
          >
            <span
              class="ant-spin-dot ant-spin-dot-spin"
            >
              <i
                class="ant-spin-dot-item"
              />
              <i
                class="ant-spin-dot-item"
              />
              <i
                class="ant-spin-dot-item"
              />
              <i
                class="ant-spin-dot-item"
              />
            </span>
          </span>
          <div
            class="ant-spin-text"
          >
            Loading
          </div>
        </div>
      </div>
      <div
        class="ant-spin-container ant-spin-blur"
      >
        <div
          style="padding:50px;background:rgba(0, 0, 0, 0.05);border-radius:4px"
        />
      </div>
    </div>
    <div
      class="ant-spin-nested-loading css-var-test-id"
    >
      <div>
        <div
          aria-busy="true"
          aria-live="polite"
          class="ant-spin ant-spin-spinning ant-spin-show-text css-var-test-id"
        >
          <span
            class="ant-spin-dot-holder"
          >
            <span
              class="ant-spin-dot ant-spin-dot-spin"
            >
              <i
                class="ant-spin-dot-item"
              />
              <i
                class="ant-spin-dot-item"
              />
              <i
                class="ant-spin-dot-item"
              />
              <i
                class="ant-spin-dot-item"
              />
            </span>
          </span>
          <div
            class="ant-spin-text"
          >
            Loading
          </div>
        </div>
      </div>
      <div
        class="ant-spin-container ant-spin-blur"
      >
        <div
          style="padding:50px;background:rgba(0, 0, 0, 0.05);border-radius:4px"
        />
      </div>
    </div>
    <div
      class="ant-spin-nested-loading css-var-test-id"
    >
      <div>
        <div
          aria-busy="true"
          aria-live="polite"
          class="ant-spin ant-spin-lg ant-spin-spinning ant-spin-show-text css-var-test-id"
        >
          <span
            class="ant-spin-dot-holder"
          >
            <span
              class="ant-spin-dot ant-spin-dot-spin"
            >
              <i
                class="ant-spin-dot-item"
              />
              <i
                class="ant-spin-dot-item"
              />
              <i
                class="ant-spin-dot-item"
              />
              <i
                class="ant-spin-dot-item"
              />
            </span>
          </span>
          <div
            class="ant-spin-text"
          >
            Loading
          </div>
        </div>
      </div>
      <div
        class="ant-spin-container ant-spin-blur"
      >
        <div
          style="padding:50px;background:rgba(0, 0, 0, 0.05);border-radius:4px"
        />
      </div>
    </div>
  </div>
  <div
    class="ant-spin-nested-loading css-var-test-id"
  >
    <div>
      <div
        aria-busy="true"
        aria-live="polite"
        class="ant-spin ant-spin-spinning ant-spin-show-text css-var-test-id"
      >
        <span
          class="ant-spin-dot-holder"
        >
          <span
            class="ant-spin-dot ant-spin-dot-spin"
          >
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
          </span>
        </span>
        <div
          class="ant-spin-text"
        >
          Loading...
        </div>
      </div>
    </div>
    <div
      class="ant-spin-container ant-spin-blur"
    >
      <div
        class="ant-alert ant-alert-info ant-alert-with-description ant-alert-no-icon css-var-test-id"
        data-show="true"
        role="alert"
      >
        <div
          class="ant-alert-section"
        >
          <div
            class="ant-alert-title"
          >
            Alert message title
          </div>
          <div
            class="ant-alert-description"
          >
            Further details about the context of this alert.
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
