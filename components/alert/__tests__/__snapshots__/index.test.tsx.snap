// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Alert custom action 1`] = `
<div
  class="ant-alert ant-alert-success css-var-root"
  data-show="true"
  role="alert"
>
  <span
    aria-label="check-circle"
    class="anticon anticon-check-circle ant-alert-icon"
    role="img"
  >
    <svg
      aria-hidden="true"
      data-icon="check-circle"
      fill="currentColor"
      focusable="false"
      height="1em"
      viewBox="64 64 896 896"
      width="1em"
    >
      <path
        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
      />
    </svg>
  </span>
  <div
    class="ant-alert-section"
  >
    <div
      class="ant-alert-title"
    >
      Success Tips
    </div>
  </div>
  <div
    class="ant-alert-actions"
  >
    <button
      class="ant-btn css-var-root ant-btn-text ant-btn-color-default ant-btn-variant-text ant-btn-sm"
      type="button"
    >
      <span>
        UNDO
      </span>
    </button>
  </div>
  <button
    class="ant-alert-close-icon"
    tabindex="0"
    type="button"
  >
    <span
      aria-label="close"
      class="anticon anticon-close"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="close"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
        />
      </svg>
    </span>
  </button>
</div>
`;

exports[`Alert rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-alert ant-alert-info ant-alert-no-icon ant-alert-rtl css-var-root"
  data-show="true"
  role="alert"
>
  <div
    class="ant-alert-section"
  />
</div>
`;
