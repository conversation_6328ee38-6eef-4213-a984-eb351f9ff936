// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/alert/demo/action.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-alert ant-alert-success css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="check-circle"
      class="anticon anticon-check-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="check-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Success Tips
      </div>
    </div>
    <div
      class="ant-alert-actions"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-text ant-btn-color-default ant-btn-variant-text ant-btn-sm"
        type="button"
      >
        <span>
          UNDO
        </span>
      </button>
    </div>
    <button
      class="ant-alert-close-icon"
      tabindex="0"
      type="button"
    >
      <span
        aria-label="close"
        class="anticon anticon-close"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </button>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-error ant-alert-with-description css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="close-circle"
      class="anticon anticon-close-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="close-circle"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.*********** 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .***********.09l45.02 45.02a.2.2 0 ***********.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.***********.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Error Text
      </div>
      <div
        class="ant-alert-description"
      >
        Error Description Error Description Error Description Error Description
      </div>
    </div>
    <div
      class="ant-alert-actions"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined ant-btn-sm"
        type="button"
      >
        <span>
          Detail
        </span>
      </button>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-warning ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Warning Text
      </div>
    </div>
    <div
      class="ant-alert-actions"
    >
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
      >
        <div
          class="ant-space-item"
        >
          <button
            class="ant-btn css-var-test-id ant-btn-text ant-btn-color-default ant-btn-variant-text ant-btn-sm"
            type="button"
          >
            <span>
              Done
            </span>
          </button>
        </div>
      </div>
    </div>
    <button
      class="ant-alert-close-icon"
      tabindex="0"
      type="button"
    >
      <span
        aria-label="close"
        class="anticon anticon-close"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </button>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-info ant-alert-with-description ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Info Text
      </div>
      <div
        class="ant-alert-description"
      >
        Info Description Info Description Info Description Info Description
      </div>
    </div>
    <div
      class="ant-alert-actions"
    >
      <div
        class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
      >
        <div
          class="ant-space-item"
        >
          <button
            class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
            type="button"
          >
            <span>
              Accept
            </span>
          </button>
        </div>
        <div
          class="ant-space-item"
        >
          <button
            class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined ant-btn-sm ant-btn-background-ghost"
            type="button"
          >
            <span>
              Decline
            </span>
          </button>
        </div>
      </div>
    </div>
    <button
      class="ant-alert-close-icon"
      tabindex="0"
      type="button"
    >
      <span
        aria-label="close"
        class="anticon anticon-close"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </button>
  </div>,
]
`;

exports[`renders components/alert/demo/action.tsx extend context correctly 2`] = `[]`;

exports[`renders components/alert/demo/banner.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-alert ant-alert-warning ant-alert-banner css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="exclamation-circle"
      class="anticon anticon-exclamation-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="exclamation-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Warning text
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-warning ant-alert-banner css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="exclamation-circle"
      class="anticon anticon-exclamation-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="exclamation-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Very long warning text warning text text text text text text text
      </div>
    </div>
    <button
      class="ant-alert-close-icon"
      tabindex="0"
      type="button"
    >
      <span
        aria-label="close"
        class="anticon anticon-close"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </button>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-warning ant-alert-no-icon ant-alert-banner css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Warning text without icon
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-error ant-alert-banner css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="close-circle"
      class="anticon anticon-close-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="close-circle"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.*********** 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .***********.09l45.02 45.02a.2.2 0 ***********.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.***********.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Error text
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/alert/demo/banner.tsx extend context correctly 2`] = `[]`;

exports[`renders components/alert/demo/basic.tsx extend context correctly 1`] = `
<div
  class="ant-alert ant-alert-success ant-alert-no-icon css-var-test-id"
  data-show="true"
  role="alert"
>
  <div
    class="ant-alert-section"
  >
    <div
      class="ant-alert-title"
    >
      Success Text
    </div>
  </div>
</div>
`;

exports[`renders components/alert/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/alert/demo/closable.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-alert ant-alert-warning ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Warning Text Warning Text Warning TextW arning Text Warning Text Warning TextWarning Text
      </div>
    </div>
    <button
      class="ant-alert-close-icon"
      tabindex="0"
      type="button"
    >
      <span
        aria-label="close"
        class="anticon anticon-close"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </button>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-error ant-alert-with-description ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Error Text
      </div>
      <div
        class="ant-alert-description"
      >
        Error Description Error Description Error Description Error Description Error Description Error Description
      </div>
    </div>
    <button
      class="ant-alert-close-icon"
      tabindex="0"
      type="button"
    >
      <span
        aria-label="close"
        class="anticon anticon-close"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </button>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-error ant-alert-with-description ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Error Text
      </div>
      <div
        class="ant-alert-description"
      >
        Error Description Error Description Error Description Error Description Error Description Error Description
      </div>
    </div>
    <button
      aria-label="close"
      class="ant-alert-close-icon"
      tabindex="0"
      type="button"
    >
      <span
        aria-label="close-square"
        class="anticon anticon-close-square"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close-square"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M880 112c17.7 0 32 14.3 32 32v736c0 17.7-14.3 32-32 32H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32zm-40 72H184v656h656V184zM640.01 338.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 ***********.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.*********** 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </button>
  </div>,
]
`;

exports[`renders components/alert/demo/closable.tsx extend context correctly 2`] = `[]`;

exports[`renders components/alert/demo/custom-icon.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-alert ant-alert-success ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        showIcon = false
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-success css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="smile"
      class="anticon anticon-smile ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="smile"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Success Tips
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-info css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="smile"
      class="anticon anticon-smile ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="smile"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Informational Notes
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-warning css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="smile"
      class="anticon anticon-smile ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="smile"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Warning
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-error css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="smile"
      class="anticon anticon-smile ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="smile"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Error
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-success ant-alert-with-description css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="smile"
      class="anticon anticon-smile ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="smile"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Success Tips
      </div>
      <div
        class="ant-alert-description"
      >
        Detailed description and advice about successful copywriting.
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-info ant-alert-with-description css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="smile"
      class="anticon anticon-smile ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="smile"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Informational Notes
      </div>
      <div
        class="ant-alert-description"
      >
        Additional description and information about copywriting.
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-warning ant-alert-with-description css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="smile"
      class="anticon anticon-smile ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="smile"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Warning
      </div>
      <div
        class="ant-alert-description"
      >
        This is a warning notice about copywriting.
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-error ant-alert-with-description css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="smile"
      class="anticon anticon-smile ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="smile"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Error
      </div>
      <div
        class="ant-alert-description"
      >
        This is an error message about copywriting.
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/alert/demo/custom-icon.tsx extend context correctly 2`] = `[]`;

exports[`renders components/alert/demo/description.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-alert ant-alert-success ant-alert-with-description ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Success Text
      </div>
      <div
        class="ant-alert-description"
      >
        Success Description Success Description Success Description
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-info ant-alert-with-description ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Info Text
      </div>
      <div
        class="ant-alert-description"
      >
        Info Description Info Description Info Description Info Description
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-warning ant-alert-with-description ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Warning Text
      </div>
      <div
        class="ant-alert-description"
      >
        Warning Description Warning Description Warning Description Warning Description
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-error ant-alert-with-description ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Error Text
      </div>
      <div
        class="ant-alert-description"
      >
        Error Description Error Description Error Description Error Description
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/alert/demo/description.tsx extend context correctly 2`] = `[]`;

exports[`renders components/alert/demo/error-boundary.tsx extend context correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined"
  type="button"
>
  <span>
    Click me to throw a error
  </span>
</button>
`;

exports[`renders components/alert/demo/error-boundary.tsx extend context correctly 2`] = `[]`;

exports[`renders components/alert/demo/icon.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-alert ant-alert-success css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="check-circle"
      class="anticon anticon-check-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="check-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Success Tips
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-info css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="info-circle"
      class="anticon anticon-info-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="info-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Informational Notes
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-warning css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="exclamation-circle"
      class="anticon anticon-exclamation-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="exclamation-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Warning
      </div>
    </div>
    <button
      class="ant-alert-close-icon"
      tabindex="0"
      type="button"
    >
      <span
        aria-label="close"
        class="anticon anticon-close"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </button>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-error css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="close-circle"
      class="anticon anticon-close-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="close-circle"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.*********** 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .***********.09l45.02 45.02a.2.2 0 ***********.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.***********.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Error
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-success ant-alert-with-description css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="check-circle"
      class="anticon anticon-check-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="check-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Success Tips
      </div>
      <div
        class="ant-alert-description"
      >
        Detailed description and advice about successful copywriting.
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-info ant-alert-with-description css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="info-circle"
      class="anticon anticon-info-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="info-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Informational Notes
      </div>
      <div
        class="ant-alert-description"
      >
        Additional description and information about copywriting.
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-warning ant-alert-with-description css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="exclamation-circle"
      class="anticon anticon-exclamation-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="exclamation-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Warning
      </div>
      <div
        class="ant-alert-description"
      >
        This is a warning notice about copywriting.
      </div>
    </div>
    <button
      class="ant-alert-close-icon"
      tabindex="0"
      type="button"
    >
      <span
        aria-label="close"
        class="anticon anticon-close"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </button>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-error ant-alert-with-description css-var-test-id"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="close-circle"
      class="anticon anticon-close-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="close-circle"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.*********** 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .***********.09l45.02 45.02a.2.2 0 ***********.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.***********.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Error
      </div>
      <div
        class="ant-alert-description"
      >
        This is an error message about copywriting.
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/alert/demo/icon.tsx extend context correctly 2`] = `[]`;

exports[`renders components/alert/demo/smooth-closed.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-alert ant-alert-success ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Alert Message Text
      </div>
    </div>
    <button
      class="ant-alert-close-icon"
      tabindex="0"
      type="button"
    >
      <span
        aria-label="close"
        class="anticon anticon-close"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </button>
  </div>,
  <p>
    click the close button to see the effect
  </p>,
  <button
    aria-checked="true"
    class="ant-switch css-var-test-id ant-switch-checked ant-switch-disabled"
    disabled=""
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>,
]
`;

exports[`renders components/alert/demo/smooth-closed.tsx extend context correctly 2`] = `[]`;

exports[`renders components/alert/demo/style.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-alert ant-alert-success ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Success Text
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-info ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Info Text
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-warning ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Warning Text
      </div>
    </div>
  </div>,
  <br />,
  <div
    class="ant-alert ant-alert-error ant-alert-no-icon css-var-test-id"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-section"
    >
      <div
        class="ant-alert-title"
      >
        Error Text
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/alert/demo/style.tsx extend context correctly 2`] = `[]`;
