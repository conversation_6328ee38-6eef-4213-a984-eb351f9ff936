// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/color-picker/demo/allowClear.tsx correctly 1`] = `
<div
  aria-describedby="test-id"
  class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
>
  <div
    class="ant-color-picker-color-block"
  >
    <div
      class="ant-color-picker-color-block-inner"
      style="background:rgb(22,119,255)"
    />
  </div>
</div>
`;

exports[`renders components/color-picker/demo/base.tsx correctly 1`] = `
<div
  aria-describedby="test-id"
  class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
>
  <div
    class="ant-color-picker-color-block"
  >
    <div
      class="ant-color-picker-color-block-inner"
      style="background:rgb(22,119,255)"
    />
  </div>
</div>
`;

exports[`renders components/color-picker/demo/controlled.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      aria-describedby="test-id"
      class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
    >
      <div
        class="ant-color-picker-color-block"
      >
        <div
          class="ant-color-picker-color-block-inner"
          style="background:rgb(22,119,255)"
        />
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      aria-describedby="test-id"
      class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
    >
      <div
        class="ant-color-picker-color-block"
      >
        <div
          class="ant-color-picker-color-block-inner"
          style="background:rgb(22,119,255)"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/color-picker/demo/disabled.tsx correctly 1`] = `
<div
  aria-describedby="test-id"
  class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var ant-color-picker-trigger-disabled"
>
  <div
    class="ant-color-picker-color-block"
  >
    <div
      class="ant-color-picker-color-block-inner"
      style="background:rgb(22,119,255)"
    />
  </div>
  <div
    class="ant-color-picker-trigger-text"
  >
    #1677FF
  </div>
</div>
`;

exports[`renders components/color-picker/demo/disabled-alpha.tsx correctly 1`] = `
<div
  aria-describedby="test-id"
  class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
>
  <div
    class="ant-color-picker-color-block"
  >
    <div
      class="ant-color-picker-color-block-inner"
      style="background:rgb(22,119,255)"
    />
  </div>
</div>
`;

exports[`renders components/color-picker/demo/format.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-middle ant-space-gap-col-middle css-var-test-id"
  style="display:flex"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    >
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(22,119,255)"
            />
          </div>
        </div>
      </div>
      <div
        class="ant-space-item"
      >
        <span>
          HEX: 
          <!-- -->
          #1677ff
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    >
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(23,120,255)"
            />
          </div>
        </div>
      </div>
      <div
        class="ant-space-item"
      >
        <span>
          HSB: 
          <!-- -->
          hsb(215, 91%, 100%)
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    >
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(22,119,255)"
            />
          </div>
        </div>
      </div>
      <div
        class="ant-space-item"
      >
        <span>
          RGB: 
          <!-- -->
          rgb(22, 119, 255)
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/color-picker/demo/line-gradient.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      aria-describedby="test-id"
      class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
    >
      <div
        class="ant-color-picker-color-block"
      >
        <div
          class="ant-color-picker-color-block-inner"
          style="background:linear-gradient(90deg, rgb(16,142,233) 0%, rgb(135,208,104) 100%)"
        />
      </div>
      <div
        class="ant-color-picker-trigger-text"
      >
        <span
          class="ant-color-picker-trigger-text-cell"
        >
          rgb(16,142,233)
          <!-- -->
          <!-- -->
          0
          <!-- -->
          %
        </span>
        <span
          class="ant-color-picker-trigger-text-cell"
        >
          rgb(135,208,104)
          <!-- -->
          <!-- -->
          100
          <!-- -->
          %
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      aria-describedby="test-id"
      class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
    >
      <div
        class="ant-color-picker-color-block"
      >
        <div
          class="ant-color-picker-color-block-inner"
          style="background:linear-gradient(90deg, rgb(16,142,233) 0%, rgb(135,208,104) 100%)"
        />
      </div>
      <div
        class="ant-color-picker-trigger-text"
      >
        <span
          class="ant-color-picker-trigger-text-cell"
        >
          rgb(16,142,233)
          <!-- -->
          <!-- -->
          0
          <!-- -->
          %
        </span>
        <span
          class="ant-color-picker-trigger-text-cell"
        >
          rgb(135,208,104)
          <!-- -->
          <!-- -->
          100
          <!-- -->
          %
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/color-picker/demo/panel-render.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    >
      <div
        class="ant-space-item"
      >
        <span>
          Add title:
        </span>
      </div>
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(22,119,255)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    >
      <div
        class="ant-space-item"
      >
        <span>
          Horizontal layout:
        </span>
      </div>
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(22,119,255)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/color-picker/demo/presets.tsx correctly 1`] = `
<div
  aria-describedby="test-id"
  class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
>
  <div
    class="ant-color-picker-color-block"
  >
    <div
      class="ant-color-picker-color-block-inner"
      style="background:rgb(22,119,255)"
    />
  </div>
</div>
`;

exports[`renders components/color-picker/demo/pure-panel.tsx correctly 1`] = `
<div
  style="padding-inline-start:100px"
>
  <div
    style="padding-bottom:0;position:relative;min-width:0"
  >
    <div
      aria-describedby="test-id"
      class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
      style="margin:0"
    >
      <div
        class="ant-color-picker-color-block"
      >
        <div
          class="ant-color-picker-color-block-inner"
          style="background:rgb(22,119,255)"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/color-picker/demo/size.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    >
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger ant-color-picker-sm css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(22,119,255)"
            />
          </div>
        </div>
      </div>
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(22,119,255)"
            />
          </div>
        </div>
      </div>
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger ant-color-picker-lg css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(22,119,255)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    >
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger ant-color-picker-sm css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(22,119,255)"
            />
          </div>
          <div
            class="ant-color-picker-trigger-text"
          >
            #1677FF
          </div>
        </div>
      </div>
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(22,119,255)"
            />
          </div>
          <div
            class="ant-color-picker-trigger-text"
          >
            #1677FF
          </div>
        </div>
      </div>
      <div
        class="ant-space-item"
      >
        <div
          aria-describedby="test-id"
          class="ant-color-picker-trigger ant-color-picker-lg css-var-test-id ant-color-picker-css-var"
        >
          <div
            class="ant-color-picker-color-block"
          >
            <div
              class="ant-color-picker-color-block-inner"
              style="background:rgb(22,119,255)"
            />
          </div>
          <div
            class="ant-color-picker-trigger-text"
          >
            #1677FF
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/color-picker/demo/text-render.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      aria-describedby="test-id"
      class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
    >
      <div
        class="ant-color-picker-color-block"
      >
        <div
          class="ant-color-picker-color-block-inner"
          style="background:rgb(22,119,255)"
        />
      </div>
      <div
        class="ant-color-picker-trigger-text"
      >
        #1677FF
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      aria-describedby="test-id"
      class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
    >
      <div
        class="ant-color-picker-color-block"
      >
        <div
          class="ant-color-picker-color-block-inner"
          style="background:rgb(22,119,255)"
        />
      </div>
      <div
        class="ant-color-picker-trigger-text"
      >
        <span>
          Custom Text (
          <!-- -->
          #1677ff
          <!-- -->
          )
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      aria-describedby="test-id"
      class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
    >
      <div
        class="ant-color-picker-color-block"
      >
        <div
          class="ant-color-picker-color-block-inner"
          style="background:rgb(22,119,255)"
        />
      </div>
      <div
        class="ant-color-picker-trigger-text"
      >
        <span
          aria-label="down"
          class="anticon anticon-down"
          role="img"
          style="color:rgba(0, 0, 0, 0.25)"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/color-picker/demo/trigger.tsx correctly 1`] = `
<button
  aria-describedby="test-id"
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  style="background-color:#1677ff"
  type="button"
>
  <span>
    open
  </span>
</button>
`;

exports[`renders components/color-picker/demo/trigger-event.tsx correctly 1`] = `
<div
  aria-describedby="test-id"
  class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
>
  <div
    class="ant-color-picker-color-block"
  >
    <div
      class="ant-color-picker-color-block-inner"
      style="background:rgb(22,119,255)"
    />
  </div>
</div>
`;
