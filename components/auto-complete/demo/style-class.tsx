import React from 'react';
import { AutoComplete, Flex, Space } from 'antd';
import type { AutoCompleteProps } from 'antd';

const classNamesObject: AutoCompleteProps['classNames'] = {
  root: 'demo-autocomplete-root',
  prefix: 'demo-autocomplete-prefix',
  input: 'demo-autocomplete-input',
};

const classNamesFn = (info: { props: AutoCompleteProps }) => {
  if (info.props.status === 'error') {
    return { root: 'demo-autocomplete-root--error' };
  }
  return { root: 'demo-autocomplete-root--normal' };
};

const stylesObject: AutoCompleteProps['styles'] = {
  root: { borderWidth: 2, borderStyle: 'dashed', padding: 4 },
  prefix: { backgroundColor: '#f5f5f5', padding: '4px 8px' },
  input: { fontWeight: 'bold', color: '#1890ff' },
};

const stylesFn = (info: { props: AutoCompleteProps }) => {
  if (info.props.status === 'warning') {
    return { 
      root: { backgroundColor: '#fff7e6', borderColor: '#ffa940' },
      input: { color: '#fa8c16' }
    };
  }
  return { 
    root: { backgroundColor: '#f6ffed', borderColor: '#52c41a' },
    input: { color: '#389e0d' }
  };
};

const mockVal = (str: string, repeat = 1) => ({
  value: str.repeat(repeat),
});

const options = [
  mockVal('Burns Bay Road'),
  mockVal('Downing Street'),
  mockVal('Wall Street'),
];

const App: React.FC = () => {
  return (
    <Space size={[8, 24]} direction="vertical" style={{ width: '100%' }}>
      <Flex vertical gap="middle">
        <h4>classNames Object</h4>
        <AutoComplete
          options={options}
          placeholder="input here"
          classNames={classNamesObject}
          style={{ width: 200 }}
        />
      </Flex>
      <Flex vertical gap="middle">
        <h4>classNames Function</h4>
        <AutoComplete
          options={options}
          placeholder="input here"
          status="error"
          classNames={classNamesFn as AutoCompleteProps['classNames']}
          style={{ width: 200 }}
        />
      </Flex>
      <Flex vertical gap="middle">
        <h4>styles Object</h4>
        <AutoComplete
          options={options}
          placeholder="input here"
          styles={stylesObject}
          style={{ width: 200 }}
        />
      </Flex>
      <Flex vertical gap="middle">
        <h4>styles Function</h4>
        <AutoComplete
          options={options}
          placeholder="input here"
          status="warning"
          styles={stylesFn as AutoCompleteProps['styles']}
          style={{ width: 200 }}
        />
      </Flex>
    </Space>
  );
};

export default App;
