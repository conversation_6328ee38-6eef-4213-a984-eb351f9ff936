// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`AutoComplete rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-select ant-select-rtl ant-select-outlined ant-select-auto-complete css-var-root ant-select-css-var ant-select-single ant-select-show-search"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          role="combobox"
          type="search"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
</div>
`;
