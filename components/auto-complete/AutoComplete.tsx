import * as React from 'react';
import type { BaseSelectRef } from '@rc-component/select';
import toArray from '@rc-component/util/lib/Children/toArray';
import omit from '@rc-component/util/lib/omit';
import cls from 'classnames';

import useMergeSemantic from '../_util/hooks/useMergeSemantic';
import type { SemanticClassNamesType, SemanticStylesType } from '../_util/hooks/useMergeSemantic';
import type { InputStatus } from '../_util/statusUtils';
import { devUseWarning } from '../_util/warning';
import type { ConfigConsumerProps } from '../config-provider';
import { ConfigContext, useComponentConfig } from '../config-provider';
import type {
  BaseOptionType,
  DefaultOptionType,
  InternalSelectProps,
  RefSelectProps,
  SelectProps,
} from '../select';
import Select from '../select';

export type AutoCompleteSemanticName = 'root' | 'prefix' | 'input';
type PopupSemantic = 'root' | 'listItem' | 'list';

const { Option } = Select;

export interface DataSourceItemObject {
  value: string;
  text: string;
}
export type DataSourceItemType = DataSourceItemObject | React.ReactNode;

export interface BaseAutoCompleteProps {
  /** @deprecated Please use `options` instead */
  dataSource?: DataSourceItemType[];
  status?: InputStatus;
  popupMatchSelectWidth?: boolean | number;
  classNames?: AutoCompleteClassNamesType;
  styles?: AutoCompleteStylesType;
  popupRender?: (menu: React.ReactElement) => React.ReactElement;
  onOpenChange?: (visible: boolean) => void;
}

export type AutoCompleteClassNamesType = SemanticClassNamesType<BaseAutoCompleteProps, AutoCompleteSemanticName>;
export type AutoCompleteStylesType = SemanticStylesType<BaseAutoCompleteProps, AutoCompleteSemanticName>;

export interface AutoCompleteProps<
  ValueType = any,
  OptionType extends BaseOptionType | DefaultOptionType = DefaultOptionType,
> extends BaseAutoCompleteProps,
    Omit<
      InternalSelectProps<ValueType, OptionType>,
      'loading' | 'mode' | 'optionLabelProp' | 'labelInValue' | 'classNames' | 'styles'
    > {
  /** @deprecated Please use `classNames.popup.root` instead */
  popupClassName?: string;
  /** @deprecated Please use `classNames.popup.root` instead */
  dropdownClassName?: string;
  /** @deprecated Please use `popupMatchSelectWidth` instead */
  dropdownMatchSelectWidth?: boolean | number;
  styles?: (Partial<Record<AutoCompleteSemanticName, React.CSSProperties>> & {
    popup?: Partial<Record<PopupSemantic, React.CSSProperties>>;
  }) | ((info: { props: BaseAutoCompleteProps }) => Partial<Record<AutoCompleteSemanticName, React.CSSProperties>> & {
    popup?: Partial<Record<PopupSemantic, React.CSSProperties>>;
  });
  classNames?: (Partial<Record<AutoCompleteSemanticName, string>> & {
    popup?: Partial<Record<PopupSemantic, string>>;
  }) | ((info: { props: BaseAutoCompleteProps }) => Partial<Record<AutoCompleteSemanticName, string>> & {
    popup?: Partial<Record<PopupSemantic, string>>;
  });
  /** @deprecated Please use `popupRender` instead */
  dropdownRender?: (menu: React.ReactElement) => React.ReactElement;
  /** @deprecated Please use `styles.popup.root` instead */
  dropdownStyle?: React.CSSProperties;
  /** @deprecated Please use `onOpenChange` instead */
  onDropdownVisibleChange?: (visible: boolean) => void;
}

function isSelectOptionOrSelectOptGroup(child: any): boolean {
  return child?.type && (child.type.isSelectOption || child.type.isSelectOptGroup);
}

const AutoComplete: React.ForwardRefRenderFunction<RefSelectProps, AutoCompleteProps> = (
  props,
  ref,
) => {
  const {
    prefixCls: customizePrefixCls,
    className,
    style,
    popupClassName,
    dropdownClassName,
    children,
    dataSource,
    rootClassName,
    dropdownStyle,
    dropdownRender,
    popupRender,
    onDropdownVisibleChange,
    onOpenChange,
    styles,
    classNames,
  } = props;
  const childNodes: React.ReactElement[] = toArray(children);

  const mergedPopupRender = popupRender || dropdownRender;
  const mergedOnOpenChange = onOpenChange || onDropdownVisibleChange;

  // ============================= Input =============================
  let customizeInput: React.ReactElement | undefined;

  if (
    childNodes.length === 1 &&
    React.isValidElement(childNodes[0]) &&
    !isSelectOptionOrSelectOptGroup(childNodes[0])
  ) {
    [customizeInput] = childNodes;
  }

  const getInputElement = customizeInput ? (): React.ReactElement => customizeInput! : undefined;

  // ============================ Options ============================
  let optionChildren: React.ReactNode;

  // [Legacy] convert `children` or `dataSource` into option children
  if (childNodes.length && isSelectOptionOrSelectOptGroup(childNodes[0])) {
    optionChildren = children;
  } else {
    optionChildren = dataSource
      ? dataSource.map((item) => {
          if (React.isValidElement(item)) {
            return item;
          }
          switch (typeof item) {
            case 'string':
              return (
                <Option key={item} value={item}>
                  {item}
                </Option>
              );
            case 'object': {
              const { value: optionValue } = item as DataSourceItemObject;
              return (
                <Option key={optionValue} value={optionValue}>
                  {(item as DataSourceItemObject).text}
                </Option>
              );
            }
            default:
              return undefined;
          }
        })
      : [];
  }
  // ====================== Warning ======================
  if (process.env.NODE_ENV !== 'production') {
    const warning = devUseWarning('AutoComplete');

    warning(
      !customizeInput || !('size' in props),
      'usage',
      'You need to control style self instead of setting `size` when using customize input.',
    );

    const deprecatedProps = {
      dropdownMatchSelectWidth: 'popupMatchSelectWidth',
      dropdownStyle: 'styles.popup.root',
      dropdownClassName: 'classNames.popup.root',
      popupClassName: 'classNames.popup.root',
      dropdownRender: 'popupRender',
      onDropdownVisibleChange: 'onOpenChange',
      dataSource: 'options',
    };

    Object.entries(deprecatedProps).forEach(([oldProp, newProp]) => {
      warning.deprecated(!(oldProp in props), oldProp, newProp);
    });
  }

  const {
    getPrefixCls,
    className: contextClassName,
    style: contextStyle,
    classNames: contextClassNames,
    styles: contextStyles,
  } = useComponentConfig('autoComplete');

  const prefixCls = getPrefixCls('select', customizePrefixCls);

  // =========== Merged Props for Semantic ===========
  const mergedProps = React.useMemo<BaseAutoCompleteProps>(() => {
    return {
      ...props,
      dataSource,
      status: props.status,
      popupMatchSelectWidth: props.popupMatchSelectWidth || props.dropdownMatchSelectWidth,
      popupRender: mergedPopupRender,
      onOpenChange: mergedOnOpenChange,
    };
  }, [props, dataSource, mergedPopupRender, mergedOnOpenChange]);

  // ========================= Style ==========================
  const [mergedClassNames, mergedStyles] = useMergeSemantic<
    AutoCompleteClassNamesType,
    AutoCompleteStylesType,
    BaseAutoCompleteProps
  >(
    [contextClassNames, classNames],
    [contextStyles, styles],
    {
      popup: {
        _default: 'root',
      },
    },
    {
      props: mergedProps,
    },
  );

  const finalClassNames = {
    root: cls(`${prefixCls}-auto-complete`, contextClassName, className, rootClassName, mergedClassNames?.root),
    prefix: mergedClassNames?.prefix,
    input: mergedClassNames?.input,
    popup: {
      root: cls(popupClassName, dropdownClassName, (mergedClassNames as any).popup?.root),
      list: (mergedClassNames as any).popup?.list,
      listItem: (mergedClassNames as any).popup?.listItem,
    },
  };

  const finalStyles = {
    root: { ...contextStyle, ...mergedStyles?.root, ...style },
    input: mergedStyles?.input,
    prefix: mergedStyles?.prefix,
    popup: {
      root: { ...dropdownStyle, ...(mergedStyles as any).popup?.root },
      list: (mergedStyles as any).popup?.list,
      listItem: (mergedStyles as any).popup?.listItem,
    },
  };

  return (
    <Select
      ref={ref}
      suffixIcon={null}
      {...omit(props, ['dataSource', 'dropdownClassName', 'popupClassName'])}
      prefixCls={prefixCls}
      classNames={finalClassNames}
      styles={finalStyles}
      mode={Select.SECRET_COMBOBOX_MODE_DO_NOT_USE as SelectProps['mode']}
      popupRender={mergedPopupRender}
      onPopupVisibleChange={mergedOnOpenChange}
      {...{
        // Internal api
        getInputElement,
      }}
    >
      {optionChildren}
    </Select>
  );
};

const RefAutoComplete = React.forwardRef<RefSelectProps, AutoCompleteProps>(
  AutoComplete,
) as unknown as (<
  ValueType = any,
  OptionType extends BaseOptionType | DefaultOptionType = DefaultOptionType,
>(
  props: React.PropsWithChildren<AutoCompleteProps<ValueType, OptionType>> &
    React.RefAttributes<BaseSelectRef>,
) => React.ReactElement) & {
  displayName?: string;
};

if (process.env.NODE_ENV !== 'production') {
  RefAutoComplete.displayName = 'AutoComplete';
}

export default RefAutoComplete;
