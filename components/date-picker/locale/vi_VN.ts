import CalendarLocale from '@rc-component/picker/lib/locale/vi_VN';

import TimePickerLocale from '../../time-picker/locale/vi_VN';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: '<PERSON>ọn thời điểm',
    yearPlaceholder: 'Chọ<PERSON> năm',
    quarterPlaceholder: 'Chọn quý',
    monthPlaceholder: 'Chọ<PERSON> tháng',
    weekPlaceholder: 'Chọn tuần',
    rangePlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeQuarterPlaceholder: ['<PERSON>u<PERSON> bắt đầu', '<PERSON>u<PERSON> kết thúc'],
    rangeMonthPlaceholder: ['<PERSON>h<PERSON><PERSON> bắt đầu', '<PERSON>h<PERSON><PERSON> kết thúc'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON>ầ<PERSON> kết thúc'],
    shortMonths: [
      'Th 01',
      'Th 02',
      'Th 03',
      'Th 04',
      'Th 05',
      'Th 06',
      'Th 07',
      'Th 08',
      'Th 09',
      'Th 10',
      'Th 11',
      'Th 12',
    ],
    shortWeekDays: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
