// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`QuarterPicker should support style prop 1`] = `
<div
  class="ant-picker ant-picker-outlined css-var-root ant-picker-css-var"
  style="width: 400px;"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select quarter"
      size="12"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="calendar"
        class="anticon anticon-calendar"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="calendar"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;
