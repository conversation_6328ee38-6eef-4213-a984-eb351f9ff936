// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`mount rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-picker ant-picker-rtl ant-picker-outlined css-var-root ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select date"
      size="12"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="calendar"
        class="anticon anticon-calendar"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="calendar"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`mount rtl render component should be rendered correctly in RTL direction 2`] = `
<div
  class="ant-picker ant-picker-rtl ant-picker-outlined css-var-root ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select month"
      size="12"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="calendar"
        class="anticon anticon-calendar"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="calendar"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`mount rtl render component should be rendered correctly in RTL direction 3`] = `
<div
  class="ant-picker ant-picker-rtl ant-picker-outlined css-var-root ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select week"
      size="12"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="calendar"
        class="anticon anticon-calendar"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="calendar"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`mount rtl render component should be rendered correctly in RTL direction 4`] = `
<div
  class="ant-picker ant-picker-range ant-picker-rtl ant-picker-outlined css-var-root ant-picker-css-var"
>
  <div
    class="ant-picker-input ant-picker-input-start"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      date-range="start"
      placeholder="Start date"
      size="12"
      value=""
    />
  </div>
  <div
    class="ant-picker-range-separator"
  >
    <span
      aria-label="to"
      class="ant-picker-separator"
    >
      <span
        aria-label="swap-right"
        class="anticon anticon-swap-right"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="swap-right"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-picker-input ant-picker-input-end"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      date-range="end"
      placeholder="End date"
      size="12"
      value=""
    />
  </div>
  <div
    class="ant-picker-active-bar"
    style="position: absolute; width: 0px;"
  />
  <span
    class="ant-picker-suffix"
  >
    <span
      aria-label="calendar"
      class="anticon anticon-calendar"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="calendar"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
        />
      </svg>
    </span>
  </span>
</div>
`;
