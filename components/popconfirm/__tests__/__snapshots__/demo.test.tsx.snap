// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/popconfirm/demo/async.tsx correctly 1`] = `
<button
  aria-describedby="test-id"
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open Popconfirm with async logic
  </span>
</button>
`;

exports[`renders components/popconfirm/demo/basic.tsx correctly 1`] = `
<button
  aria-describedby="test-id"
  class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined"
  type="button"
>
  <span>
    Delete
  </span>
</button>
`;

exports[`renders components/popconfirm/demo/dynamic-trigger.tsx correctly 1`] = `
<div>
  <button
    aria-describedby="test-id"
    class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined"
    type="button"
  >
    <span>
      Delete a task
    </span>
  </button>
  <br />
  <br />
  Whether directly execute：
  <button
    aria-checked="true"
    class="ant-switch css-var-test-id ant-switch-checked"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>
</div>
`;

exports[`renders components/popconfirm/demo/icon.tsx correctly 1`] = `
<button
  aria-describedby="test-id"
  class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined"
  type="button"
>
  <span>
    Delete
  </span>
</button>
`;

exports[`renders components/popconfirm/demo/locale.tsx correctly 1`] = `
<button
  aria-describedby="test-id"
  class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined"
  type="button"
>
  <span>
    Delete
  </span>
</button>
`;

exports[`renders components/popconfirm/demo/placement.tsx correctly 1`] = `
<div
  class="demo ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center ant-flex-vertical"
>
  <div
    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
    style="white-space:nowrap"
  >
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width:80px;margin:4px"
      type="button"
    >
      <span>
        TL
      </span>
    </button>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width:80px;margin:4px"
      type="button"
    >
      <span>
        Top
      </span>
    </button>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width:80px;margin:4px"
      type="button"
    >
      <span>
        TR
      </span>
    </button>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
    style="width:432px"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-vertical"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width:80px;margin:4px"
        type="button"
      >
        <span>
          LT
        </span>
      </button>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width:80px;margin:4px"
        type="button"
      >
        <span>
          Left
        </span>
      </button>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width:80px;margin:4px"
        type="button"
      >
        <span>
          LB
        </span>
      </button>
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-vertical"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width:80px;margin:4px"
        type="button"
      >
        <span>
          RT
        </span>
      </button>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width:80px;margin:4px"
        type="button"
      >
        <span>
          Right
        </span>
      </button>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width:80px;margin:4px"
        type="button"
      >
        <span>
          RB
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
    style="white-space:nowrap"
  >
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width:80px;margin:4px"
      type="button"
    >
      <span>
        BL
      </span>
    </button>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width:80px;margin:4px"
      type="button"
    >
      <span>
        Bottom
      </span>
    </button>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width:80px;margin:4px"
      type="button"
    >
      <span>
        BR
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/popconfirm/demo/promise.tsx correctly 1`] = `
<button
  aria-describedby="test-id"
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Open Popconfirm with Promise
  </span>
</button>
`;

exports[`renders components/popconfirm/demo/render-panel.tsx correctly 1`] = `
Array [
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-top ant-popconfirm css-var-test-id"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-inner-content"
        >
          <div
            class="ant-popconfirm-inner-content"
          >
            <div
              class="ant-popconfirm-message"
            >
              <span
                class="ant-popconfirm-message-icon"
              >
                <span
                  aria-label="exclamation-circle"
                  class="anticon anticon-exclamation-circle"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="exclamation-circle"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                    />
                  </svg>
                </span>
              </span>
              <div
                class="ant-popconfirm-message-text"
              >
                <div
                  class="ant-popconfirm-title"
                >
                  Are you OK?
                </div>
                <div
                  class="ant-popconfirm-description"
                >
                  Does this look good?
                </div>
              </div>
            </div>
            <div
              class="ant-popconfirm-buttons"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm"
                type="button"
              >
                <span>
                  Cancel
                </span>
              </button>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-bottomRight ant-popconfirm css-var-test-id"
    style="width:250px"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
      style="width:250px"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-inner-content"
        >
          <div
            class="ant-popconfirm-inner-content"
          >
            <div
              class="ant-popconfirm-message"
            >
              <span
                class="ant-popconfirm-message-icon"
              >
                <span
                  aria-label="exclamation-circle"
                  class="anticon anticon-exclamation-circle"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="exclamation-circle"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                    />
                  </svg>
                </span>
              </span>
              <div
                class="ant-popconfirm-message-text"
              >
                <div
                  class="ant-popconfirm-title"
                >
                  Are you OK?
                </div>
                <div
                  class="ant-popconfirm-description"
                >
                  Does this look good?
                </div>
              </div>
            </div>
            <div
              class="ant-popconfirm-buttons"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm"
                type="button"
              >
                <span>
                  Cancel
                </span>
              </button>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-top ant-popconfirm css-var-test-id"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-inner-content"
        >
          <div
            class="ant-popconfirm-inner-content"
          >
            <div
              class="ant-popconfirm-message"
            >
              <div
                class="ant-popconfirm-message-text"
              >
                <div
                  class="ant-popconfirm-title"
                >
                  Are you OK?
                </div>
              </div>
            </div>
            <div
              class="ant-popconfirm-buttons"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm"
                type="button"
              >
                <span>
                  Cancel
                </span>
              </button>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-top ant-popconfirm css-var-test-id"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-inner-content"
        >
          <div
            class="ant-popconfirm-inner-content"
          >
            <div
              class="ant-popconfirm-message"
            >
              <div
                class="ant-popconfirm-message-text"
              >
                <div
                  class="ant-popconfirm-title"
                >
                  Are you OK?
                </div>
                <div
                  class="ant-popconfirm-description"
                >
                  Does this look good?
                </div>
              </div>
            </div>
            <div
              class="ant-popconfirm-buttons"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm"
                type="button"
              >
                <span>
                  Cancel
                </span>
              </button>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/popconfirm/demo/shift.tsx correctly 1`] = `
<div
  style="width:300vw;height:300vh;display:flex;align-items:center;justify-content:center"
>
  <button
    class="ant-btn ant-btn-primary ant-popover-open"
    type="button"
  >
    <span>
      Scroll The Window
    </span>
  </button>
  <div
    class="ant-popover ant-popconfirm ant-popover-placement-top"
    style="--arrow-x:0px;--arrow-y:0px;left:-1000vw;top:-1000vh;right:auto;bottom:auto;box-sizing:border-box"
  >
    <div
      class="ant-popover-arrow"
      style="position:absolute;bottom:0;left:0"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-inner-content"
        >
          <div
            class="ant-popconfirm-inner-content"
          >
            <div
              class="ant-popconfirm-message"
            >
              <span
                class="ant-popconfirm-message-icon"
              >
                <span
                  aria-label="exclamation-circle"
                  class="anticon anticon-exclamation-circle"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="exclamation-circle"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                    />
                  </svg>
                </span>
              </span>
              <div
                class="ant-popconfirm-message-text"
              >
                <div
                  class="ant-popconfirm-title"
                >
                  Thanks for using antd. Have a nice day !
                </div>
              </div>
            </div>
            <div
              class="ant-popconfirm-buttons"
            >
              <button
                class="ant-btn ant-btn-default ant-btn-sm"
                type="button"
              >
                <span>
                  Cancel
                </span>
              </button>
              <button
                class="ant-btn ant-btn-primary ant-btn-sm"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/popconfirm/demo/wireframe.tsx correctly 1`] = `
Array [
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-top ant-popconfirm css-var-test-id"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-inner-content"
        >
          <div
            class="ant-popconfirm-inner-content"
          >
            <div
              class="ant-popconfirm-message"
            >
              <span
                class="ant-popconfirm-message-icon"
              >
                <span
                  aria-label="exclamation-circle"
                  class="anticon anticon-exclamation-circle"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="exclamation-circle"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                    />
                  </svg>
                </span>
              </span>
              <div
                class="ant-popconfirm-message-text"
              >
                <div
                  class="ant-popconfirm-title"
                >
                  Are you OK?
                </div>
              </div>
            </div>
            <div
              class="ant-popconfirm-buttons"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm"
                type="button"
              >
                <span>
                  Cancel
                </span>
              </button>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-bottomRight ant-popconfirm css-var-test-id"
    style="width:250px"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
      style="width:250px"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-inner-content"
        >
          <div
            class="ant-popconfirm-inner-content"
          >
            <div
              class="ant-popconfirm-message"
            >
              <span
                class="ant-popconfirm-message-icon"
              >
                <span
                  aria-label="exclamation-circle"
                  class="anticon anticon-exclamation-circle"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="exclamation-circle"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                    />
                  </svg>
                </span>
              </span>
              <div
                class="ant-popconfirm-message-text"
              >
                <div
                  class="ant-popconfirm-title"
                >
                  Are you OK?
                </div>
              </div>
            </div>
            <div
              class="ant-popconfirm-buttons"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm"
                type="button"
              >
                <span>
                  Cancel
                </span>
              </button>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
                type="button"
              >
                <span>
                  OK
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;
