// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Input.OTP rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-otp ant-otp-rtl css-var-root"
  role="group"
>
  <span
    class="ant-otp-input-wrapper"
    role="presentation"
  >
    <input
      aria-label="OTP Input 1"
      class="ant-input ant-input-rtl ant-input-outlined ant-otp-input css-var-root ant-input-css-var"
      size="1"
      type="text"
      value=""
    />
  </span>
  <span
    class="ant-otp-input-wrapper"
    role="presentation"
  >
    <input
      aria-label="OTP Input 2"
      class="ant-input ant-input-rtl ant-input-outlined ant-otp-input css-var-root ant-input-css-var"
      size="1"
      type="text"
      value=""
    />
  </span>
  <span
    class="ant-otp-input-wrapper"
    role="presentation"
  >
    <input
      aria-label="OTP Input 3"
      class="ant-input ant-input-rtl ant-input-outlined ant-otp-input css-var-root ant-input-css-var"
      size="1"
      type="text"
      value=""
    />
  </span>
  <span
    class="ant-otp-input-wrapper"
    role="presentation"
  >
    <input
      aria-label="OTP Input 4"
      class="ant-input ant-input-rtl ant-input-outlined ant-otp-input css-var-root ant-input-css-var"
      size="1"
      type="text"
      value=""
    />
  </span>
  <span
    class="ant-otp-input-wrapper"
    role="presentation"
  >
    <input
      aria-label="OTP Input 5"
      class="ant-input ant-input-rtl ant-input-outlined ant-otp-input css-var-root ant-input-css-var"
      size="1"
      type="text"
      value=""
    />
  </span>
  <span
    class="ant-otp-input-wrapper"
    role="presentation"
  >
    <input
      aria-label="OTP Input 6"
      class="ant-input ant-input-rtl ant-input-outlined ant-otp-input css-var-root ant-input-css-var"
      size="1"
      type="text"
      value=""
    />
  </span>
</div>
`;
