// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Input.Search rtl render component should be rendered correctly in RTL direction 1`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-rtl ant-input-group-wrapper-outlined ant-input-search ant-input-search-rtl css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group ant-input-group-rtl"
  >
    <input
      class="ant-input ant-input-rtl ant-input-outlined"
      type="search"
      value=""
    />
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-rtl ant-input-search-button"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
    </span>
  </span>
</span>
`;

exports[`Input.Search should support ReactNode suffix without error 1`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <span
      class="ant-input-affix-wrapper ant-input-outlined"
    >
      <input
        class="ant-input"
        type="search"
        value=""
      />
      <span
        class="ant-input-suffix"
      >
        <div>
          ok
        </div>
      </span>
    </span>
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
    </span>
  </span>
</span>
`;

exports[`Input.Search should support addonAfter 1`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <input
      class="ant-input ant-input-outlined"
      type="search"
      value=""
    />
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <span>
        Addon After
      </span>
    </span>
  </span>
</span>
`;

exports[`Input.Search should support addonAfter 2`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-input-search-with-button css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <input
      class="ant-input ant-input-outlined"
      type="search"
      value=""
    />
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-input-search-button"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
      <span>
        Addon After
      </span>
    </span>
  </span>
</span>
`;

exports[`Input.Search should support addonAfter and suffix for loading 1`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <span
      class="ant-input-affix-wrapper ant-input-outlined"
    >
      <input
        class="ant-input"
        type="search"
        value=""
      />
      <span
        class="ant-input-suffix"
      >
        suffix
      </span>
    </span>
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-loading ant-input-search-button"
        type="button"
      >
        <span
          class="ant-btn-icon ant-btn-loading-icon"
        >
          <span
            aria-label="loading"
            class="anticon anticon-loading anticon-spin"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="loading"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
              />
            </svg>
          </span>
        </span>
      </button>
      addonAfter
    </span>
  </span>
</span>
`;

exports[`Input.Search should support addonAfter and suffix for loading 2`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-input-search-with-button css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <span
      class="ant-input-affix-wrapper ant-input-outlined"
    >
      <input
        class="ant-input"
        type="search"
        value=""
      />
      <span
        class="ant-input-suffix"
      >
        suffix
      </span>
    </span>
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-btn-loading ant-input-search-button"
        type="button"
      >
        <span
          class="ant-btn-icon ant-btn-loading-icon"
        >
          <span
            aria-label="loading"
            class="anticon anticon-loading anticon-spin"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="loading"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
              />
            </svg>
          </span>
        </span>
      </button>
      addonAfter
    </span>
  </span>
</span>
`;

exports[`Input.Search should support custom Button 1`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-input-search-with-button css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <input
      class="ant-input ant-input-outlined"
      type="search"
      value=""
    />
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-input-search-button"
        type="button"
      >
        <span>
          ok
        </span>
      </button>
    </span>
  </span>
</span>
`;

exports[`Input.Search should support custom button 1`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-input-search-with-button css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <input
      class="ant-input ant-input-outlined"
      type="search"
      value=""
    />
    <span
      class="ant-input-group-addon"
    >
      <button
        type="button"
      >
        ok
      </button>
    </span>
  </span>
</span>
`;

exports[`Input.Search should support invalid addonAfter 1`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-input-search-with-button css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <input
      class="ant-input ant-input-outlined"
      type="search"
      value=""
    />
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-input-search-button"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
    </span>
  </span>
</span>
`;

exports[`Input.Search should support invalid suffix 1`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <span
      class="ant-input-affix-wrapper ant-input-outlined"
    >
      <input
        class="ant-input"
        type="search"
        value=""
      />
      <span
        class="ant-input-suffix"
      />
    </span>
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="search"
            class="anticon anticon-search"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="search"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
              />
            </svg>
          </span>
        </span>
      </button>
    </span>
  </span>
</span>
`;

exports[`Input.Search should support loading 1`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <input
      class="ant-input ant-input-outlined"
      type="search"
      value=""
    />
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-loading ant-input-search-button"
        type="button"
      >
        <span
          class="ant-btn-icon ant-btn-loading-icon"
        >
          <span
            aria-label="loading"
            class="anticon anticon-loading anticon-spin"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="loading"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
              />
            </svg>
          </span>
        </span>
      </button>
    </span>
  </span>
</span>
`;

exports[`Input.Search should support loading 2`] = `
<span
  class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-input-search-with-button css-var-root ant-input-css-var"
>
  <span
    class="ant-input-wrapper ant-input-group"
  >
    <input
      class="ant-input ant-input-outlined"
      type="search"
      value=""
    />
    <span
      class="ant-input-group-addon"
    >
      <button
        class="ant-btn css-var-root ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-btn-loading ant-input-search-button"
        type="button"
      >
        <span
          class="ant-btn-icon ant-btn-loading-icon"
        >
          <span
            aria-label="loading"
            class="anticon anticon-loading anticon-spin"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="loading"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
              />
            </svg>
          </span>
        </span>
      </button>
    </span>
  </span>
</span>
`;
