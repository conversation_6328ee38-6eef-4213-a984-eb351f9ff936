// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`QRCode test custom status render 1`] = `
<div>
  <div
    class="ant-qrcode qrcode-expired css-var-root"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <div
      class="ant-qrcode-cover"
    >
      <div
        class="custom-expired"
      >
        <span>
          QR code expired
        </span>
        <button
          id="refresh"
          type="button"
        >
          refresh
        </button>
      </div>
    </div>
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>
  <div
    class="ant-qrcode qrcode-loading css-var-root"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <div
      class="ant-qrcode-cover"
    >
      <div
        class="custom-loading"
      >
        Loading
      </div>
    </div>
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>
  <div
    class="ant-qrcode qrcode-scanned css-var-root"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <div
      class="ant-qrcode-cover"
    >
      <div
        class="custom-scanned"
      >
        Scanned
      </div>
    </div>
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>
</div>
`;

exports[`QRCode test rtl render component should be rendered correctly in RTL direction 1`] = `null`;

exports[`QRCode test should correct render 1`] = `
<div>
  <div
    class="ant-qrcode css-var-root"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>
</div>
`;

exports[`QRCode test should render \`null\` and console Error when value not exist 1`] = `null`;
