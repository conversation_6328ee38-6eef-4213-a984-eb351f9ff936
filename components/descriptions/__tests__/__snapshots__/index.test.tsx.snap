// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Descriptions Descriptions support colon 1`] = `
<div
  class="ant-descriptions css-var-root"
>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label ant-descriptions-item-no-colon"
              >
                Product
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Cloud Database
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`Descriptions Descriptions support style 1`] = `
<div
  class="ant-descriptions css-var-root"
  style="background-color: rgb(232, 232, 232);"
>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-content"
              >
                Cloud Database
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`Descriptions Descriptions.Item support className 1`] = `
<div
  class="ant-descriptions css-var-root"
>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item my-class"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Product
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Cloud Database
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`Descriptions column is number 1`] = `
<div
  class="ant-descriptions css-var-root"
>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Product
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Cloud Database
              </span>
            </div>
          </td>
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Billing
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Prepaid
              </span>
            </div>
          </td>
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                time
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                18:00:00
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="3"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Amount
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                $80.00
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`Descriptions number 0 should render correct 1`] = `
<div
  class="ant-descriptions css-var-root"
>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
                style="color: red;"
              >
                0
              </span>
              <span
                class="ant-descriptions-item-content"
                style="color: red;"
              >
                0
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`Descriptions should items work 1`] = `
<div>
  <div
    class="ant-descriptions css-var-root"
  >
    <div
      class="ant-descriptions-view"
    >
      <table>
        <tbody>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  UserName
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  Zhou Maomao
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Telephone
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  1810000000
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Live
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  Hangzhou, Zhejiang
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
`;

exports[`Descriptions should work with React Fragment 1`] = `
<div
  class="ant-descriptions css-var-root"
>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                bamboo
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                bamboo
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                bamboo
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                bamboo
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                bamboo
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                bamboo
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`Descriptions vertical layout 1`] = `
<div
  class="ant-descriptions css-var-root"
>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Product
              </span>
            </div>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-content"
              >
                Cloud Database
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Billing
              </span>
            </div>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-content"
              >
                Prepaid
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                time
              </span>
            </div>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-content"
              >
                18:00:00
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Amount
              </span>
            </div>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-content"
              >
                $80.00
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`Descriptions when item is rendered conditionally 1`] = `
<div
  class="ant-descriptions css-var-root"
>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Product
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Cloud Database
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Billing
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Prepaid
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                time
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                18:00:00
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Amount
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                $80.00
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;
