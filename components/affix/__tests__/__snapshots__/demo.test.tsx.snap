// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/affix/demo/basic.tsx correctly 1`] = `
Array [
  <div
    class=""
  >
    <div
      class=""
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Affix top
        </span>
      </button>
    </div>
  </div>,
  <br />,
  <div
    class=""
  >
    <div
      class=""
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Affix bottom
        </span>
      </button>
    </div>
  </div>,
]
`;

exports[`renders components/affix/demo/debug.tsx correctly 1`] = `
<div
  style="height:10000px"
>
  <div>
    Top
  </div>
  <div
    class=""
  >
    <div
      class=""
    >
      <div
        style="background:red"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
          type="button"
        >
          <span>
            Affix top
          </span>
        </button>
      </div>
    </div>
  </div>
  <div>
    Bottom
  </div>
</div>
`;

exports[`renders components/affix/demo/on-change.tsx correctly 1`] = `
<div
  class=""
>
  <div
    class=""
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        120px to affix top
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/affix/demo/target.tsx correctly 1`] = `
<div
  style="width:100%;height:100px;overflow:auto;box-shadow:0 0 0 1px #1677ff;scrollbar-width:thin;scrollbar-gutter:stable"
>
  <div
    style="width:100%;height:1000px"
  >
    <div
      class=""
    >
      <div
        class=""
      >
        <button
          class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
          type="button"
        >
          <span>
            Fixed at the top of container
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
`;
