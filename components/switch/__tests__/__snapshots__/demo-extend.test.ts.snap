// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/switch/demo/basic.tsx extend context correctly 1`] = `
<button
  aria-checked="true"
  class="ant-switch css-var-test-id ant-switch-checked"
  role="switch"
  type="button"
>
  <div
    class="ant-switch-handle"
  />
  <span
    class="ant-switch-inner"
  >
    <span
      class="ant-switch-inner-checked"
    />
    <span
      class="ant-switch-inner-unchecked"
    />
  </span>
</button>
`;

exports[`renders components/switch/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/switch/demo/disabled.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      aria-checked="true"
      class="ant-switch css-var-test-id ant-switch-checked ant-switch-disabled"
      disabled=""
      role="switch"
      type="button"
    >
      <div
        class="ant-switch-handle"
      />
      <span
        class="ant-switch-inner"
      >
        <span
          class="ant-switch-inner-checked"
        />
        <span
          class="ant-switch-inner-unchecked"
        />
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Toggle disabled
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/switch/demo/disabled.tsx extend context correctly 2`] = `[]`;

exports[`renders components/switch/demo/loading.tsx extend context correctly 1`] = `
Array [
  <button
    aria-checked="true"
    class="ant-switch ant-switch-loading css-var-test-id ant-switch-checked ant-switch-disabled"
    disabled=""
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    >
      <span
        aria-label="loading"
        class="anticon anticon-loading anticon-spin ant-switch-loading-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="loading"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
          />
        </svg>
      </span>
    </div>
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>,
  <br />,
  <button
    aria-checked="false"
    class="ant-switch ant-switch-small ant-switch-loading css-var-test-id ant-switch-disabled"
    disabled=""
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    >
      <span
        aria-label="loading"
        class="anticon anticon-loading anticon-spin ant-switch-loading-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="loading"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
          />
        </svg>
      </span>
    </div>
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>,
]
`;

exports[`renders components/switch/demo/loading.tsx extend context correctly 2`] = `[]`;

exports[`renders components/switch/demo/size.tsx extend context correctly 1`] = `
Array [
  <button
    aria-checked="true"
    class="ant-switch css-var-test-id ant-switch-checked"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>,
  <br />,
  <button
    aria-checked="true"
    class="ant-switch ant-switch-small css-var-test-id ant-switch-checked"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>,
]
`;

exports[`renders components/switch/demo/size.tsx extend context correctly 2`] = `[]`;

exports[`renders components/switch/demo/text.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <button
      aria-checked="true"
      class="ant-switch css-var-test-id ant-switch-checked"
      role="switch"
      type="button"
    >
      <div
        class="ant-switch-handle"
      />
      <span
        class="ant-switch-inner"
      >
        <span
          class="ant-switch-inner-checked"
        >
          开启
        </span>
        <span
          class="ant-switch-inner-unchecked"
        >
          关闭
        </span>
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      aria-checked="false"
      class="ant-switch css-var-test-id"
      role="switch"
      type="button"
    >
      <div
        class="ant-switch-handle"
      />
      <span
        class="ant-switch-inner"
      >
        <span
          class="ant-switch-inner-checked"
        >
          1
        </span>
        <span
          class="ant-switch-inner-unchecked"
        >
          0
        </span>
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      aria-checked="true"
      class="ant-switch css-var-test-id ant-switch-checked"
      role="switch"
      type="button"
    >
      <div
        class="ant-switch-handle"
      />
      <span
        class="ant-switch-inner"
      >
        <span
          class="ant-switch-inner-checked"
        >
          <span
            aria-label="check"
            class="anticon anticon-check"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="check"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
              />
            </svg>
          </span>
        </span>
        <span
          class="ant-switch-inner-unchecked"
        >
          <span
            aria-label="close"
            class="anticon anticon-close"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
              />
            </svg>
          </span>
        </span>
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/switch/demo/text.tsx extend context correctly 2`] = `[]`;
