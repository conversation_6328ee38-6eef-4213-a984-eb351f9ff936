// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Switch rtl render component should be rendered correctly in RTL direction 1`] = `
<button
  aria-checked="false"
  class="ant-switch ant-switch-rtl css-var-root"
  role="switch"
  type="button"
>
  <div
    class="ant-switch-handle"
  />
  <span
    class="ant-switch-inner"
  >
    <span
      class="ant-switch-inner-checked"
    />
    <span
      class="ant-switch-inner-unchecked"
    />
  </span>
</button>
`;
