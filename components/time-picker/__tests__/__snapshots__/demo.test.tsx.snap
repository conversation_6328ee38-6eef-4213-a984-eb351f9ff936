// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/time-picker/demo/12hours.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="flex-wrap:wrap"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select time"
          size="12"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select time"
          size="11"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select time"
          size="10"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/addon.tsx correctly 1`] = `
<div
  class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select time"
      size="10"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="clock-circle"
        class="anticon anticon-clock-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="clock-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/basic.tsx correctly 1`] = `
<div
  class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select time"
      size="10"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="clock-circle"
        class="anticon anticon-clock-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="clock-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/change-on-scroll.tsx correctly 1`] = `
<div
  class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select time"
      size="10"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="clock-circle"
        class="anticon anticon-clock-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="clock-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/colored-popup.tsx correctly 1`] = `
<div
  class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select time"
      size="10"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="clock-circle"
        class="anticon anticon-clock-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="clock-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/disabled.tsx correctly 1`] = `
<div
  class="ant-picker ant-picker-disabled ant-picker-outlined css-var-test-id ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      disabled=""
      placeholder="Select time"
      size="10"
      value="12:08:23"
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="clock-circle"
        class="anticon anticon-clock-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="clock-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/hide-column.tsx correctly 1`] = `
<div
  class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select time"
      size="10"
      value="12:08"
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="clock-circle"
        class="anticon anticon-clock-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="clock-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
          />
        </svg>
      </span>
    </span>
    <span
      class="ant-picker-clear"
      role="button"
    >
      <span
        aria-label="close-circle"
        class="anticon anticon-close-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close-circle"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/interval-options.tsx correctly 1`] = `
<div
  class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select time"
      size="10"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="clock-circle"
        class="anticon anticon-clock-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="clock-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/need-confirm.tsx correctly 1`] = `
<div
  class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select time"
      size="10"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="clock-circle"
        class="anticon anticon-clock-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="clock-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/range-picker.tsx correctly 1`] = `
<div
  class="ant-picker ant-picker-range ant-picker-outlined css-var-test-id ant-picker-css-var"
>
  <div
    class="ant-picker-input ant-picker-input-start"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      date-range="start"
      placeholder="Start time"
      size="10"
      value="12:08:23"
    />
  </div>
  <div
    class="ant-picker-range-separator"
  >
    <span
      aria-label="to"
      class="ant-picker-separator"
    >
      <span
        aria-label="swap-right"
        class="anticon anticon-swap-right"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="swap-right"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-picker-input ant-picker-input-end"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      date-range="end"
      placeholder="End time"
      size="10"
      value="12:08:23"
    />
  </div>
  <div
    class="ant-picker-active-bar"
    style="position:absolute;width:0"
  />
  <span
    class="ant-picker-suffix"
  >
    <span
      aria-label="clock-circle"
      class="anticon anticon-clock-circle"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="clock-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
        />
        <path
          d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
        />
      </svg>
    </span>
  </span>
  <span
    class="ant-picker-clear"
    role="button"
  >
    <span
      aria-label="close-circle"
      class="anticon anticon-close-circle"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="close-circle"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/time-picker/demo/render-panel.tsx correctly 1`] = `
<div
  style="padding-bottom:0;position:relative;min-width:0"
>
  <div
    class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
    style="margin:0"
  >
    <div
      class="ant-picker-input"
    >
      <input
        aria-invalid="false"
        autocomplete="off"
        placeholder="Select time"
        size="10"
        value=""
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/size.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="flex-wrap:wrap"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-large ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select time"
          size="10"
          value="12:08:23"
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
        <span
          class="ant-picker-clear"
          role="button"
        >
          <span
            aria-label="close-circle"
            class="anticon anticon-close-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close-circle"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select time"
          size="10"
          value="12:08:23"
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
        <span
          class="ant-picker-clear"
          role="button"
        >
          <span
            aria-label="close-circle"
            class="anticon anticon-close-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close-circle"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-small ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select time"
          size="10"
          value="12:08:23"
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
        <span
          class="ant-picker-clear"
          role="button"
        >
          <span
            aria-label="close-circle"
            class="anticon anticon-close-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="close-circle"
              fill="currentColor"
              fill-rule="evenodd"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/status.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-outlined ant-picker-status-error css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select time"
          size="10"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-outlined ant-picker-status-warning css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select time"
          size="10"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-range ant-picker-outlined ant-picker-status-error css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input ant-picker-input-start"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="start"
          placeholder="Start time"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-range-separator"
      >
        <span
          aria-label="to"
          class="ant-picker-separator"
        >
          <span
            aria-label="swap-right"
            class="anticon anticon-swap-right"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="swap-right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-picker-input ant-picker-input-end"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="end"
          placeholder="End time"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-active-bar"
        style="position:absolute;width:0"
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-range ant-picker-outlined ant-picker-status-warning css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input ant-picker-input-start"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="start"
          placeholder="Start time"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-range-separator"
      >
        <span
          aria-label="to"
          class="ant-picker-separator"
        >
          <span
            aria-label="swap-right"
            class="anticon anticon-swap-right"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="swap-right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-picker-input ant-picker-input-end"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="end"
          placeholder="End time"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-active-bar"
        style="position:absolute;width:0"
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/suffix.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical css-var-test-id"
  style="column-gap:12px;row-gap:12px"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select time"
          size="10"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="smile"
            class="anticon anticon-smile"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="smile"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-prefix"
      >
        <span
          aria-label="smile"
          class="anticon anticon-smile"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="smile"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select time"
          size="10"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-picker ant-picker-range ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-prefix"
      >
        <span
          aria-label="smile"
          class="anticon anticon-smile"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="smile"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-picker-input ant-picker-input-start"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="start"
          placeholder="Start time"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-range-separator"
      >
        <span
          aria-label="to"
          class="ant-picker-separator"
        >
          <span
            aria-label="swap-right"
            class="anticon anticon-swap-right"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="swap-right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-picker-input ant-picker-input-end"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="end"
          placeholder="End time"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-active-bar"
        style="position:absolute;width:0"
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/value.tsx correctly 1`] = `
<div
  class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
>
  <div
    class="ant-picker-input"
  >
    <input
      aria-invalid="false"
      autocomplete="off"
      placeholder="Select time"
      size="10"
      value=""
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="clock-circle"
        class="anticon anticon-clock-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="clock-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/time-picker/demo/variant.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  style="gap:12px"
>
  <div
    class="ant-flex css-var-test-id"
    style="gap:8px"
  >
    <div
      class="ant-picker ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Outlined"
          size="10"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
    <div
      class="ant-picker ant-picker-range ant-picker-outlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input ant-picker-input-start"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="start"
          placeholder="Outlined Start"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-range-separator"
      >
        <span
          aria-label="to"
          class="ant-picker-separator"
        >
          <span
            aria-label="swap-right"
            class="anticon anticon-swap-right"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="swap-right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-picker-input ant-picker-input-end"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="end"
          placeholder="Outlined End"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-active-bar"
        style="position:absolute;width:0"
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
    style="gap:8px"
  >
    <div
      class="ant-picker ant-picker-filled css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Filled"
          size="10"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
    <div
      class="ant-picker ant-picker-range ant-picker-filled css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input ant-picker-input-start"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="start"
          placeholder="Filled Start"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-range-separator"
      >
        <span
          aria-label="to"
          class="ant-picker-separator"
        >
          <span
            aria-label="swap-right"
            class="anticon anticon-swap-right"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="swap-right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-picker-input ant-picker-input-end"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="end"
          placeholder="Filled End"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-active-bar"
        style="position:absolute;width:0"
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
    style="gap:8px"
  >
    <div
      class="ant-picker ant-picker-borderless css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Borderless"
          size="10"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
    <div
      class="ant-picker ant-picker-range ant-picker-borderless css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input ant-picker-input-start"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="start"
          placeholder="Borderless Start"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-range-separator"
      >
        <span
          aria-label="to"
          class="ant-picker-separator"
        >
          <span
            aria-label="swap-right"
            class="anticon anticon-swap-right"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="swap-right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-picker-input ant-picker-input-end"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="end"
          placeholder="Borderless End"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-active-bar"
        style="position:absolute;width:0"
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
    style="gap:8px"
  >
    <div
      class="ant-picker ant-picker-underlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Underlined"
          size="10"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="clock-circle"
            class="anticon anticon-clock-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="clock-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
    <div
      class="ant-picker ant-picker-range ant-picker-underlined css-var-test-id ant-picker-css-var"
    >
      <div
        class="ant-picker-input ant-picker-input-start"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="start"
          placeholder="Underlined Start"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-range-separator"
      >
        <span
          aria-label="to"
          class="ant-picker-separator"
        >
          <span
            aria-label="swap-right"
            class="anticon anticon-swap-right"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="swap-right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-picker-input ant-picker-input-end"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="end"
          placeholder="Underlined End"
          size="10"
          value=""
        />
      </div>
      <div
        class="ant-picker-active-bar"
        style="position:absolute;width:0"
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
</div>
`;
