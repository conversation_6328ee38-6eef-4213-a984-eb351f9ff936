// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/checkbox/demo/basic.tsx correctly 1`] = `
<label
  class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
>
  <span
    class="ant-checkbox ant-wave-target"
  >
    <input
      class="ant-checkbox-input"
      type="checkbox"
    />
    <span
      class="ant-checkbox-inner"
    />
  </span>
  <span
    class="ant-checkbox-label"
  >
    Checkbox
  </span>
</label>
`;

exports[`renders components/checkbox/demo/check-all.tsx correctly 1`] = `
Array [
  <label
    class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
  >
    <span
      class="ant-checkbox ant-checkbox-indeterminate ant-wave-target"
    >
      <input
        class="ant-checkbox-input"
        type="checkbox"
      />
      <span
        class="ant-checkbox-inner"
      />
    </span>
    <span
      class="ant-checkbox-label"
    >
      Check all
    </span>
  </label>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-checkbox-group css-var-test-id ant-checkbox-css-var"
  >
    <label
      class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-checkbox-group-item css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target ant-checkbox-checked"
      >
        <input
          checked=""
          class="ant-checkbox-input"
          type="checkbox"
          value="Apple"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-checkbox-wrapper ant-checkbox-group-item css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target"
      >
        <input
          class="ant-checkbox-input"
          type="checkbox"
          value="Pear"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-checkbox-group-item css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target ant-checkbox-checked"
      >
        <input
          checked=""
          class="ant-checkbox-input"
          type="checkbox"
          value="Orange"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Orange
      </span>
    </label>
  </div>,
]
`;

exports[`renders components/checkbox/demo/controller.tsx correctly 1`] = `
Array [
  <p
    style="margin-bottom:20px"
  >
    <label
      class="ant-checkbox-wrapper ant-checkbox-wrapper-checked css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target ant-checkbox-checked"
      >
        <input
          checked=""
          class="ant-checkbox-input"
          type="checkbox"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Checked-Enabled
      </span>
    </label>
  </p>,
  <p>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
      type="button"
    >
      <span>
        Uncheck
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
      style="margin:0 10px"
      type="button"
    >
      <span>
        Disable
      </span>
    </button>
  </p>,
]
`;

exports[`renders components/checkbox/demo/custom-line-width.tsx correctly 1`] = `
Array [
  <label
    class="ant-checkbox-wrapper ant-checkbox-wrapper-checked css-var-test-id ant-checkbox-css-var"
  >
    <span
      class="ant-checkbox ant-wave-target ant-checkbox-checked"
    >
      <input
        checked=""
        class="ant-checkbox-input"
        type="checkbox"
      />
      <span
        class="ant-checkbox-inner"
      />
    </span>
  </label>,
  <label
    class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
  >
    <span
      class="ant-checkbox ant-wave-target"
    >
      <input
        class="ant-checkbox-input"
        type="checkbox"
      />
      <span
        class="ant-checkbox-inner"
      />
    </span>
  </label>,
  <label
    class="ant-checkbox-wrapper ant-checkbox-wrapper-checked css-var-test-id ant-checkbox-css-var"
  >
    <span
      class="ant-checkbox ant-wave-target ant-checkbox-checked"
    >
      <input
        checked=""
        class="ant-checkbox-input"
        type="checkbox"
      />
      <span
        class="ant-checkbox-inner"
      />
    </span>
  </label>,
  <label
    class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
  >
    <span
      class="ant-checkbox ant-wave-target"
    >
      <input
        class="ant-checkbox-input"
        type="checkbox"
      />
      <span
        class="ant-checkbox-inner"
      />
    </span>
  </label>,
]
`;

exports[`renders components/checkbox/demo/debug-disable-popover.tsx correctly 1`] = `
<div
  style="padding:56px"
>
  <label
    class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-checkbox-wrapper-disabled css-var-test-id ant-checkbox-css-var"
  >
    <span
      class="ant-checkbox ant-wave-target ant-checkbox-checked ant-checkbox-disabled"
    >
      <input
        aria-describedby="test-id"
        checked=""
        class="ant-checkbox-input"
        disabled=""
        type="checkbox"
      />
      <span
        class="ant-checkbox-inner"
      />
    </span>
  </label>
</div>
`;

exports[`renders components/checkbox/demo/debug-line.tsx correctly 1`] = `
<div>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    style="border:1px solid red;margin-bottom:16px"
  >
    <div
      class="ant-space-item"
    >
      <label
        class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
            value="light"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
      </label>
    </div>
    <div
      class="ant-space-item"
    >
      <div>
        Bamboo
      </div>
    </div>
    <div
      class="ant-space-item"
    >
      <label
        class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
            value="little"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
        <span
          class="ant-checkbox-label"
        >
          Little
        </span>
      </label>
    </div>
  </div>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    style="border:1px solid red;margin-bottom:16px"
  >
    <div
      class="ant-space-item"
    >
      <label
        class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target"
        >
          <input
            class="ant-radio-input"
            type="radio"
            value="light"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
      </label>
    </div>
    <div
      class="ant-space-item"
    >
      <div>
        Bamboo
      </div>
    </div>
    <div
      class="ant-space-item"
    >
      <label
        class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target"
        >
          <input
            class="ant-radio-input"
            type="radio"
            value="little"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          Little
        </span>
      </label>
    </div>
  </div>
  <div
    style="border:1px solid red;margin-bottom:16px;display:flex;align-items:center"
  >
    <label
      class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target"
      >
        <input
          class="ant-checkbox-input"
          type="checkbox"
          value="light"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
    </label>
    <div>
      Bamboo
    </div>
    <label
      class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target"
      >
        <input
          class="ant-checkbox-input"
          type="checkbox"
          value="little"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Little
      </span>
    </label>
  </div>
  <div
    style="border:1px solid red;margin-bottom:16px;display:flex;align-items:center"
  >
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          type="radio"
          value="light"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
    </label>
    <div>
      Bamboo
    </div>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          type="radio"
          value="little"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        Little
      </span>
    </label>
  </div>
  <div>
    <label
      class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target"
      >
        <input
          class="ant-checkbox-input"
          type="checkbox"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Aligned
      </span>
    </label>
  </div>
  <div>
    <label
      class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target"
      >
        <input
          class="ant-checkbox-input"
          type="checkbox"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        <span
          style="font-size:32px"
        >
          Aligned
        </span>
      </span>
    </label>
  </div>
</div>
`;

exports[`renders components/checkbox/demo/disabled.tsx correctly 1`] = `
Array [
  <label
    class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled css-var-test-id ant-checkbox-css-var"
  >
    <span
      class="ant-checkbox ant-wave-target ant-checkbox-disabled"
    >
      <input
        class="ant-checkbox-input"
        disabled=""
        type="checkbox"
      />
      <span
        class="ant-checkbox-inner"
      />
    </span>
  </label>,
  <br />,
  <label
    class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled css-var-test-id ant-checkbox-css-var"
  >
    <span
      class="ant-checkbox ant-checkbox-indeterminate ant-wave-target ant-checkbox-disabled"
    >
      <input
        class="ant-checkbox-input"
        disabled=""
        type="checkbox"
      />
      <span
        class="ant-checkbox-inner"
      />
    </span>
  </label>,
  <br />,
  <label
    class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled css-var-test-id ant-checkbox-css-var"
  >
    <span
      class="ant-checkbox ant-wave-target ant-checkbox-checked ant-checkbox-disabled"
    >
      <input
        checked=""
        class="ant-checkbox-input"
        disabled=""
        type="checkbox"
      />
      <span
        class="ant-checkbox-inner"
      />
    </span>
  </label>,
]
`;

exports[`renders components/checkbox/demo/group.tsx correctly 1`] = `
Array [
  <div
    class="ant-checkbox-group css-var-test-id ant-checkbox-css-var"
  >
    <label
      class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-checkbox-group-item css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target ant-checkbox-checked"
      >
        <input
          checked=""
          class="ant-checkbox-input"
          type="checkbox"
          value="Apple"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-checkbox-wrapper ant-checkbox-group-item css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target"
      >
        <input
          class="ant-checkbox-input"
          type="checkbox"
          value="Pear"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-checkbox-wrapper ant-checkbox-group-item css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target"
      >
        <input
          class="ant-checkbox-input"
          type="checkbox"
          value="Orange"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Orange
      </span>
    </label>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-checkbox-group css-var-test-id ant-checkbox-css-var"
  >
    <label
      class="ant-checkbox-wrapper ant-checkbox-group-item label-1 css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target"
      >
        <input
          class="ant-checkbox-input"
          type="checkbox"
          value="Apple"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-checkbox-group-item label-2 css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target ant-checkbox-checked"
      >
        <input
          checked=""
          class="ant-checkbox-input"
          type="checkbox"
          value="Pear"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-checkbox-wrapper ant-checkbox-group-item label-3 css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target"
      >
        <input
          class="ant-checkbox-input"
          type="checkbox"
          value="Orange"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Orange
      </span>
    </label>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-checkbox-group css-var-test-id ant-checkbox-css-var"
  >
    <label
      class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-checkbox-wrapper-disabled ant-checkbox-group-item label-1 css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target ant-checkbox-checked ant-checkbox-disabled"
      >
        <input
          checked=""
          class="ant-checkbox-input"
          disabled=""
          type="checkbox"
          value="Apple"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Apple
      </span>
    </label>
    <label
      class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled ant-checkbox-group-item label-2 css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target ant-checkbox-disabled"
      >
        <input
          class="ant-checkbox-input"
          disabled=""
          type="checkbox"
          value="Pear"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Pear
      </span>
    </label>
    <label
      class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled ant-checkbox-group-item label-3 css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target ant-checkbox-disabled"
      >
        <input
          class="ant-checkbox-input"
          disabled=""
          type="checkbox"
          value="Orange"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Orange
      </span>
    </label>
  </div>,
]
`;

exports[`renders components/checkbox/demo/layout.tsx correctly 1`] = `
<div
  class="ant-checkbox-group css-var-test-id ant-checkbox-css-var"
  style="width:100%"
>
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-8 css-var-test-id"
    >
      <label
        class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
            value="A"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
        <span
          class="ant-checkbox-label"
        >
          A
        </span>
      </label>
    </div>
    <div
      class="ant-col ant-col-8 css-var-test-id"
    >
      <label
        class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
            value="B"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
        <span
          class="ant-checkbox-label"
        >
          B
        </span>
      </label>
    </div>
    <div
      class="ant-col ant-col-8 css-var-test-id"
    >
      <label
        class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
            value="C"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
        <span
          class="ant-checkbox-label"
        >
          C
        </span>
      </label>
    </div>
    <div
      class="ant-col ant-col-8 css-var-test-id"
    >
      <label
        class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
            value="D"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
        <span
          class="ant-checkbox-label"
        >
          D
        </span>
      </label>
    </div>
    <div
      class="ant-col ant-col-8 css-var-test-id"
    >
      <label
        class="ant-checkbox-wrapper css-var-test-id ant-checkbox-css-var"
      >
        <span
          class="ant-checkbox ant-wave-target"
        >
          <input
            class="ant-checkbox-input"
            type="checkbox"
            value="E"
          />
          <span
            class="ant-checkbox-inner"
          />
        </span>
        <span
          class="ant-checkbox-label"
        >
          E
        </span>
      </label>
    </div>
  </div>
</div>
`;
