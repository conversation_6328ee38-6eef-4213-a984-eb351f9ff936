// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`List Item Layout horizontal itemLayout List should accept extra node 1`] = `
<div
  class="ant-list ant-list-split css-var-root"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div
      class="ant-spin-container"
    >
      <ul
        class="ant-list-items"
      >
        <li
          class="ant-list-item"
        >
          <div
            class="ant-list-item-meta"
          >
            <div
              class="ant-list-item-meta-content"
            >
              <h4
                class="ant-list-item-meta-title"
              >
                <a
                  href="https://ant.design"
                >
                  ant design
                </a>
              </h4>
              <div
                class="ant-list-item-meta-description"
              >
                Ant Design, a design language for background applications, is refined by Ant UED Team.
              </div>
            </div>
          </div>
          <ul
            class="ant-list-item-action"
          >
            <li>
              <a>
                Action
              </a>
            </li>
          </ul>
          <span>
            extra
          </span>
        </li>
      </ul>
    </div>
  </div>
</div>
`;

exports[`List Item Layout rowKey could be function 1`] = `
<div
  class="ant-list ant-list-split css-var-root"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div
      class="ant-spin-container"
    >
      <ul
        class="ant-list-items"
      >
        <li
          class="ant-list-item"
        >
          ant design
        </li>
        <li
          class="ant-list-item"
        >
          ant design
        </li>
        <li
          class="ant-list-item"
        >
          ant design
        </li>
      </ul>
    </div>
  </div>
</div>
`;

exports[`List Item Layout rowKey could be string 1`] = `
<div
  class="ant-list ant-list-split css-var-root"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div
      class="ant-spin-container"
    >
      <ul
        class="ant-list-items"
      >
        <li
          class="ant-list-item"
        >
          ant design
        </li>
        <li
          class="ant-list-item"
        >
          ant design
        </li>
        <li
          class="ant-list-item"
        >
          ant design
        </li>
      </ul>
    </div>
  </div>
</div>
`;

exports[`List Item Layout should render in RTL direction 1`] = `
<div
  class="ant-list ant-list-split ant-list-rtl css-var-root"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div
      class="ant-spin-container"
    >
      <ul
        class="ant-list-items"
      >
        <li
          class="ant-list-item"
        >
          <div
            class="ant-list-item-meta"
          >
            <div
              class="ant-list-item-meta-content"
            >
              <h4
                class="ant-list-item-meta-title"
              >
                <a
                  href="https://ant.design"
                >
                  ant design
                </a>
              </h4>
              <div
                class="ant-list-item-meta-description"
              >
                Ant Design, a design language for background applications, is refined by Ant UED Team.
              </div>
            </div>
          </div>
          <ul
            class="ant-list-item-action"
          >
            <li>
              <a>
                Action
              </a>
            </li>
          </ul>
          <span>
            extra
          </span>
        </li>
      </ul>
    </div>
  </div>
</div>
`;
