// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/anchor/demo/basic.tsx correctly 1`] = `
<div
  class="ant-row css-var-test-id"
>
  <div
    class="ant-col ant-col-16 css-var-test-id"
  >
    <div
      id="part-1"
      style="height:100vh;background:rgba(255,0,0,0.02)"
    />
    <div
      id="part-2"
      style="height:100vh;background:rgba(0,255,0,0.02)"
    />
    <div
      id="part-3"
      style="height:100vh;background:rgba(0,0,255,0.02)"
    />
  </div>
  <div
    class="ant-col ant-col-8 css-var-test-id"
  >
    <div
      class=""
    >
      <div
        class=""
      >
        <div
          class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
          style="max-height:100vh"
        >
          <div
            class="ant-anchor"
          >
            <span
              class="ant-anchor-ink"
            />
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-1"
                title="Part 1"
              >
                Part 1
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-2"
                title="Part 2"
              >
                Part 2
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-3"
                title="Part 3"
              >
                Part 3
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/component-token.tsx correctly 1`] = `
<div
  class="ant-row css-var-test-id"
>
  <div
    class="ant-col ant-col-16 css-var-test-id"
  >
    <div
      id="part-1"
      style="height:100vh;background:rgba(255,0,0,0.02)"
    />
    <div
      id="part-2"
      style="height:100vh;background:rgba(0,255,0,0.02)"
    />
    <div
      id="part-3"
      style="height:100vh;background:rgba(0,0,255,0.02)"
    />
  </div>
  <div
    class="ant-col ant-col-8 css-var-test-id"
  >
    <div
      class=""
    >
      <div
        class=""
      >
        <div
          class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
          style="max-height:100vh"
        >
          <div
            class="ant-anchor"
          >
            <span
              class="ant-anchor-ink"
            />
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-1"
                title="Part 1"
              >
                Part 1
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-2"
                title="Part 2"
              >
                Part 2
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-3"
                title="Part 3"
              >
                Part 3
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/customizeHighlight.tsx correctly 1`] = `
<div
  class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
  style="max-height:100vh"
>
  <div
    class="ant-anchor ant-anchor-fixed"
  >
    <span
      class="ant-anchor-ink"
    />
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-basic"
        title="Basic demo"
      >
        Basic demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-static"
        title="Static demo"
      >
        Static demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#api"
        title="API"
      >
        API
      </a>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#anchor-props"
          title="Anchor Props"
        >
          Anchor Props
        </a>
      </div>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#link-props"
          title="Link Props"
        >
          Link Props
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/horizontal.tsx correctly 1`] = `
Array [
  <div
    style="padding:20px"
  >
    <div
      class=""
    >
      <div
        class=""
      >
        <div
          class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper ant-anchor-wrapper-horizontal"
          style="max-height:100vh"
        >
          <div
            class="ant-anchor"
          >
            <span
              class="ant-anchor-ink"
            />
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-1"
                title="Part 1"
              >
                Part 1
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-2"
                title="Part 2"
              >
                Part 2
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-3"
                title="Part 3"
              >
                Part 3
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div>
    <div
      id="part-1"
      style="width:100vw;height:100vh;text-align:center;background:rgba(0,255,0,0.02)"
    />
    <div
      id="part-2"
      style="width:100vw;height:100vh;text-align:center;background:rgba(0,0,255,0.02)"
    />
    <div
      id="part-3"
      style="width:100vw;height:100vh;text-align:center;background:#FFFBE9"
    />
  </div>,
]
`;

exports[`renders components/anchor/demo/legacy-anchor.tsx correctly 1`] = `
<div
  class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
  style="max-height:100vh"
>
  <div
    class="ant-anchor ant-anchor-fixed"
  >
    <span
      class="ant-anchor-ink"
    />
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-basic"
        title="Basic demo"
      >
        Basic demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-static"
        title="Static demo"
      >
        Static demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#api"
        title="API"
      >
        API
      </a>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#anchor-props"
          title="Anchor Props"
        >
          Anchor Props
        </a>
      </div>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#link-props"
          title="Link Props"
        >
          Link Props
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/onChange.tsx correctly 1`] = `
<div
  class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
  style="max-height:100vh"
>
  <div
    class="ant-anchor ant-anchor-fixed"
  >
    <span
      class="ant-anchor-ink"
    />
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-basic"
        title="Basic demo"
      >
        Basic demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-static"
        title="Static demo"
      >
        Static demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#api"
        title="API"
      >
        API
      </a>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#anchor-props"
          title="Anchor Props"
        >
          Anchor Props
        </a>
      </div>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#link-props"
          title="Link Props"
        >
          Link Props
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/onClick.tsx correctly 1`] = `
<div
  class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
  style="max-height:100vh"
>
  <div
    class="ant-anchor ant-anchor-fixed"
  >
    <span
      class="ant-anchor-ink"
    />
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-basic"
        title="Basic demo"
      >
        Basic demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-static"
        title="Static demo"
      >
        Static demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#api"
        title="API"
      >
        API
      </a>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#anchor-props"
          title="Anchor Props"
        >
          Anchor Props
        </a>
      </div>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#link-props"
          title="Link Props"
        >
          Link Props
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/replace.tsx correctly 1`] = `
<div
  class="ant-row css-var-test-id"
>
  <div
    class="ant-col ant-col-16 css-var-test-id"
  >
    <div
      id="part-1"
      style="height:100vh;background:rgba(255,0,0,0.02)"
    />
    <div
      id="part-2"
      style="height:100vh;background:rgba(0,255,0,0.02)"
    />
    <div
      id="part-3"
      style="height:100vh;background:rgba(0,0,255,0.02)"
    />
  </div>
  <div
    class="ant-col ant-col-8 css-var-test-id"
  >
    <div
      class=""
    >
      <div
        class=""
      >
        <div
          class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
          style="max-height:100vh"
        >
          <div
            class="ant-anchor"
          >
            <span
              class="ant-anchor-ink"
            />
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-1"
                title="Part 1"
              >
                Part 1
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-2"
                title="Part 2"
              >
                Part 2
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-3"
                title="Part 3"
              >
                Part 3
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/static.tsx correctly 1`] = `
<div
  class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
  style="max-height:100vh"
>
  <div
    class="ant-anchor ant-anchor-fixed"
  >
    <span
      class="ant-anchor-ink"
    />
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-basic"
        title="Basic demo"
      >
        Basic demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-static"
        title="Static demo"
      >
        Static demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#api"
        title="API"
      >
        API
      </a>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#anchor-props"
          title="Anchor Props"
        >
          Anchor Props
        </a>
      </div>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#link-props"
          title="Link Props"
        >
          Link Props
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/targetOffset.tsx correctly 1`] = `
<div>
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-18 css-var-test-id"
    >
      <div
        id="part-1"
        style="height:100vh;background:rgba(255,0,0,0.02);margin-top:30vh"
      >
        Part 1
      </div>
      <div
        id="part-2"
        style="height:100vh;background:rgba(0,255,0,0.02)"
      >
        Part 2
      </div>
      <div
        id="part-3"
        style="height:100vh;background:rgba(0,0,255,0.02)"
      >
        Part 3
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
    >
      <div
        class=""
      >
        <div
          class=""
        >
          <div
            class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
            style="max-height:100vh"
          >
            <div
              class="ant-anchor"
            >
              <span
                class="ant-anchor-ink"
              />
              <div
                class="ant-anchor-link"
              >
                <a
                  class="ant-anchor-link-title"
                  href="#part-1"
                  title="Part 1"
                >
                  Part 1
                </a>
              </div>
              <div
                class="ant-anchor-link"
              >
                <a
                  class="ant-anchor-link-title"
                  href="#part-2"
                  title="Part 2"
                >
                  Part 2
                </a>
              </div>
              <div
                class="ant-anchor-link"
              >
                <a
                  class="ant-anchor-link-title"
                  href="#part-3"
                  title="Part 3"
                >
                  Part 3
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    style="height:30vh;background-color:rgba(0, 0, 0, 0.85);position:fixed;top:0;inset-inline-start:0;width:75%;color:#fff"
  >
    <div>
      Fixed Top Block
    </div>
  </div>
</div>
`;
