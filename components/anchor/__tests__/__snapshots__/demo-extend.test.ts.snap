// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/anchor/demo/basic.tsx extend context correctly 1`] = `
<div
  class="ant-row css-var-test-id"
>
  <div
    class="ant-col ant-col-16 css-var-test-id"
  >
    <div
      id="part-1"
      style="height: 100vh; background: rgba(255, 0, 0, 0.02);"
    />
    <div
      id="part-2"
      style="height: 100vh; background: rgba(0, 255, 0, 0.02);"
    />
    <div
      id="part-3"
      style="height: 100vh; background: rgba(0, 0, 255, 0.02);"
    />
  </div>
  <div
    class="ant-col ant-col-8 css-var-test-id"
  >
    <div
      class=""
    >
      <div
        class=""
      >
        <div
          class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
          style="max-height: 100vh;"
        >
          <div
            class="ant-anchor"
          >
            <span
              class="ant-anchor-ink ant-anchor-ink-visible"
              style="top: 0px; height: 0px;"
            />
            <div
              class="ant-anchor-link ant-anchor-link-active"
            >
              <a
                class="ant-anchor-link-title ant-anchor-link-title-active"
                href="#part-1"
                title="Part 1"
              >
                Part 1
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-2"
                title="Part 2"
              >
                Part 2
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-3"
                title="Part 3"
              >
                Part 3
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/anchor/demo/component-token.tsx extend context correctly 1`] = `
<div
  class="ant-row css-var-test-id"
>
  <div
    class="ant-col ant-col-16 css-var-test-id"
  >
    <div
      id="part-1"
      style="height: 100vh; background: rgba(255, 0, 0, 0.02);"
    />
    <div
      id="part-2"
      style="height: 100vh; background: rgba(0, 255, 0, 0.02);"
    />
    <div
      id="part-3"
      style="height: 100vh; background: rgba(0, 0, 255, 0.02);"
    />
  </div>
  <div
    class="ant-col ant-col-8 css-var-test-id"
  >
    <div
      class=""
    >
      <div
        class=""
      >
        <div
          class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
          style="max-height: 100vh;"
        >
          <div
            class="ant-anchor"
          >
            <span
              class="ant-anchor-ink ant-anchor-ink-visible"
              style="top: 0px; height: 0px;"
            />
            <div
              class="ant-anchor-link ant-anchor-link-active"
            >
              <a
                class="ant-anchor-link-title ant-anchor-link-title-active"
                href="#part-1"
                title="Part 1"
              >
                Part 1
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-2"
                title="Part 2"
              >
                Part 2
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-3"
                title="Part 3"
              >
                Part 3
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/component-token.tsx extend context correctly 2`] = `[]`;

exports[`renders components/anchor/demo/customizeHighlight.tsx extend context correctly 1`] = `
<div
  class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
  style="max-height: 100vh;"
>
  <div
    class="ant-anchor ant-anchor-fixed"
  >
    <span
      class="ant-anchor-ink ant-anchor-ink-visible"
      style="top: 0px; height: 0px;"
    />
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-basic"
        title="Basic demo"
      >
        Basic demo
      </a>
    </div>
    <div
      class="ant-anchor-link ant-anchor-link-active"
    >
      <a
        class="ant-anchor-link-title ant-anchor-link-title-active"
        href="#anchor-demo-static"
        title="Static demo"
      >
        Static demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#api"
        title="API"
      >
        API
      </a>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#anchor-props"
          title="Anchor Props"
        >
          Anchor Props
        </a>
      </div>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#link-props"
          title="Link Props"
        >
          Link Props
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/customizeHighlight.tsx extend context correctly 2`] = `[]`;

exports[`renders components/anchor/demo/horizontal.tsx extend context correctly 1`] = `
Array [
  <div
    style="padding: 20px;"
  >
    <div
      class=""
    >
      <div
        class=""
      >
        <div
          class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper ant-anchor-wrapper-horizontal"
          style="max-height: 100vh;"
        >
          <div
            class="ant-anchor"
          >
            <span
              class="ant-anchor-ink ant-anchor-ink-visible"
              style="left: 0px; width: 0px;"
            />
            <div
              class="ant-anchor-link ant-anchor-link-active"
            >
              <a
                class="ant-anchor-link-title ant-anchor-link-title-active"
                href="#part-1"
                title="Part 1"
              >
                Part 1
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-2"
                title="Part 2"
              >
                Part 2
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-3"
                title="Part 3"
              >
                Part 3
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div>
    <div
      id="part-1"
      style="width: 100vw; height: 100vh; text-align: center; background: rgba(0, 255, 0, 0.02);"
    />
    <div
      id="part-2"
      style="width: 100vw; height: 100vh; text-align: center; background: rgba(0, 0, 255, 0.02);"
    />
    <div
      id="part-3"
      style="width: 100vw; height: 100vh; text-align: center; background: rgb(255, 251, 233);"
    />
  </div>,
]
`;

exports[`renders components/anchor/demo/horizontal.tsx extend context correctly 2`] = `[]`;

exports[`renders components/anchor/demo/legacy-anchor.tsx extend context correctly 1`] = `
<div
  class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
  style="max-height: 100vh;"
>
  <div
    class="ant-anchor ant-anchor-fixed"
  >
    <span
      class="ant-anchor-ink"
    />
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-basic"
        title="Basic demo"
      >
        Basic demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-static"
        title="Static demo"
      >
        Static demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#api"
        title="API"
      >
        API
      </a>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#anchor-props"
          title="Anchor Props"
        >
          Anchor Props
        </a>
      </div>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#link-props"
          title="Link Props"
        >
          Link Props
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/legacy-anchor.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Anchor] \`Anchor children\` is deprecated. Please use \`items\` instead.",
]
`;

exports[`renders components/anchor/demo/onChange.tsx extend context correctly 1`] = `
<div
  class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
  style="max-height: 100vh;"
>
  <div
    class="ant-anchor ant-anchor-fixed"
  >
    <span
      class="ant-anchor-ink"
    />
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-basic"
        title="Basic demo"
      >
        Basic demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-static"
        title="Static demo"
      >
        Static demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#api"
        title="API"
      >
        API
      </a>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#anchor-props"
          title="Anchor Props"
        >
          Anchor Props
        </a>
      </div>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#link-props"
          title="Link Props"
        >
          Link Props
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/onChange.tsx extend context correctly 2`] = `[]`;

exports[`renders components/anchor/demo/onClick.tsx extend context correctly 1`] = `
<div
  class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
  style="max-height: 100vh;"
>
  <div
    class="ant-anchor ant-anchor-fixed"
  >
    <span
      class="ant-anchor-ink"
    />
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-basic"
        title="Basic demo"
      >
        Basic demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-static"
        title="Static demo"
      >
        Static demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#api"
        title="API"
      >
        API
      </a>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#anchor-props"
          title="Anchor Props"
        >
          Anchor Props
        </a>
      </div>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#link-props"
          title="Link Props"
        >
          Link Props
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/onClick.tsx extend context correctly 2`] = `[]`;

exports[`renders components/anchor/demo/replace.tsx extend context correctly 1`] = `
<div
  class="ant-row css-var-test-id"
>
  <div
    class="ant-col ant-col-16 css-var-test-id"
  >
    <div
      id="part-1"
      style="height: 100vh; background: rgba(255, 0, 0, 0.02);"
    />
    <div
      id="part-2"
      style="height: 100vh; background: rgba(0, 255, 0, 0.02);"
    />
    <div
      id="part-3"
      style="height: 100vh; background: rgba(0, 0, 255, 0.02);"
    />
  </div>
  <div
    class="ant-col ant-col-8 css-var-test-id"
  >
    <div
      class=""
    >
      <div
        class=""
      >
        <div
          class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
          style="max-height: 100vh;"
        >
          <div
            class="ant-anchor"
          >
            <span
              class="ant-anchor-ink ant-anchor-ink-visible"
              style="top: 0px; height: 0px;"
            />
            <div
              class="ant-anchor-link ant-anchor-link-active"
            >
              <a
                class="ant-anchor-link-title ant-anchor-link-title-active"
                href="#part-1"
                title="Part 1"
              >
                Part 1
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-2"
                title="Part 2"
              >
                Part 2
              </a>
            </div>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#part-3"
                title="Part 3"
              >
                Part 3
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/replace.tsx extend context correctly 2`] = `[]`;

exports[`renders components/anchor/demo/static.tsx extend context correctly 1`] = `
<div
  class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
  style="max-height: 100vh;"
>
  <div
    class="ant-anchor ant-anchor-fixed"
  >
    <span
      class="ant-anchor-ink"
    />
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-basic"
        title="Basic demo"
      >
        Basic demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#anchor-demo-static"
        title="Static demo"
      >
        Static demo
      </a>
    </div>
    <div
      class="ant-anchor-link"
    >
      <a
        class="ant-anchor-link-title"
        href="#api"
        title="API"
      >
        API
      </a>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#anchor-props"
          title="Anchor Props"
        >
          Anchor Props
        </a>
      </div>
      <div
        class="ant-anchor-link"
      >
        <a
          class="ant-anchor-link-title"
          href="#link-props"
          title="Link Props"
        >
          Link Props
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/static.tsx extend context correctly 2`] = `[]`;

exports[`renders components/anchor/demo/targetOffset.tsx extend context correctly 1`] = `
<div>
  <div
    class="ant-row css-var-test-id"
  >
    <div
      class="ant-col ant-col-18 css-var-test-id"
    >
      <div
        id="part-1"
        style="height: 100vh; background: rgba(255, 0, 0, 0.02); margin-top: 30vh;"
      >
        Part 1
      </div>
      <div
        id="part-2"
        style="height: 100vh; background: rgba(0, 255, 0, 0.02);"
      >
        Part 2
      </div>
      <div
        id="part-3"
        style="height: 100vh; background: rgba(0, 0, 255, 0.02);"
      >
        Part 3
      </div>
    </div>
    <div
      class="ant-col ant-col-6 css-var-test-id"
    >
      <div
        class=""
      >
        <div
          class=""
        >
          <div
            class="css-var-test-id ant-anchor-css-var ant-anchor-wrapper"
            style="max-height: 100vh;"
          >
            <div
              class="ant-anchor"
            >
              <span
                class="ant-anchor-ink ant-anchor-ink-visible"
                style="top: 0px; height: 0px;"
              />
              <div
                class="ant-anchor-link ant-anchor-link-active"
              >
                <a
                  class="ant-anchor-link-title ant-anchor-link-title-active"
                  href="#part-1"
                  title="Part 1"
                >
                  Part 1
                </a>
              </div>
              <div
                class="ant-anchor-link"
              >
                <a
                  class="ant-anchor-link-title"
                  href="#part-2"
                  title="Part 2"
                >
                  Part 2
                </a>
              </div>
              <div
                class="ant-anchor-link"
              >
                <a
                  class="ant-anchor-link-title"
                  href="#part-3"
                  title="Part 3"
                >
                  Part 3
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    style="height: 30vh; background-color: rgba(0, 0, 0, 0.85); position: fixed; top: 0px; inset-inline-start: 0; width: 75%; color: rgb(255, 255, 255);"
  >
    <div>
      Fixed Top Block
    </div>
  </div>
</div>
`;

exports[`renders components/anchor/demo/targetOffset.tsx extend context correctly 2`] = `[]`;
