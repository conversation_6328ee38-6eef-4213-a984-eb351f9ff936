// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Anchor Render render items and ignore jsx children 1`] = `
<div
  class=""
>
  <div
    class=""
  >
    <div
      class="css-var-root ant-anchor-css-var ant-anchor-wrapper"
      style="max-height: 100vh;"
    >
      <div
        class="ant-anchor"
      >
        <span
          class="ant-anchor-ink"
        />
        <div
          class="ant-anchor-link"
        >
          <a
            class="ant-anchor-link-title"
            href="#anchor-demo-basic"
            title="Item Basic Demo"
          >
            Item Basic Demo
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Anchor Render renders items correctly 1`] = `
<div
  class=""
>
  <div
    class=""
  >
    <div
      class="css-var-root ant-anchor-css-var ant-anchor-wrapper"
      style="max-height: 100vh;"
    >
      <div
        class="ant-anchor"
      >
        <span
          class="ant-anchor-ink"
        />
        <div
          class="ant-anchor-link"
        >
          <a
            class="ant-anchor-link-title"
            href="#anchor-demo-basic"
            title="Item Basic Demo"
          >
            Item Basic Demo
          </a>
        </div>
        <div
          class="ant-anchor-link"
        >
          <a
            class="ant-anchor-link-title"
            href="#anchor-demo-static"
            title="Static demo"
          >
            Static demo
          </a>
        </div>
        <div
          class="ant-anchor-link"
        >
          <a
            class="ant-anchor-link-title"
            href="#api"
            title="API"
          >
            API
          </a>
          <div
            class="ant-anchor-link"
          >
            <a
              class="ant-anchor-link-title"
              href="#anchor-props"
              title="Anchor Props"
            >
              Anchor Props
            </a>
            <div
              class="ant-anchor-link"
            >
              <a
                class="ant-anchor-link-title"
                href="#link-props"
                title="Link Props"
              >
                Link Props
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Anchor Render renders items correctly#horizontal 1`] = `
<div
  class=""
>
  <div
    class=""
  >
    <div
      class="css-var-root ant-anchor-css-var ant-anchor-wrapper"
      style="max-height: 100vh;"
    >
      <div
        class="ant-anchor"
      >
        <span
          class="ant-anchor-ink"
        />
        <div
          class="ant-anchor-link"
        >
          <a
            class="ant-anchor-link-title"
            href="#anchor-demo-basic"
            title="Item Basic Demo"
          >
            Item Basic Demo
          </a>
        </div>
        <div
          class="ant-anchor-link"
        >
          <a
            class="ant-anchor-link-title"
            href="#anchor-demo-static"
            title="Static demo"
          >
            Static demo
          </a>
        </div>
        <div
          class="ant-anchor-link"
        >
          <a
            class="ant-anchor-link-title"
            href="#api"
            title="API"
          >
            API
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;
