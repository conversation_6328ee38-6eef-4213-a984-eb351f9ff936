// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`DropdownButton rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-space-compact ant-space-compact-rtl ant-space-compact-block ant-dropdown-button"
>
  <button
    class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-rtl ant-btn-compact-item ant-btn-compact-first-item ant-btn-compact-item-rtl"
    type="button"
  />
  <button
    class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-rtl ant-btn-compact-item ant-btn-compact-last-item ant-btn-compact-item-rtl ant-dropdown-trigger ant-dropdown-rtl"
    type="button"
  >
    <span
      class="ant-btn-icon"
    >
      <span
        aria-label="ellipsis"
        class="anticon anticon-ellipsis"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="ellipsis"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
          />
        </svg>
      </span>
    </span>
  </button>
</div>
`;

exports[`DropdownButton should support href like Button 1`] = `
<div
  class="ant-space-compact ant-space-compact-block ant-dropdown-button"
>
  <a
    aria-disabled="false"
    class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
    href="https://ant.design"
    tabindex="0"
  />
  <button
    class="ant-btn css-var-root ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
    type="button"
  >
    <span
      class="ant-btn-icon"
    >
      <span
        aria-label="ellipsis"
        class="anticon anticon-ellipsis"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="ellipsis"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
          />
        </svg>
      </span>
    </span>
  </button>
</div>
`;
