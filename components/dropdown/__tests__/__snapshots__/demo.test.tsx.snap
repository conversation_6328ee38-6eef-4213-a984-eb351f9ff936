// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/dropdown/demo/arrow.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
      style="flex-wrap:wrap"
    >
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            bottomLeft
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            bottom
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            bottomRight
          </span>
        </button>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
      style="flex-wrap:wrap"
    >
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            topLeft
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            top
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            topRight
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/dropdown/demo/arrow-center.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
      style="flex-wrap:wrap"
    >
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            bottomLeft
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            bottom
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            bottomRight
          </span>
        </button>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
      style="flex-wrap:wrap"
    >
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            topLeft
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            top
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            topRight
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/dropdown/demo/basic.tsx correctly 1`] = `
<a
  class="ant-dropdown-trigger"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      Hover me
    </div>
    <div
      class="ant-space-item"
    >
      <span
        aria-label="down"
        class="anticon anticon-down"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </div>
  </div>
</a>
`;

exports[`renders components/dropdown/demo/context-menu.tsx correctly 1`] = `
<div
  class="ant-dropdown-trigger"
  style="color:rgba(0,0,0,0.45);background:#f5f5f5;height:200px;text-align:center;line-height:200px"
>
  Right Click on here
</div>
`;

exports[`renders components/dropdown/demo/custom-dropdown.tsx correctly 1`] = `
<a
  class="ant-dropdown-trigger"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      Hover me
    </div>
    <div
      class="ant-space-item"
    >
      <span
        aria-label="down"
        class="anticon anticon-down"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </div>
  </div>
</a>
`;

exports[`renders components/dropdown/demo/dropdown-button.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="flex-wrap:wrap"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span>
          Dropdown
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span>
          Dropdown
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="user"
            class="anticon anticon-user"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="user"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
        disabled=""
        type="button"
      >
        <span>
          Dropdown
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        disabled=""
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span>
          With Tooltip
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-loading ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon ant-btn-loading-icon"
        >
          <span
            aria-label="loading"
            class="anticon anticon-loading anticon-spin"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="loading"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-end ant-dropdown-trigger"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="down"
          class="anticon anticon-down"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
      <span>
        Button
      </span>
    </button>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span>
          Danger
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`renders components/dropdown/demo/event.tsx correctly 1`] = `
<a
  class="ant-dropdown-trigger"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      Hover me, Click menu item
    </div>
    <div
      class="ant-space-item"
    >
      <span
        aria-label="down"
        class="anticon anticon-down"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </div>
  </div>
</a>
`;

exports[`renders components/dropdown/demo/extra.tsx correctly 1`] = `
<a
  class="ant-dropdown-trigger"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      Hover me
    </div>
    <div
      class="ant-space-item"
    >
      <span
        aria-label="down"
        class="anticon anticon-down"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </div>
  </div>
</a>
`;

exports[`renders components/dropdown/demo/icon-debug.tsx correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact ant-space-compact-block ant-dropdown-button"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span>
          Submit
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="down"
            class="anticon anticon-down"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact ant-space-compact-block ant-dropdown-button"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span>
          Submit
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="down"
            class="anticon anticon-down"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span>
          Submit
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="down"
            class="anticon anticon-down"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span>
          Submit
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="down"
            class="anticon anticon-down"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`renders components/dropdown/demo/item.tsx correctly 1`] = `
<a
  class="ant-dropdown-trigger"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      Hover me
    </div>
    <div
      class="ant-space-item"
    >
      <span
        aria-label="down"
        class="anticon anticon-down"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </div>
  </div>
</a>
`;

exports[`renders components/dropdown/demo/loading.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-loading ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span
          class="ant-btn-icon ant-btn-loading-icon"
        >
          <span
            aria-label="loading"
            class="anticon anticon-loading anticon-spin"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="loading"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
              />
            </svg>
          </span>
        </span>
        <span>
          Submit
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-loading ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span
          class="ant-btn-icon ant-btn-loading-icon"
        >
          <span
            aria-label="loading"
            class="anticon anticon-loading anticon-spin"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="loading"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
              />
            </svg>
          </span>
        </span>
        <span>
          Submit
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span>
          Submit
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
        type="button"
      >
        <span>
          Submit
        </span>
      </button>
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item ant-dropdown-trigger"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="down"
            class="anticon anticon-down"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`renders components/dropdown/demo/menu-full.tsx correctly 1`] = `
<a
  class="ant-dropdown-trigger"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      Hover to check menu style
    </div>
    <div
      class="ant-space-item"
    >
      <span
        aria-label="down"
        class="anticon anticon-down"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </div>
  </div>
</a>
`;

exports[`renders components/dropdown/demo/overlay-open.tsx correctly 1`] = `
<a
  class="ant-dropdown-trigger"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      Hover me
    </div>
    <div
      class="ant-space-item"
    >
      <span
        aria-label="down"
        class="anticon anticon-down"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </div>
  </div>
</a>
`;

exports[`renders components/dropdown/demo/placement.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
      style="flex-wrap:wrap"
    >
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            bottomLeft
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            bottom
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            bottomRight
          </span>
        </button>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
      style="flex-wrap:wrap"
    >
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            topLeft
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            top
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-dropdown-trigger"
          type="button"
        >
          <span>
            topRight
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/dropdown/demo/render-panel.tsx correctly 1`] = `
<div
  style="padding-bottom:0;position:relative;min-width:0"
>
  <span
    class="ant-dropdown-trigger"
  />
</div>
`;

exports[`renders components/dropdown/demo/selectable.tsx correctly 1`] = `
<a
  class="ant-typography ant-dropdown-trigger css-var-test-id"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      Selectable
    </div>
    <div
      class="ant-space-item"
    >
      <span
        aria-label="down"
        class="anticon anticon-down"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </div>
  </div>
</a>
`;

exports[`renders components/dropdown/demo/sub-menu.tsx correctly 1`] = `
<a
  class="ant-dropdown-trigger"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      Cascading menu
    </div>
    <div
      class="ant-space-item"
    >
      <span
        aria-label="down"
        class="anticon anticon-down"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </div>
  </div>
</a>
`;

exports[`renders components/dropdown/demo/trigger.tsx correctly 1`] = `
<a
  class="ant-dropdown-trigger"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      Click me
    </div>
    <div
      class="ant-space-item"
    >
      <span
        aria-label="down"
        class="anticon anticon-down"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </div>
  </div>
</a>
`;
