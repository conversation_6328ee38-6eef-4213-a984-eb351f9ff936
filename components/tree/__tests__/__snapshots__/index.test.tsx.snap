// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Tree icon and switcherIcon of Tree with showLine should render correctly 1`] = `
<div
  class="ant-tree css-var-root ant-tree-show-line"
>
  <div>
    <input
      aria-label="for screen reader"
      style="width: 0px; height: 0px; display: flex; overflow: hidden; opacity: 0; border: 0px; padding: 0px; margin: 0px;"
      tabindex="0"
      value=""
    />
  </div>
  <div
    aria-hidden="true"
    class="ant-tree-treenode"
    style="position: absolute; pointer-events: none; visibility: hidden; height: 0px; overflow: hidden; border: 0px; padding: 0px;"
  >
    <div
      class="ant-tree-indent"
    >
      <div
        class="ant-tree-indent-unit"
      />
    </div>
  </div>
  <div
    class="ant-tree-list"
    role="tree"
    style="position: relative;"
  >
    <div
      class="ant-tree-list-holder"
    >
      <div>
        <div
          class="ant-tree-list-holder-inner"
          style="display: flex; flex-direction: column;"
        >
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            />
            <span
              class="ant-tree-switcher ant-tree-switcher_close"
            >
              switcherIcon
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-close"
              title="---"
            >
              <span
                class="ant-tree-iconEle ant-tree-icon__customize"
              >
                icon
              </span>
              <span
                class="ant-tree-title"
              >
                ---
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            />
            <span
              class="ant-tree-switcher ant-tree-switcher_close"
            >
              switcherIcon
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-close"
              title="---"
            >
              <span
                class="ant-tree-iconEle ant-tree-icon__close"
              />
              <span
                class="ant-tree-title"
              >
                ---
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close ant-tree-treenode-leaf-last"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            />
            <span
              class="ant-tree-switcher ant-tree-switcher_close"
            >
              <span
                aria-label="plus-square"
                class="anticon anticon-plus-square ant-tree-switcher-line-icon"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="plus-square"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
                  />
                  <path
                    d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"
                  />
                </svg>
              </span>
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-close"
              title="---"
            >
              <span
                class="ant-tree-iconEle ant-tree-icon__close"
              />
              <span
                class="ant-tree-title"
              >
                ---
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Tree showLine is object type should render correctly 1`] = `
<div
  class="ant-tree ant-tree-icon-hide css-var-root ant-tree-show-line"
>
  <div>
    <input
      aria-label="for screen reader"
      style="width: 0px; height: 0px; display: flex; overflow: hidden; opacity: 0; border: 0px; padding: 0px; margin: 0px;"
      tabindex="0"
      value=""
    />
  </div>
  <div
    aria-hidden="true"
    class="ant-tree-treenode"
    style="position: absolute; pointer-events: none; visibility: hidden; height: 0px; overflow: hidden; border: 0px; padding: 0px;"
  >
    <div
      class="ant-tree-indent"
    >
      <div
        class="ant-tree-indent-unit"
      />
    </div>
  </div>
  <div
    class="ant-tree-list"
    role="tree"
    style="position: relative;"
  >
    <div
      class="ant-tree-list-holder"
    >
      <div>
        <div
          class="ant-tree-list-holder-inner"
          style="display: flex; flex-direction: column;"
        >
          <div
            aria-expanded="true"
            class="ant-tree-treenode ant-tree-treenode-switcher-open ant-tree-treenode-leaf-last"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            />
            <span
              class="ant-tree-switcher ant-tree-switcher_open"
            >
              <span
                aria-label="minus-square"
                class="anticon anticon-minus-square ant-tree-switcher-line-icon"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="minus-square"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
                  />
                  <path
                    d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"
                  />
                </svg>
              </span>
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-open"
              title="parent 1"
            >
              <span
                class="ant-tree-title"
              >
                parent 1
              </span>
            </span>
          </div>
          <div
            aria-expanded="true"
            class="ant-tree-treenode ant-tree-treenode-switcher-open"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher_open"
            >
              <span
                aria-label="minus-square"
                class="anticon anticon-minus-square ant-tree-switcher-line-icon"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="minus-square"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
                  />
                  <path
                    d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"
                  />
                </svg>
              </span>
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-open"
              title="parent 1-0"
            >
              <span
                class="ant-tree-title"
              >
                parent 1-0
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close ant-tree-treenode-leaf"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher-noop"
            >
              <span
                class="ant-tree-switcher-leaf-line"
              />
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-normal"
              title="leaf"
            >
              <span
                class="ant-tree-title"
              >
                leaf
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close ant-tree-treenode-leaf"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher-noop"
            >
              <span
                class="ant-tree-switcher-leaf-line"
              />
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-normal"
              title="leaf"
            >
              <span
                class="ant-tree-title"
              >
                leaf
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close ant-tree-treenode-leaf-last ant-tree-treenode-leaf"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher-noop"
            >
              <span
                class="ant-tree-switcher-leaf-line"
              />
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-normal"
              title="leaf"
            >
              <span
                class="ant-tree-title"
              >
                leaf
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher_close"
            >
              <span
                aria-label="plus-square"
                class="anticon anticon-plus-square ant-tree-switcher-line-icon"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="plus-square"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
                  />
                  <path
                    d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"
                  />
                </svg>
              </span>
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-close"
              title="parent 1-1"
            >
              <span
                class="ant-tree-title"
              >
                parent 1-1
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close ant-tree-treenode-leaf-last"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher_close"
            >
              <span
                aria-label="plus-square"
                class="anticon anticon-plus-square ant-tree-switcher-line-icon"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="plus-square"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
                  />
                  <path
                    d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"
                  />
                </svg>
              </span>
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-close"
              title="parent 1-2"
            >
              <span
                class="ant-tree-title"
              >
                parent 1-2
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Tree support switcherLoadingIcon prop when loadData 1`] = `
<div
  class="ant-tree ant-tree-icon-hide css-var-root"
>
  <div>
    <input
      aria-label="for screen reader"
      style="width: 0px; height: 0px; display: flex; overflow: hidden; opacity: 0; border: 0px; padding: 0px; margin: 0px;"
      tabindex="0"
      value=""
    />
  </div>
  <div
    aria-hidden="true"
    class="ant-tree-treenode"
    style="position: absolute; pointer-events: none; visibility: hidden; height: 0px; overflow: hidden; border: 0px; padding: 0px;"
  >
    <div
      class="ant-tree-indent"
    >
      <div
        class="ant-tree-indent-unit"
      />
    </div>
  </div>
  <div
    class="ant-tree-list"
    role="tree"
    style="position: relative;"
  >
    <div
      class="ant-tree-list-holder"
    >
      <div>
        <div
          class="ant-tree-list-holder-inner"
          style="display: flex; flex-direction: column;"
        >
          <div
            aria-expanded="true"
            class="ant-tree-treenode ant-tree-treenode-switcher-open ant-tree-treenode-leaf-last"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            />
            <span
              class="ant-tree-switcher ant-tree-switcher_open"
            >
              switcherIcon
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-open"
              title="---"
            >
              <span
                class="ant-tree-title"
              >
                ---
              </span>
            </span>
          </div>
          <div
            aria-expanded="true"
            class="ant-tree-treenode ant-tree-treenode-switcher-open ant-tree-treenode-loading"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher_open"
            >
              <div>
                loading...
              </div>
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-open"
              title="node1"
            >
              <span
                class="ant-tree-iconEle ant-tree-icon__open ant-tree-icon_loading"
              />
              <span
                class="ant-tree-title"
              >
                node1
              </span>
            </span>
          </div>
          <div
            aria-expanded="true"
            class="ant-tree-treenode ant-tree-treenode-switcher-open ant-tree-treenode-loading ant-tree-treenode-leaf-last"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher_open"
            >
              <div>
                loading...
              </div>
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-open"
              title="node2"
            >
              <span
                class="ant-tree-iconEle ant-tree-icon__open ant-tree-icon_loading"
              />
              <span
                class="ant-tree-title"
              >
                node2
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Tree switcherIcon in Tree could be string 1`] = `
<div
  class="ant-tree ant-tree-icon-hide css-var-root"
>
  <div>
    <input
      aria-label="for screen reader"
      style="width: 0px; height: 0px; display: flex; overflow: hidden; opacity: 0; border: 0px; padding: 0px; margin: 0px;"
      tabindex="0"
      value=""
    />
  </div>
  <div
    aria-hidden="true"
    class="ant-tree-treenode"
    style="position: absolute; pointer-events: none; visibility: hidden; height: 0px; overflow: hidden; border: 0px; padding: 0px;"
  >
    <div
      class="ant-tree-indent"
    >
      <div
        class="ant-tree-indent-unit"
      />
    </div>
  </div>
  <div
    class="ant-tree-list"
    role="tree"
    style="position: relative;"
  >
    <div
      class="ant-tree-list-holder"
    >
      <div>
        <div
          class="ant-tree-list-holder-inner"
          style="display: flex; flex-direction: column;"
        >
          <div
            aria-expanded="true"
            class="ant-tree-treenode ant-tree-treenode-switcher-open ant-tree-treenode-leaf-last"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            />
            <span
              class="ant-tree-switcher ant-tree-switcher_open"
            >
              switcherIcon
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-open"
              title="---"
            >
              <span
                class="ant-tree-title"
              >
                ---
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close ant-tree-treenode-leaf"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher-noop"
            />
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-normal"
              title="node1"
            >
              <span
                class="ant-tree-title"
              >
                node1
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close ant-tree-treenode-leaf-last ant-tree-treenode-leaf"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher-noop"
            />
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-normal"
              title="node2"
            >
              <span
                class="ant-tree-title"
              >
                node2
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Tree switcherIcon should be loading icon when loadData 1`] = `
<div
  class="ant-tree ant-tree-icon-hide css-var-root"
>
  <div>
    <input
      aria-label="for screen reader"
      style="width: 0px; height: 0px; display: flex; overflow: hidden; opacity: 0; border: 0px; padding: 0px; margin: 0px;"
      tabindex="0"
      value=""
    />
  </div>
  <div
    aria-hidden="true"
    class="ant-tree-treenode"
    style="position: absolute; pointer-events: none; visibility: hidden; height: 0px; overflow: hidden; border: 0px; padding: 0px;"
  >
    <div
      class="ant-tree-indent"
    >
      <div
        class="ant-tree-indent-unit"
      />
    </div>
  </div>
  <div
    class="ant-tree-list"
    role="tree"
    style="position: relative;"
  >
    <div
      class="ant-tree-list-holder"
    >
      <div>
        <div
          class="ant-tree-list-holder-inner"
          style="display: flex; flex-direction: column;"
        >
          <div
            aria-expanded="true"
            class="ant-tree-treenode ant-tree-treenode-switcher-open ant-tree-treenode-leaf-last"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            />
            <span
              class="ant-tree-switcher ant-tree-switcher_open"
            >
              switcherIcon
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-open"
              title="---"
            >
              <span
                class="ant-tree-title"
              >
                ---
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher_close"
            >
              switcherIcon
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-close"
              title="node1"
            >
              <span
                class="ant-tree-title"
              >
                node1
              </span>
            </span>
          </div>
          <div
            aria-expanded="false"
            class="ant-tree-treenode ant-tree-treenode-switcher-close ant-tree-treenode-leaf-last"
            draggable="false"
            role="treeitem"
          >
            <span
              aria-hidden="true"
              class="ant-tree-indent"
            >
              <span
                class="ant-tree-indent-unit ant-tree-indent-unit-start ant-tree-indent-unit-end"
              />
            </span>
            <span
              class="ant-tree-switcher ant-tree-switcher_close"
            >
              switcherIcon
            </span>
            <span
              class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-close"
              title="node2"
            >
              <span
                class="ant-tree-title"
              >
                node2
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
