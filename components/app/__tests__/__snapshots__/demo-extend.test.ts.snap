// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/app/demo/basic.tsx extend context correctly 1`] = `
<div
  class="ant-app css-var-test-id"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    style="flex-wrap: wrap;"
  >
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Open message
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Open modal
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Open notification
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`renders components/app/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/app/demo/config.tsx extend context correctly 1`] = `
<div
  class="ant-app css-var-test-id"
>
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    style="flex-wrap: wrap;"
  >
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Message for only one
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Notification for bottomLeft
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`renders components/app/demo/config.tsx extend context correctly 2`] = `[]`;
