// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/breadcrumb/demo/basic.tsx correctly 1`] = `
<nav
  class="ant-breadcrumb css-var-test-id"
>
  <ol>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        Home
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        <a
          href=""
        >
          Application Center
        </a>
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        <a
          href=""
        >
          Application List
        </a>
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        An Application
      </span>
    </li>
  </ol>
</nav>
`;

exports[`renders components/breadcrumb/demo/component-token.tsx correctly 1`] = `
<nav
  class="ant-breadcrumb css-var-test-id"
>
  <ol>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        Home
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      &gt;
    </li>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        <a
          href=""
        >
          Application Center
        </a>
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      &gt;
    </li>
    <li
      class=""
    >
      <span
        class="ant-dropdown-trigger ant-breadcrumb-overlay-link"
      >
        <span
          class="ant-breadcrumb-link"
        >
          <a
            href=""
          >
            General
          </a>
        </span>
        <span
          aria-label="down"
          class="anticon anticon-down"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      &gt;
    </li>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href=""
      >
        Application Center
      </a>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      &gt;
    </li>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href=""
      >
        <span
          aria-label="home"
          class="anticon anticon-home"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="home"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"
            />
          </svg>
        </span>
      </a>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      &gt;
    </li>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href=""
      >
        <span
          aria-label="user"
          class="anticon anticon-user"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="user"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
            />
          </svg>
        </span>
        <span>
          Application List
        </span>
      </a>
    </li>
  </ol>
</nav>
`;

exports[`renders components/breadcrumb/demo/debug-routes.tsx correctly 1`] = `
<nav
  class="ant-breadcrumb css-var-test-id"
>
  <ol>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href="#/home"
      >
        Home
      </a>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <span
        class="ant-dropdown-trigger ant-breadcrumb-overlay-link"
      >
        <a
          class="ant-breadcrumb-link"
          href="#/home/<USER>"
        >
          User
        </a>
        <span
          aria-label="down"
          class="anticon anticon-down"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </li>
  </ol>
</nav>
`;

exports[`renders components/breadcrumb/demo/overlay.tsx correctly 1`] = `
<nav
  class="ant-breadcrumb css-var-test-id"
>
  <ol>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        Ant Design
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        <a
          href=""
        >
          Component
        </a>
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <span
        class="ant-dropdown-trigger ant-breadcrumb-overlay-link"
      >
        <span
          class="ant-breadcrumb-link"
        >
          <a
            href=""
          >
            General
          </a>
        </span>
        <span
          aria-label="down"
          class="anticon anticon-down"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        Button
      </span>
    </li>
  </ol>
</nav>
`;

exports[`renders components/breadcrumb/demo/separator.tsx correctly 1`] = `
<nav
  class="ant-breadcrumb css-var-test-id"
>
  <ol>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        Home
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      &gt;
    </li>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href=""
      >
        Application Center
      </a>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      &gt;
    </li>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href=""
      >
        Application List
      </a>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      &gt;
    </li>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        An Application
      </span>
    </li>
  </ol>
</nav>
`;

exports[`renders components/breadcrumb/demo/separator-component.tsx correctly 1`] = `
<nav
  class="ant-breadcrumb css-var-test-id"
>
  <ol>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        Location
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      :
    </li>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href=""
      >
        Application Center
      </a>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href=""
      >
        Application List
      </a>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        An Application
      </span>
    </li>
  </ol>
</nav>
`;

exports[`renders components/breadcrumb/demo/withIcon.tsx correctly 1`] = `
<nav
  class="ant-breadcrumb css-var-test-id"
>
  <ol>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href=""
      >
        <span
          aria-label="home"
          class="anticon anticon-home"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="home"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"
            />
          </svg>
        </span>
      </a>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href=""
      >
        <span
          aria-label="user"
          class="anticon anticon-user"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="user"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
            />
          </svg>
        </span>
        <span>
          Application List
        </span>
      </a>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        Application
      </span>
    </li>
  </ol>
</nav>
`;

exports[`renders components/breadcrumb/demo/withParams.tsx correctly 1`] = `
<nav
  class="ant-breadcrumb css-var-test-id"
>
  <ol>
    <li
      class=""
    >
      <span
        class="ant-breadcrumb-link"
      >
        Users
      </span>
    </li>
    <li
      aria-hidden="true"
      class="ant-breadcrumb-separator"
    >
      /
    </li>
    <li
      class=""
    >
      <a
        class="ant-breadcrumb-link"
        href=""
      >
        1
      </a>
    </li>
  </ol>
</nav>
`;
