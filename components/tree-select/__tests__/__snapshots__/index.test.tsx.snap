// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`TreeSelect TreeSelect Custom Icons should support customized icons 1`] = `
<div
  class="ant-select ant-tree-select ant-select-outlined css-var-root ant-select-css-var ant-tree-select-css-var ant-select-multiple ant-select-allow-clear ant-select-show-arrow ant-select-show-search"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <div
        class="ant-select-selection-overflow"
      >
        <div
          class="ant-select-selection-overflow-item"
          style="opacity: 1;"
        >
          <span
            class="ant-select-selection-item"
            title="my leaf"
          >
            <span
              class="ant-select-selection-item-content"
            >
              my leaf
            </span>
            <span
              aria-hidden="true"
              class="ant-select-selection-item-remove"
              style="user-select: none;"
              unselectable="on"
            >
              <span>
                remove
              </span>
            </span>
          </span>
        </div>
        <div
          class="ant-select-selection-overflow-item"
          style="opacity: 1;"
        >
          <span
            class="ant-select-selection-item"
            title="your leaf"
          >
            <span
              class="ant-select-selection-item-content"
            >
              your leaf
            </span>
            <span
              aria-hidden="true"
              class="ant-select-selection-item-remove"
              style="user-select: none;"
              unselectable="on"
            >
              <span>
                remove
              </span>
            </span>
          </span>
        </div>
        <div
          class="ant-select-selection-overflow-item ant-select-selection-overflow-item-suffix"
          style="opacity: 1;"
        >
          <div
            class="ant-select-selection-search"
            style="width: 0px;"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              readonly=""
              role="combobox"
              style="opacity: 0;"
              type="search"
              unselectable="on"
              value=""
            />
            <span
              aria-hidden="true"
              class="ant-select-selection-search-mirror"
            >
            </span>
          </div>
        </div>
      </div>
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select: none;"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
  <span
    aria-hidden="true"
    class="ant-select-clear"
    style="user-select: none;"
    unselectable="on"
  >
    <span>
      clear
    </span>
  </span>
</div>
`;

exports[`TreeSelect rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-select ant-tree-select ant-select-rtl ant-select-outlined css-var-root ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          readonly=""
          role="combobox"
          style="opacity: 0;"
          type="search"
          unselectable="on"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
  <span
    aria-hidden="true"
    class="ant-select-arrow"
    style="user-select: none;"
    unselectable="on"
  >
    <span
      aria-label="down"
      class="anticon anticon-down ant-select-suffix"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="down"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        />
      </svg>
    </span>
  </span>
</div>
`;
