// Jest <PERSON>napshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Skeleton avatar element active 1`] = `
<div
  class="ant-skeleton ant-skeleton-element ant-skeleton-active css-var-root"
>
  <span
    class="ant-skeleton-avatar ant-skeleton-avatar-circle"
  />
</div>
`;

exports[`Skeleton avatar element shape 1`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-avatar ant-skeleton-avatar-circle"
  />
</div>
`;

exports[`Skeleton avatar element shape 2`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-avatar ant-skeleton-avatar-square"
  />
</div>
`;

exports[`Skeleton avatar element size 1`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-avatar ant-skeleton-avatar-sm ant-skeleton-avatar-circle"
  />
</div>
`;

exports[`Skeleton avatar element size 2`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-avatar ant-skeleton-avatar-circle"
  />
</div>
`;

exports[`Skeleton avatar element size 3`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-avatar ant-skeleton-avatar-lg ant-skeleton-avatar-circle"
  />
</div>
`;

exports[`Skeleton avatar element size 4`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-avatar ant-skeleton-avatar-circle"
    style="width: 20px; height: 20px; line-height: 20px;"
  />
</div>
`;

exports[`Skeleton avatar shape 1`] = `
<div
  class="ant-skeleton ant-skeleton-with-avatar css-var-root"
>
  <div
    class="ant-skeleton-header"
  >
    <span
      class="ant-skeleton-avatar ant-skeleton-avatar-lg ant-skeleton-avatar-circle"
    />
  </div>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 50%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
    </ul>
  </div>
</div>
`;

exports[`Skeleton avatar shape 2`] = `
<div
  class="ant-skeleton ant-skeleton-with-avatar css-var-root"
>
  <div
    class="ant-skeleton-header"
  >
    <span
      class="ant-skeleton-avatar ant-skeleton-avatar-lg ant-skeleton-avatar-square"
    />
  </div>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 50%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
    </ul>
  </div>
</div>
`;

exports[`Skeleton avatar size 1`] = `
<div
  class="ant-skeleton ant-skeleton-with-avatar css-var-root"
>
  <div
    class="ant-skeleton-header"
  >
    <span
      class="ant-skeleton-avatar ant-skeleton-avatar-sm ant-skeleton-avatar-circle"
    />
  </div>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 50%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
    </ul>
  </div>
</div>
`;

exports[`Skeleton avatar size 2`] = `
<div
  class="ant-skeleton ant-skeleton-with-avatar css-var-root"
>
  <div
    class="ant-skeleton-header"
  >
    <span
      class="ant-skeleton-avatar ant-skeleton-avatar-circle"
    />
  </div>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 50%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
    </ul>
  </div>
</div>
`;

exports[`Skeleton avatar size 3`] = `
<div
  class="ant-skeleton ant-skeleton-with-avatar css-var-root"
>
  <div
    class="ant-skeleton-header"
  >
    <span
      class="ant-skeleton-avatar ant-skeleton-avatar-lg ant-skeleton-avatar-circle"
    />
  </div>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 50%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
    </ul>
  </div>
</div>
`;

exports[`Skeleton avatar size 4`] = `
<div
  class="ant-skeleton ant-skeleton-with-avatar css-var-root"
>
  <div
    class="ant-skeleton-header"
  >
    <span
      class="ant-skeleton-avatar ant-skeleton-avatar-circle"
      style="width: 20px; height: 20px; line-height: 20px;"
    />
  </div>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 50%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
    </ul>
  </div>
</div>
`;

exports[`Skeleton button element active 1`] = `
<div
  class="ant-skeleton ant-skeleton-element ant-skeleton-active css-var-root"
>
  <span
    class="ant-skeleton-button"
  />
</div>
`;

exports[`Skeleton button element block 1`] = `
<div
  class="ant-skeleton ant-skeleton-element ant-skeleton-block css-var-root"
>
  <span
    class="ant-skeleton-button"
  />
</div>
`;

exports[`Skeleton button element shape 1`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-button"
  />
</div>
`;

exports[`Skeleton button element shape 2`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-button ant-skeleton-button-round"
  />
</div>
`;

exports[`Skeleton button element shape 3`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-button ant-skeleton-button-circle"
  />
</div>
`;

exports[`Skeleton button element size 1`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-button"
  />
</div>
`;

exports[`Skeleton button element size 2`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-button ant-skeleton-button-lg"
  />
</div>
`;

exports[`Skeleton button element size 3`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-button ant-skeleton-button-sm"
  />
</div>
`;

exports[`Skeleton custom node element should render normal 1`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <div
    class="ant-skeleton-node"
  />
</div>
`;

exports[`Skeleton custom node element should render normal 2`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <div
    class="ant-skeleton-node"
  >
    <span>
      Custom Content Node
    </span>
  </div>
</div>
`;

exports[`Skeleton image element should render normal 1`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <div
    class="ant-skeleton-image"
  >
    <svg
      class="ant-skeleton-image-svg"
      viewBox="0 0 1098 1024"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>
        Image placeholder
      </title>
      <path
        class="ant-skeleton-image-path"
        d="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z"
      />
    </svg>
  </div>
</div>
`;

exports[`Skeleton input element active 1`] = `
<div
  class="ant-skeleton ant-skeleton-element ant-skeleton-active css-var-root"
>
  <span
    class="ant-skeleton-input"
  />
</div>
`;

exports[`Skeleton input element size 1`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-input ant-skeleton-input-sm"
  />
</div>
`;

exports[`Skeleton input element size 2`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-input"
  />
</div>
`;

exports[`Skeleton input element size 3`] = `
<div
  class="ant-skeleton ant-skeleton-element css-var-root"
>
  <span
    class="ant-skeleton-input ant-skeleton-input-lg"
  />
</div>
`;

exports[`Skeleton paragraph rows 1`] = `
<div
  class="ant-skeleton css-var-root"
>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 38%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
      <li />
      <li />
      <li
        style="width: 61%;"
      />
    </ul>
  </div>
</div>
`;

exports[`Skeleton paragraph width 1`] = `
<div
  class="ant-skeleton css-var-root"
>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 38%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
      <li
        style="width: 93%;"
      />
    </ul>
  </div>
</div>
`;

exports[`Skeleton paragraph width 2`] = `
<div
  class="ant-skeleton css-var-root"
>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 38%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li
        style="width: 28%;"
      />
      <li
        style="width: 93%;"
      />
      <li />
    </ul>
  </div>
</div>
`;

exports[`Skeleton rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-skeleton ant-skeleton-rtl css-var-root"
>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 38%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
      <li
        style="width: 61%;"
      />
    </ul>
  </div>
</div>
`;

exports[`Skeleton should display without children and falsy loading props 1`] = `null`;

exports[`Skeleton should round title and paragraph 1`] = `
<div
  class="ant-skeleton ant-skeleton-round css-var-root"
>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 38%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
      <li
        style="width: 61%;"
      />
    </ul>
  </div>
</div>
`;

exports[`Skeleton should square avatar 1`] = `
<div
  class="ant-skeleton ant-skeleton-with-avatar css-var-root"
>
  <div
    class="ant-skeleton-header"
  >
    <span
      class="ant-skeleton-avatar ant-skeleton-avatar-lg ant-skeleton-avatar-square"
    />
  </div>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
    />
  </div>
</div>
`;

exports[`Skeleton should support style 1`] = `
<div
  class="ant-skeleton css-var-root"
  style="background: blue;"
>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 38%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
      <li
        style="width: 61%;"
      />
    </ul>
  </div>
</div>
`;

exports[`Skeleton should without avatar and paragraph 1`] = `
<div
  class="ant-skeleton css-var-root"
>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
    />
  </div>
</div>
`;

exports[`Skeleton title width 1`] = `
<div
  class="ant-skeleton css-var-root"
>
  <div
    class="ant-skeleton-section"
  >
    <h3
      class="ant-skeleton-title"
      style="width: 93%;"
    />
    <ul
      class="ant-skeleton-paragraph"
    >
      <li />
      <li />
      <li
        style="width: 61%;"
      />
    </ul>
  </div>
</div>
`;
