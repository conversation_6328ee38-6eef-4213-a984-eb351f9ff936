// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Statistic rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="ant-statistic ant-statistic-rtl css-var-root"
>
  <div
    class="ant-statistic-content"
  >
    <span
      class="ant-statistic-content-value"
    >
      <span
        class="ant-statistic-content-value-int"
      >
        0
      </span>
    </span>
  </div>
</div>
`;

exports[`Statistic support negative number 1`] = `
<div
  class="ant-statistic css-var-root"
>
  <div
    class="ant-statistic-header"
  >
    <div
      class="ant-statistic-title"
    >
      Account Balance (CNY)
    </div>
  </div>
  <div
    class="ant-statistic-content"
  >
    <span
      class="ant-statistic-content-value"
    >
      <span
        class="ant-statistic-content-value-int"
      >
        -112,893
      </span>
      <span
        class="ant-statistic-content-value-decimal"
      >
        .12
      </span>
    </span>
  </div>
</div>
`;
