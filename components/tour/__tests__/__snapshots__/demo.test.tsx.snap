// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/tour/demo/actions-render.tsx correctly 1`] = `
Array [
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Begin Tour
    </span>
  </button>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        type="button"
      >
        <span>
          Upload
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Save
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>,
]
`;

exports[`renders components/tour/demo/basic.tsx correctly 1`] = `
Array [
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Begin Tour
    </span>
  </button>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        type="button"
      >
        <span>
          Upload
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Save
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>,
]
`;

exports[`renders components/tour/demo/gap.tsx correctly 1`] = `
<div>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Begin Tour
    </span>
  </button>
  <div
    class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
    style="display:flex;margin-top:12px"
  >
    <div
      class="ant-space-item"
    >
      <div
        class="ant-row css-var-test-id"
      >
        <div
          class="ant-col ant-col-6 css-var-test-id"
        >
          <span
            class="ant-typography css-var-test-id"
          >
            Radius:
          </span>
        </div>
        <div
          class="ant-col ant-col-12 css-var-test-id"
        >
          <div
            class="ant-slider css-var-test-id ant-slider-horizontal"
          >
            <div
              class="ant-slider-rail"
            />
            <div
              class="ant-slider-track"
              style="left:0%;width:8%"
            />
            <div
              class="ant-slider-step"
            />
            <div
              aria-describedby="test-id"
              aria-disabled="false"
              aria-orientation="horizontal"
              aria-valuemax="100"
              aria-valuemin="0"
              aria-valuenow="8"
              class="ant-slider-handle"
              role="slider"
              style="left:8%;transform:translateX(-50%)"
              tabindex="0"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-space-item"
    >
      <div
        class="ant-row css-var-test-id"
      >
        <div
          class="ant-col ant-col-6 css-var-test-id"
        >
          <span
            class="ant-typography css-var-test-id"
          >
             offset:
          </span>
        </div>
        <div
          class="ant-col ant-col-12 css-var-test-id"
        >
          <div
            class="ant-slider css-var-test-id ant-slider-horizontal"
          >
            <div
              class="ant-slider-rail"
            />
            <div
              class="ant-slider-track"
              style="left:0%;width:4%"
            />
            <div
              class="ant-slider-step"
            />
            <div
              aria-describedby="test-id"
              aria-disabled="false"
              aria-orientation="horizontal"
              aria-valuemax="50"
              aria-valuemin="0"
              aria-valuenow="2"
              class="ant-slider-handle"
              role="slider"
              style="left:4%;transform:translateX(-50%)"
              tabindex="0"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-space-item"
    >
      <div
        class="ant-row css-var-test-id"
      >
        <div
          class="ant-col ant-col-6 css-var-test-id"
        >
          <span
            class="ant-typography css-var-test-id"
          >
            Horizontal offset:
          </span>
        </div>
        <div
          class="ant-col ant-col-12 css-var-test-id"
        >
          <div
            class="ant-slider css-var-test-id ant-slider-horizontal"
          >
            <div
              class="ant-slider-rail"
            />
            <div
              class="ant-slider-track"
              style="left:0%;width:4%"
            />
            <div
              class="ant-slider-step"
            />
            <div
              aria-describedby="test-id"
              aria-disabled="false"
              aria-orientation="horizontal"
              aria-valuemax="50"
              aria-valuemin="0"
              aria-valuenow="2"
              class="ant-slider-handle"
              role="slider"
              style="left:4%;transform:translateX(-50%)"
              tabindex="0"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-space-item"
    >
      <div
        class="ant-row css-var-test-id"
      >
        <div
          class="ant-col ant-col-6 css-var-test-id"
        >
          <span
            class="ant-typography css-var-test-id"
          >
            Vertical offset:
          </span>
        </div>
        <div
          class="ant-col ant-col-12 css-var-test-id"
        >
          <div
            class="ant-slider css-var-test-id ant-slider-horizontal"
          >
            <div
              class="ant-slider-rail"
            />
            <div
              class="ant-slider-track"
              style="left:0%;width:4%"
            />
            <div
              class="ant-slider-step"
            />
            <div
              aria-describedby="test-id"
              aria-disabled="false"
              aria-orientation="horizontal"
              aria-valuemax="50"
              aria-valuemin="0"
              aria-valuenow="2"
              class="ant-slider-handle"
              role="slider"
              style="left:4%;transform:translateX(-50%)"
              tabindex="0"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/tour/demo/indicator.tsx correctly 1`] = `
Array [
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Begin Tour
    </span>
  </button>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        type="button"
      >
        <span>
          Upload
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Save
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>,
]
`;

exports[`renders components/tour/demo/mask.tsx correctly 1`] = `
Array [
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Begin Tour
    </span>
  </button>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        type="button"
      >
        <span>
          Upload
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Save
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>,
]
`;

exports[`renders components/tour/demo/non-modal.tsx correctly 1`] = `
Array [
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Begin non-modal Tour
    </span>
  </button>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  >
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        type="button"
      >
        <span>
          Upload
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Save
        </span>
      </button>
    </div>
    <div
      class="ant-space-item"
    >
      <button
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
        </span>
      </button>
    </div>
  </div>,
]
`;

exports[`renders components/tour/demo/placement.tsx correctly 1`] = `
<button
  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
  type="button"
>
  <span>
    Begin Tour
  </span>
</button>
`;

exports[`renders components/tour/demo/render-panel.tsx correctly 1`] = `
<div
  style="display:flex;flex-direction:column;row-gap:16px;background:rgba(50,0,0,0.65);padding:8px"
>
  <div
    class="ant-tour ant-tour-pure ant-tour-placement-top ant-tour-pure css-var-test-id"
  >
    <div
      class="ant-tour-arrow"
    />
    <div
      class="ant-tour-content"
    >
      <div
        class="ant-tour-inner"
        role="tooltip"
      >
        <div
          class="ant-tour-pannel"
        >
          <div
            class="ant-tour-section"
          >
            <button
              aria-label="Close"
              class="ant-tour-close"
              type="button"
            >
              <span
                aria-label="Close"
                class="anticon anticon-close ant-tour-close-icon"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                  />
                </svg>
              </span>
            </button>
            <div
              class="ant-tour-header"
            >
              <div
                class="ant-tour-title"
              >
                Hello World!
              </div>
            </div>
            <div
              class="ant-tour-description"
            >
              Hello World?!
            </div>
            <div
              class="ant-tour-footer"
            >
              <div
                class="ant-tour-indicators"
              >
                <span
                  class="ant-tour-indicator-active ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
              </div>
              <div
                class="ant-tour-actions"
              >
                <button
                  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-tour-next-btn"
                  type="button"
                >
                  <span>
                    Next
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-tour ant-tour-pure ant-tour-placement-top ant-tour-pure css-var-test-id"
  >
    <div
      class="ant-tour-arrow"
    />
    <div
      class="ant-tour-content"
    >
      <div
        class="ant-tour-inner"
        role="tooltip"
      >
        <div
          class="ant-tour-pannel"
        >
          <div
            class="ant-tour-section"
          >
            <button
              aria-label="Close"
              class="ant-tour-close"
              type="button"
            >
              <span
                aria-label="Close"
                class="anticon anticon-close ant-tour-close-icon"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                  />
                </svg>
              </span>
            </button>
            <div
              class="ant-tour-cover"
            >
              <img
                alt="tour.png"
                src="https://user-images.githubusercontent.com/5378891/197385811-55df8480-7ff4-44bd-9d43-a7dade598d70.png"
              />
            </div>
            <div
              class="ant-tour-header"
            >
              <div
                class="ant-tour-title"
              >
                Hello World!
              </div>
            </div>
            <div
              class="ant-tour-description"
            >
              Hello World?!
            </div>
            <div
              class="ant-tour-footer"
            >
              <div
                class="ant-tour-indicators"
              >
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator-active ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
              </div>
              <div
                class="ant-tour-actions"
              >
                <button
                  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm ant-tour-prev-btn"
                  type="button"
                >
                  <span>
                    Previous
                  </span>
                </button>
                <button
                  class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm ant-tour-next-btn"
                  type="button"
                >
                  <span>
                    Next
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-tour ant-tour-pure ant-tour-placement-top ant-tour-pure ant-tour-primary css-var-test-id"
  >
    <div
      class="ant-tour-arrow"
    />
    <div
      class="ant-tour-content"
    >
      <div
        class="ant-tour-inner"
        role="tooltip"
      >
        <div
          class="ant-tour-pannel"
        >
          <div
            class="ant-tour-section"
          >
            <button
              aria-label="Close"
              class="ant-tour-close"
              type="button"
            >
              <span
                aria-label="Close"
                class="anticon anticon-close ant-tour-close-icon"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                  />
                </svg>
              </span>
            </button>
            <div
              class="ant-tour-header"
            >
              <div
                class="ant-tour-title"
              >
                Hello World!
              </div>
            </div>
            <div
              class="ant-tour-description"
            >
              Hello World?!
            </div>
            <div
              class="ant-tour-footer"
            >
              <div
                class="ant-tour-indicators"
              >
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator"
                />
                <span
                  class="ant-tour-indicator-active ant-tour-indicator"
                />
              </div>
              <div
                class="ant-tour-actions"
              >
                <button
                  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm ant-btn-background-ghost ant-tour-prev-btn"
                  type="button"
                >
                  <span>
                    Previous
                  </span>
                </button>
                <button
                  class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm ant-tour-next-btn"
                  type="button"
                >
                  <span>
                    Finish
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
