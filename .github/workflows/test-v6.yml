# Origin Source
# https://github.com/ant-design/ant-design/blob/79f566b7f8abb1012ef55b0d2793bfdf5595b85d/.github/workflows/test.yml
name: ✅ test v6

on:
  push:
    branches: [next]
  pull_request:
    branches: [next]

# Cancel prev CI if new commit come
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - run: bun run lint

  ################################ Test ################################
  test-react-legacy:
    name: test-react-legacy
    strategy:
      matrix:
        react: ['18']
        shard: [1/2, 2/2]
    env:
      REACT: ${{ matrix.react }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - name: install react 18
        if: ${{ matrix.react == '18' }}
        run: bun run bun-install-react-18
      # dom test
      - name: dom test
        run: bun run test -- --maxWorkers=2 --shard=${{matrix.shard}}

  test-node:
    name: test-node
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - run: bun run test:node

  test-react-latest:
    name: test-react-latest
    strategy:
      matrix:
        module: [dom]
        shard: [1/2, 2/2]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - uses: oven-sh/setup-bun@v2
      - run: bun install

      # dom test
      - name: dom test
        run: bun run test -- --maxWorkers=2 --shard=${{matrix.shard}} --coverage

      - name: persist coverages
        run: |
          mkdir persist-coverage
          mv coverage/coverage-final.json persist-coverage/react-test-${{matrix.module}}-${{strategy.job-index}}.json

      - uses: actions/upload-artifact@v4
        name: upload coverages
        with:
          name: coverage-artifacts-${{ matrix.module }}-${{ strategy.job-index }}
          path: persist-coverage/

  test-react-latest-dist:
    name: test-react-latest-dist
    strategy:
      matrix:
        module: [dist, dist-min]
        shard: [1/2, 2/2]
    runs-on: ubuntu-latest
    needs: build
    steps:
      - uses: actions/checkout@v5
      - uses: oven-sh/setup-bun@v2
      - run: bun install

      - name: restore cache from dist
        uses: actions/cache@v4
        with:
          path: dist
          key: dist-${{ github.sha }}

      - name: dist-min test
        if: ${{ matrix.module == 'dist-min' }}
        run: bun run test
        env:
          LIB_DIR: dist-min

      - name: dist test
        if: ${{ matrix.module == 'dist' }}
        run: bun run test
        env:
          LIB_DIR: dist

  ############################ Test Coverage ###########################
  upload-test-coverage:
    name: test-coverage
    runs-on: ubuntu-latest
    needs: test-react-latest
    steps:
      - uses: actions/checkout@v5
      - uses: oven-sh/setup-bun@v2
      - run: bun install

      - uses: actions/download-artifact@v5
        with:
          pattern: coverage-artifacts-*
          merge-multiple: true
          path: persist-coverage
      - name: Merge Code Coverage
        run: |
          bunx nyc merge persist-coverage/ coverage/coverage-final.json
          bunx nyc report --reporter text -t coverage --report-dir coverage
          rm -rf persist-coverage
      - name: Upload coverage to codecov
        uses: codecov/codecov-action@v5
        with:
          # use own token to upload coverage reports
          token: ${{ secrets.CODECOV_TOKEN }}

  ########################### Compile & Test ###########################
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - uses: oven-sh/setup-bun@v2
      - run: bun install

      - name: cache lib
        uses: actions/cache@v4
        with:
          path: lib
          key: lib-${{ github.sha }}

      - name: cache es
        uses: actions/cache@v4
        with:
          path: es
          key: es-${{ github.sha }}

      - name: compile
        run: bun run compile

      - name: cache dist
        uses: actions/cache@v4
        with:
          path: dist
          key: dist-${{ github.sha }}

      - name: dist
        run: bun run dist
        env:
          NODE_OPTIONS: --max_old_space_size=4096
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
          CI: 1

      - name: check build files
        run: bun run test:dekko

      # Artifact build files
      - uses: actions/upload-artifact@v4
        if: github.event_name == 'push' && github.ref == 'refs/heads/next'
        with:
          name: build artifacts
          path: |
            dist
            locale
            es
            lib

      - name: zip builds
        if: github.repository == 'ant-design/ant-design' && github.event_name == 'push' && github.ref == 'refs/heads/next'
        env:
          ALI_OSS_AK_ID: ${{ secrets.ALI_OSS_AK_ID }}
          ALI_OSS_AK_SECRET: ${{ secrets.ALI_OSS_AK_SECRET }}
          HEAD_SHA: ${{ github.sha }}
        run: |
          zip -r oss-artifacts.zip dist locale es lib
          echo "🤖 Uploading"
          node scripts/visual-regression/upload.js ./oss-artifacts.zip --ref=$HEAD_SHA

  test-lib-es:
    name: test lib/es module
    runs-on: ubuntu-latest
    strategy:
      matrix:
        module: [lib, es]
        shard: [1/2, 2/2]
    steps:
      - uses: actions/checkout@v5
      - uses: oven-sh/setup-bun@v2
      - run: bun install

      - name: restore cache from ${{ matrix.module }}
        # lib only run in master branch not in pull request
        if: ${{ github.event_name != 'pull_request' || matrix.module != 'lib' }}
        uses: actions/cache@v4
        with:
          path: ${{ matrix.module }}
          key: ${{ matrix.module }}-${{ github.sha }}

      - name: compile
        # lib only run in master branch not in pull request
        if: ${{ github.event_name != 'pull_request' || matrix.module != 'lib' }}
        run: bun run compile

      - name: test
        # lib only run in master branch not in pull request
        if: ${{ github.event_name != 'pull_request' || matrix.module != 'lib' }}
        run: bun run test -- --maxWorkers=2 --shard=${{matrix.shard}}
        env:
          LIB_DIR: ${{ matrix.module }}
