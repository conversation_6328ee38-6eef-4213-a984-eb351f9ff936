# Build a cron job to create a umi project which use antd and dumi, and then build it.
name: Mock Project Build

on:
  workflow_dispatch:
  schedule:
    - cron: '*/30 * * * *'

# Cancel prev CI if new commit come
concurrency:
  group: unique
  cancel-in-progress: true

jobs:
  pr-check-ci:
    if: github.repository == 'ant-design/ant-design'
    runs-on: ubuntu-latest
    name: Build Project
    steps:
      - name: checkout
        uses: actions/checkout@v5

      - uses: oven-sh/setup-bun@v2

      - uses: actions/cache@v4
        with:
          path: ~tmpProj/yarn.lock
          key: primes-${{ runner.os }}-${{ github.run_id }}
          restore-keys: mock-proj-lock-file

      - name: Run Script
        run: bash ./scripts/ci-mock-project-build.sh

      ##################################################################
      ##                        Diff Lock File                        ##
      ##################################################################
      - name: Rename failed lock file
        if: ${{ failure() }}
        run: mv ~tmpProj/yarn.lock ~tmpProj/yarn.lock.failed

      - name: Download success lock file as `success.lock`
        if: ${{ failure() }}
        uses: actions/cache/restore@v4
        with:
          path: ~tmpProj/yarn.lock
          key: primes-${{ runner.os }}-${{ github.run_id }}
          restore-keys: mock-proj-lock-file

      - name: ls tmpProj
        if: ${{ failure() }}
        run: ls ~tmpProj

      - name: 🎨 Diff Report
        if: ${{ failure() }}
        run: bunx diff-yarn-lock --source=~tmpProj/yarn.lock --target=~tmpProj/yarn.lock.failed

      - uses: actions-cool/ci-notice@v1
        if: ${{ failure() }}
        with:
          notice-types: dingding
          # Exit directly with non-zero to trigger the failure logic of ci-notice.
          ci: |
            echo "❌ CI Mock Project Build Failed"
            exit 1
          dingding-token: ${{ secrets.DINGDING_BOT_COLLABORATOR_TOKEN }}
          notice-title: CI Mock Project Build Failed
