# Each PR will build preview site that help to check code is work as expect.

name: Preview Build

on:
  pull_request:
    types: [opened, synchronize, reopened]

# Cancel prev CI if new commit come
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  build-site:
    name: build preview
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - name: bun run site
        id: site
        run: bun run site
        env:
          NODE_OPTIONS: --max_old_space_size=4096
      - name: run e2e test
        run: bun run test:site
      - name: upload site artifact
        uses: actions/upload-artifact@v4
        with:
          name: site
          path: _site/
          retention-days: 5
      # Upload PR id for next workflow use
      - name: Save PR number
        if: ${{ always() }}
        run: echo ${{ github.event.number }} > ./pr-id.txt

      - name: Upload PR number
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: pr
          path: ./pr-id.txt
