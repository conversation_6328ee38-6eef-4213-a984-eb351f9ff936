name: 📦 Size Limit

on:
  pull_request:
    types: [opened, synchronize]

permissions:
  issues: write
  contents: read

jobs:
  size:
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v5
      - uses: oven-sh/setup-bun@v2
      - name: size-limit
        uses: ant-design/size-limit-action@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          package_manager: bun
          build_script: dist
        env:
          NODE_OPTIONS: --max_old_space_size=4096
          PRODUCTION_ONLY: 1
          CI_JOB_NUMBER: 1
