// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`component changelog match snapshot misc changelog snapshot 1`] = `
[
  "  - 🤖 导出 \`MappingAlgorithm\` 作为 Design Token 主题算法的类型。[#43953](https://github.com/ant-design/ant-design/pull/43953)",
  "- 🔥 组件 ComponentToken 支持配置 \`algorithm\` 参数，添加配置即可像全局 Token 一样由部分修改的 token 计算派生 token 的值并用于组件样式中。[#43810](https://github.com/ant-design/ant-design/pull/43810) [@MadCcc](https://github.com/MadCcc)",
  "- 💄 \`@ant-design/icons\` 优化了 CloseCircleFilled / CloseSquareFilled / CloseOutlined / CloseCircleOutlined / CloseSquareOutlined / ExportOutlined / ImportOutlined 等图标的设计。[824500](https://github.com/ant-design/ant-design-icons/commit/824500349894a87562f033dbdc5e3c5d301a2f5c)",
  "- 💄 修复和其他使用 \`@ant-design/cssinjs\` 的组件库混合使用，antd 的样式总是会插入在最前面，以避免加载顺序导致的样式覆盖问题。[#43847](https://github.com/ant-design/ant-design/pull/43847)",
  "- 🛠 解决 vite、rollup、meteor、microbundle 等构建工具中遇到的循环依赖问题，并增加相关的检测。[#42750](https://github.com/ant-design/ant-design/pull/42750)，感谢 [@jrr997](https://github.com/jrr997)、[@kiner-tang](https://github.com/kiner-tang) 和 [@MuxinFeng](https://github.com/MuxinFeng) 的贡献。",
  "- 🐞 杂项：修复样式特性支持检测时部分浏览器因为未重绘导致出现滚动条的问题。[#43358](https://github.com/ant-design/ant-design/pull/43358) [@LeeeeeeM](https://github.com/LeeeeeeM)",
  "- 💄 Design Token 将 \`colorLink\` 添加至 seed token 中, \`colorLinkHover\` 和 \`colorLinkActive\` 将会由 \`colorLink\` 计算得出。[#43183](https://github.com/ant-design/ant-design/pull/43183) [@MadCcc](https://github.com/MadCcc)",
  "- 🆕 Design Token 新增静态方法 \`getDesignToken\` 用于获取完整的主题 token。[#42723](https://github.com/ant-design/ant-design/pull/42723) [@MadCcc](https://github.com/MadCcc)",
  "- 💄 修复部分全局 Design Token 无法覆盖组件样式的问题。[#42535](https://github.com/ant-design/ant-design/pull/42535) [@MadCcc](https://github.com/MadCcc)",
  "- 🇱🇹 为 \`lt_LT\` 添加缺失的部分文案。[#42664](https://github.com/ant-design/ant-design/pull/42664) [@Digital-512](https://github.com/Digital-512)",
  "- 🛠 全局移除 \`rc-util/lib/Dom/addEventListener\` 引入的 \`addEventListener\` 方法，用原生代替。[#42464](https://github.com/ant-design/ant-design/pull/42464) [@li-jia-nan](https://github.com/li-jia-nan)",
  "- 🛠 优化 @ant-design/icons 的 bundle 体积大小。修复 TwoTone 类的图标色为 5.0 的新主色。[#42443](https://github.com/ant-design/ant-design/pull/42443)",
  "- 🌐 添加 \`mn_MN\` 中缺失的翻译。[#42512](https://github.com/ant-design/ant-design/pull/42512) [@ariunbatb](https://github.com/ariunbatb)",
  "- 🤖 组件 ComponentToken 名称规范化。[#42184](https://github.com/ant-design/ant-design/pull/42184)",
  "- 🐞 修复 \`@ant-design/cssinjs\` 中 CSS 属性值为 \`undefined\` 时 cssinjs 报错的问题。[#41896](https://github.com/ant-design/ant-design/pull/41896)",
  "- 🐞 杂项：箭头元素兼容旧版本不支持 \`clip-path: path()\` 的浏览器。 [#41872](https://github.com/ant-design/ant-design/pull/41872)",
  "- 🇩🇪 修复德语本地化文案。[#41780](https://github.com/ant-design/ant-design/pull/41780) [@aaarichter](https://github.com/aaarichter)",
  "- 🇩🇪 补充 \`de_DE\` 遗漏的国际化。[#41747](https://github.com/ant-design/ant-design/pull/41747) [@eldarcodes](https://github.com/eldarcodes)",
  "- 🛠 使用 \`useMemo\` 重构部分组件代码。[#41533](https://github.com/ant-design/ant-design/pull/41533) [#41550](https://github.com/ant-design/ant-design/pull/41550) [@li-jia-nan](https://github.com/li-jia-nan)",
  "  - 🇻🇳 修复越南语本地化文案。[#41320](https://github.com/ant-design/ant-design/pull/41320) [@trongtai37](https://github.com/trongtai37) [#41345](https://github.com/ant-design/ant-design/pull/41345) [@duypham90](https://github.com/duypham90)",
  "  - 🇲🇲 添加缅甸语本地化文案。[#41366](https://github.com/ant-design/ant-design/pull/41366) [@enson0131](https://github.com/enson0131)",
  "  - 🇸🇪 完善 \`sv_SE\` 语言缺失内容。[#41424](https://github.com/ant-design/ant-design/pull/41424) [@dhalenok](https://github.com/dhalenok)",
  "  - 🤖 优化 Design Token 的类型提示和说明。[#41297](https://github.com/ant-design/ant-design/pull/41297) [@arvinxx](https://github.com/arvinxx)",
  "- 🌐 更新韩语国际化，添加国际化阿姆哈拉语。[#41103](https://github.com/ant-design/ant-design/pull/41103) [@li-jia-nan](https://github.com/li-jia-nan)",
  "- 🛠 重命名 token 中的预设颜色，如 \`blue-1\` 变为 \`blue1\`，废弃原有的 token。[#41071](https://github.com/ant-design/ant-design/pull/41071)",
  "- 💄 Design Token 为组件聚焦时的 \`outline\` 提供新的 AliasToken \`lineWidthFocus\`。[#40840](https://github.com/ant-design/ant-design/pull/40840)",
  "- 🐞 杂项：修复部分组件箭头形状问题。[#40971](https://github.com/ant-design/ant-design/pull/40971)",
  "- 🛠 重写 \`useLocale\` 方法，对外暴露 \`localeCode\`。[#40884](https://github.com/ant-design/ant-design/pull/40884) [@li-jia-nan](https://github.com/li-jia-nan)",
  "- 🛠 重构：使用 \`useLocale\` 替换 LocaleReceiver 组件，并删除 LocaleReceiver 组件。[#40870](https://github.com/ant-design/ant-design/pull/40870) [@li-jia-nan](https://github.com/li-jia-nan)",
  "- 💄 Design Token 修改组件聚焦下的 \`outline\` 为默认 \`4px\`。[#40839](https://github.com/ant-design/ant-design/pull/40839)",
  "- 🇮🇷 增加了伊朗本地化。[#40895](https://github.com/ant-design/ant-design/pull/40895) [@majidsadr](https://github.com/majidsadr)",
  "  - 🇰🇷 更新韩国本地化文案。[#40716](https://github.com/ant-design/ant-design/pull/40716) [@owjs3901](https://github.com/owjs3901)",
  "  - 🇷🇺/🇺🇦 补全 \`ru_RU\` 和 \`uk_UA\` 文案。[#40656](https://github.com/ant-design/ant-design/pull/40656) [@eldarcodes](https://github.com/eldarcodes)",
  "- 🛠 重构水波纹视效，以支持多个水波纹同时触发了。[#39705](https://github.com/ant-design/ant-design/pull/39705) [@li-jia-nan](https://github.com/li-jia-nan)",
  "- 💄 Design Token 优化 \`boxShadow\` token 分级。[#40516](https://github.com/ant-design/ant-design/pull/40516)",
  "  - 🤖 杂项：新增 \`Breakpoint\` \`ThemeConfig\` \`GlobalToken\` 类型导出。[#40508](https://github.com/ant-design/ant-design/pull/40508) [@Kamahl19](https://github.com/Kamahl19)",
  "  - 🇮🇳 补全 \`ta_IN\` 文案。[#39936](https://github.com/ant-design/ant-design/pull/39936) [@KIRUBASHANKAR26](https://github.com/KIRUBASHANKAR26)",
  "- 💄 Design Token 优化 focus \`outline\` 计算逻辑，替换 \`lineWidth\` 为 \`lineWidthBold\`。[#40291](https://github.com/ant-design/ant-design/pull/40291) [@simonpfish](https://github.com/simonpfish)",
  "- 💄 杂项：重写部分组件样式以兼容部分对 \`:not\` 支持不完全的旧版浏览器。[#40264](https://github.com/ant-design/ant-design/pull/40264)",
  "- 🌐 修复 \`pt_BR\` 缺失的国际化。[#40270](https://github.com/ant-design/ant-design/pull/40270) [@rafaelncarvalho](https://github.com/rafaelncarvalho)",
  "- 🐞 修复 locale 文件丢失的问题。[#40116](https://github.com/ant-design/ant-design/pull/40116)",
  "- 🐞 修复 \`rc-motion\` 部分组件动画闪烁的问题。[react-component/motion#39](https://github.com/react-component/motion/pull/39)",
  "- 🌐 增加缺失的泰米尔语翻译。[#39936](https://github.com/ant-design/ant-design/pull/39936) [@KIRUBASHANKAR26](https://github.com/KIRUBASHANKAR26)",
  "- 📖 官网主题编辑器添加主题上传功能。[#39621](https://github.com/ant-design/ant-design/pull/39621) [@BoyYangzai](https://github.com/BoyYangzai)",
  "- 📦 在构建流程中去掉对 IE 等旧版本浏览器的支持以减少包体积。[#38779](https://github.com/ant-design/ant-design/pull/38779)",
  "- 🐞 Design Token 修复组件字体错误问题。[#39806](https://github.com/ant-design/ant-design/pull/39806)",
  "  - 🤖 修复部分 Design Token 缺少类型提示的问题。[#39754](https://github.com/ant-design/ant-design/pull/39754)",
  "- 🛠 简化 lodash 方法引用。[#39599](https://github.com/ant-design/ant-design/pull/39599) [#39602](https://github.com/ant-design/ant-design/pull/39602)",
  "  - 🇧🇪 补全 \`fr_BE\` 文案。[#39415](https://github.com/ant-design/ant-design/pull/39415) [@azro352](https://github.com/azro352)",
  "  - 🇨🇦 补全 \`fr_CA\` 文案。[#39416](https://github.com/ant-design/ant-design/pull/39416) [@azro352](https://github.com/azro352)",
  "  - 🇪🇸 补全 \`eu_ES\` 文案。[#39371](https://github.com/ant-design/ant-design/pull/39371) [@Ian-Inizias](https://github.com/Ian-Inizias)",
  "- 🌐 修正 \`vi_VN\` 国际化描述。[#39279](https://github.com/ant-design/ant-design/pull/39279) [@nghiepdev](https://github.com/nghiepdev)",
  "- 🌐 修正 \`he_IL\` 国际化描述。[#39280](https://github.com/ant-design/ant-design/pull/39280) [@Ran-Sagy](https://github.com/Ran-Sagy)",
  "- 🐞 修复 \`@ant-design/cssinjs\` dev 下动态 hashId 导致的 ssr 注水失败的问题。[#39069](https://github.com/ant-design/ant-design/pull/39069)",
  "- 💄 Design Token 优化错误色的默认算法。[#38933](https://github.com/ant-design/ant-design/pull/38933)",
  "- 💄 修复 RTL 模式下的样式问题。[#38829](https://github.com/ant-design/ant-design/pull/38829) [@Wxh16144](https://github.com/Wxh16144)",
  "- 🐞 修复 \`reset.css\` 不会被打包的问题。[#38956](https://github.com/ant-design/ant-design/pull/38956) [@passerV](https://github.com/passerV)",
  "- 🛠 清除残留 \`Moment.js\` 依赖。[#38762](https://github.com/ant-design/ant-design/pull/38762)",
  "- 🛠 修复外部暴露类 \`CompoundedComponent\` 的组件的类型报错。[#38666](https://github.com/ant-design/ant-design/pull/38666) [@wangcch](https://github.com/wangcch)",
  "- 🛠 重新添加 \`lib\` 产物。[#38832](https://github.com/ant-design/ant-design/pull/38832) [@chunsch](https://github.com/chunsch)",
  "  - 🤖 Export Design Token \`MappingAlgorithm\` as type of theme algorithm. [#43953](https://github.com/ant-design/ant-design/pull/43953)",
  "- 🔥 Component Token support \`algorithm\` to calculate derivative tokens same as global. [#43810](https://github.com/ant-design/ant-design/pull/43810) [@MadCcc](https://github.com/MadCcc)",
  "- 🐞 Optimized the import method for \`@ant-design/icons\` to avoid importing all icons. [#43915](https://github.com/ant-design/ant-design/pull/43915) [@ssxenon01](https://github.com/ssxenon01)",
  "- 💄 Optimized \`@ant-design/icons\` the design of icons including CloseCircleFilled/CloseSquareFilled/CloseOutlined/CloseCircleOutlined/CloseSquareOutlined/ExportOutlined/ImportOutlined. [824500](https://github.com/ant-design/ant-design-icons/commit/824500349894a87562f033dbdc5e3c5d301a2f5c)",
  "- 💄 Fix when using with other component libraries that use \`@ant-design/cssinjs\`, antd styles will always be inserted at the top to avoid style override issues caused by loading order. [#43847](https://github.com/ant-design/ant-design/pull/43847)",
  "- 🛠 Resolved Circular dependency issue in vite, rollup, meteor and microbundle. [#42750](https://github.com/ant-design/ant-design/pull/42750). Thanks to [@jrr997](https://github.com/jrr997), [@kiner-tang](https://github.com/kiner-tang) and [@MuxinFeng](https://github.com/MuxinFeng) for their contributions.",
  "- 🐞 MISC: Fixed an issue where some browsers had scroll bars that were not redrawn when style feature support was detected. [#43358](https://github.com/ant-design/ant-design/pull/43358) [@LeeeeeeM](https://github.com/LeeeeeeM)",
  "- 💄 Design Token add \`colorLink\` to the seed token, and \`colorLinkHover\` and \`colorLinkActive\` will be calculated from colorLink. [#43183](https://github.com/ant-design/ant-design/pull/43183) [@MadCcc](https://github.com/MadCcc)",
  "  - 🛠 Improve BreadCrumb behavior when receiving a null title. [#43099](https://github.com/ant-design/ant-design/pull/43099) [@Asanio06](https://github.com/Asanio06)",
  "- 🆕 Add static function \`getDesignToken\` to access full Design Token. [#42723](https://github.com/ant-design/ant-design/pull/42723) [@MadCcc](https://github.com/MadCcc)",
  "  - 🛠 Migrate Timenline less variables. [#42491](https://github.com/ant-design/ant-design/pull/42491) [@jrr997](https://github.com/jrr997)",
  "- 💄 Fix Design Token that global token should be able to override component style. [#42535](https://github.com/ant-design/ant-design/pull/42535) [@MadCcc](https://github.com/MadCcc)",
  "- 🇱🇹 Add missing i18n for \`lt_LT\` locale. [#42664](https://github.com/ant-design/ant-design/pull/42664) [@Digital-512](https://github.com/Digital-512)",
  "- 🛠 Remove \`addEventListener\` from \`rc-util/lib/Dom/addEventListener\` and use native \`addEventListener\` instead. [#42464](https://github.com/ant-design/ant-design/pull/42464) [@li-jia-nan](https://github.com/li-jia-nan)",
  "- 🛠 Reduce 1KB @ant-design/icons bundle size. Fix TwoTone icon color to primary color of 5.0. [#42443](https://github.com/ant-design/ant-design/pull/42443)",
  "- 🌐 Add missing translation for \`mn_MN\`. [#42512](https://github.com/ant-design/ant-design/pull/42512) [@ariunbatb](https://github.com/ariunbatb)",
  "- 🤖 Component Token name canonicalization. [#42184](https://github.com/ant-design/ant-design/pull/42184)",
  "- 🐞 Fix \`@ant-design/cssinjs\` that cssinjs may crash if CSS value is \`undefined\`. [#41896](https://github.com/ant-design/ant-design/pull/41896)",
  "- 🐞 MISC: Arrow element support more old browsers which do not support \`clip-path: path()\`. [#41872](https://github.com/ant-design/ant-design/pull/41872)",
  "- 🇩🇪 Fix typo in German locale. [#41780](https://github.com/ant-design/ant-design/pull/41780) [@aaarichter](https://github.com/aaarichter)",
  "- 🇩🇪 Add missing translations for \`de_DE\`. [#41747](https://github.com/ant-design/ant-design/pull/41747) [@eldarcodes](https://github.com/eldarcodes)",
  "- 🛠 Refactor some components by using \`useMemo\`. [#41533](https://github.com/ant-design/ant-design/pull/41533) [#41550](https://github.com/ant-design/ant-design/pull/41550) [@li-jia-nan](https://github.com/li-jia-nan)",
  "  - 🇲🇲 Added Burmese locale. [#41366](https://github.com/ant-design/ant-design/pull/41366) [@enson0131](https://github.com/enson0131)",
  "  - 🇻🇳 Fix Vietnamese locale text. [#41320](https://github.com/ant-design/ant-design/pull/41320) [@trongtai37](https://github.com/trongtai37) [#41345](https://github.com/ant-design/ant-design/pull/41345) [@duypham90](https://github.com/duypham90)",
  "  - 🇸🇪 Add the missing content of \`sv_SE\` language. [#41424](https://github.com/ant-design/ant-design/pull/41424) [@dhalenok](https://github.com/dhalenok)",
  "  - 🤖 Improve Design Token most alias token meta info. [#41297](https://github.com/ant-design/ant-design/pull/41297) [@arvinxx](https://github.com/arvinxx)",
  "- 🌐 Update ko_KR、Added Amharic Language. [#41103](https://github.com/ant-design/ant-design/pull/41103) [@li-jia-nan](https://github.com/li-jia-nan)",
  "- 🛠 Rename preset colors in token, .e.g \`blue-1\` to \`blue1\`, and deprecate tokens before. [#41071](https://github.com/ant-design/ant-design/pull/41071)",
  "- 💄 Provide Design Token new AliasToken \`lineWidthFocus\` for \`outline-width\` of focused component. [#40840](https://github.com/ant-design/ant-design/pull/40840)",
  "- 🐞 MISC: Fix arrow shape in some components. [#40971](https://github.com/ant-design/ant-design/pull/40971)",
  "- 🛠 Rewrote the \`useLocale\` method and exposed \`localeCode\` to the public. [#40884](https://github.com/ant-design/ant-design/pull/40884) [@li-jia-nan](https://github.com/li-jia-nan)",
  "- 🛠 Refactored: replaced the LocaleReceiver component with \`useLocale\` and removed the LocaleReceiver component. [#40870](https://github.com/ant-design/ant-design/pull/40870) [@li-jia-nan](https://github.com/li-jia-nan)",
  "- 💄 Changed Design Token the component's focus \`outline\` to the default \`4px\`.[#40839](https://github.com/ant-design/ant-design/pull/40839)",
  "- 🇮🇷 Added Iranian localization. [#40895](https://github.com/ant-design/ant-design/pull/40895) [@majidsadr](https://github.com/majidsadr)",
  "  - 🇰🇷 Update ko_KR locale. [#40716](https://github.com/ant-design/ant-design/pull/40716) [@owjs3901](https://github.com/owjs3901)",
  "  - 🇷🇺/🇺🇦 add missing translations for ru_RU and uk_UA. [#40656](https://github.com/ant-design/ant-design/pull/40656) [@eldarcodes](https://github.com/eldarcodes)",
  "- 🛠 Refactored the water ripple visual effect to trigger multiple water ripples at the same time. [#39705](https://github.com/ant-design/ant-design/pull/39705) [@li-jia-nan](https://github.com/li-jia-nan)",
  "- 💄 Optimize Design Token \`boxShadow\` tokens. [#40516](https://github.com/ant-design/ant-design/pull/40516)",
  "  - 🤖 MISC: Add \`Breakpoint\` \`ThemeConfig\` \`GlobalToken\` type export. [#40508](https://github.com/ant-design/ant-design/pull/40508) [@Kamahl19](https://github.com/Kamahl19)",
  "  - 🇮🇳 Add \`ta_IN\` local. [#39936](https://github.com/ant-design/ant-design/pull/39936) [@KIRUBASHANKAR26](https://github.com/KIRUBASHANKAR26)",
  "- 💄 Optimize Design Token calculation logic of focus \`outline\`, replace \`lineWidth\` with \`lineWidthBold\`. [#40291](https://github.com/ant-design/ant-design/pull/40291) [@simonpfish](https://github.com/simonpfish)",
  "- 💄 MISC: Rewrite part component style to compatible the browser that not support concat \`:not\` selector. [#40264](https://github.com/ant-design/ant-design/pull/40264)",
  "- 🌐 Fix missing translation for \`pt_BR\`. [#40270](https://github.com/ant-design/ant-design/pull/40270) [@rafaelncarvalho](https://github.com/rafaelncarvalho)",
  "- 🐞 Fix missing locale file. [#40116](https://github.com/ant-design/ant-design/pull/40116)",
  "- 🐞 Fix \`rc-motion\` animation flicking in some components. [react-component/motion#39](https://github.com/react-component/motion/pull/39)",
  "- 🌐 Add missing ta_IN translations. [#39936](https://github.com/ant-design/ant-design/pull/39936) [@KIRUBASHANKAR26](https://github.com/KIRUBASHANKAR26)",
  "- 📖 Theme Editor supports uploading themes. [#39621](https://github.com/ant-design/ant-design/pull/39621) [@BoyYangzai](https://github.com/BoyYangzai)",
  "- 📦 Remove IE and other legacy browsers from browserslist to reduce bundle size.[#38779](https://github.com/ant-design/ant-design/pull/38779)",
  "- 🐞 Fix Design Token wrong \`font-family\` of components. [#39806](https://github.com/ant-design/ant-design/pull/39806)",
  "  - 🤖 Fix missing type defination for Design Token. [#39754](https://github.com/ant-design/ant-design/pull/39754)",
  "- 🛠 Simplified lodash method introduction. [#39599](https://github.com/ant-design/ant-design/pull/39599) [#39602](https://github.com/ant-design/ant-design/pull/39602)",
  "  - 🇧🇪 Add \`fr_BE\` locale. [#39415](https://github.com/ant-design/ant-design/pull/39415) [@azro352](https://github.com/azro352)",
  "  - 🇨🇦 Add \`fr_CA\` locale. [#39416](https://github.com/ant-design/ant-design/pull/39416) [@azro352](https://github.com/azro352)",
  "  - 🇪🇸 Add \`eu_ES\` locale. [#39371](https://github.com/ant-design/ant-design/pull/39371) [@Ian-Inizias](https://github.com/Ian-Inizias)",
  "- 🌐 Fix \`vi_VN\` i18n mistake. [#39279](https://github.com/ant-design/ant-design/pull/39279) [@nghiepdev](https://github.com/nghiepdev)",
  "- 🌐 Fix \`he_IL\` i18n mistake. [#39280](https://github.com/ant-design/ant-design/pull/39280) [@Ran-Sagy](https://github.com/Ran-Sagy)",
  "- 🐞 Fix \`@ant-design/cssinjs\` ssr warning in dev mode caused by dynamic hashId. [#39069](https://github.com/ant-design/ant-design/pull/39069)",
  "- 💄 Optimize Design Token default algorithm for error color. [#38933](https://github.com/ant-design/ant-design/pull/38933)",
  "- 💄 Optimize the style issue in RTL mode. [#38829](https://github.com/ant-design/ant-design/pull/38829) [@Wxh16144](https://github.com/Wxh16144)",
  "- 🐞 Fix that \`dist/reset.css\` may be dropped in production. [#38956](https://github.com/ant-design/ant-design/pull/38956) [@passerV](https://github.com/passerV)",
  "- 🛠 Remove \`Moment.js\` dependency. [#38762](https://github.com/ant-design/ant-design/pull/38762)",
  "- 🛠 Fix \`CompoundedComponent\` ts error. [#38666](https://github.com/ant-design/ant-design/pull/38666) [@wangcch](https://github.com/wangcch)",
  "- 🛠 Rollback \`lib\` in package. [#38832](https://github.com/ant-design/ant-design/pull/38832) [@chunsch](https://github.com/chunsch)",
]
`;
